<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Monitoring Dashboard - Secure Backend API</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1600px;
            margin: 2rem auto;
        }

        .dashboard-header {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .dashboard-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .dashboard-info h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
            color: var(--text-primary);
        }

        .dashboard-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .dashboard-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .refresh-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .refresh-indicator.active {
            color: var(--success-color);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .status-card.security::before {
            background: linear-gradient(90deg, var(--error-color), var(--warning-color));
        }

        .status-card.auth::before {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .status-card.sessions::before {
            background: linear-gradient(90deg, var(--success-color), var(--primary-color));
        }

        .status-card.users::before {
            background: linear-gradient(90deg, var(--info-color), var(--success-color));
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .status-info {
            flex: 1;
        }

        .status-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .status-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .status-icon.security {
            background: linear-gradient(135deg, var(--error-color), var(--warning-color));
        }

        .status-icon.auth {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        }

        .status-icon.sessions {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
        }

        .status-icon.users {
            background: linear-gradient(135deg, var(--info-color), var(--success-color));
        }

        .status-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .metric-item {
            text-align: center;
        }

        .metric-number {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .metric-number.critical {
            color: var(--error-color);
        }

        .metric-number.warning {
            color: var(--warning-color);
        }

        .metric-number.success {
            color: var(--success-color);
        }

        .metric-number.info {
            color: var(--info-color);
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .status-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.5rem;
            border-radius: 20px;
        }

        .status-trend.up {
            background: rgba(var(--error-color-rgb), 0.1);
            color: var(--error-color);
        }

        .status-trend.down {
            background: rgba(var(--success-color-rgb), 0.1);
            color: var(--success-color);
        }

        .status-trend.stable {
            background: rgba(var(--text-secondary-rgb), 0.1);
            color: var(--text-secondary);
        }

        .monitoring-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .monitoring-section {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .section-actions {
            display: flex;
            gap: 0.5rem;
        }

        .event-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .event-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
            font-size: 0.9rem;
        }

        .event-icon.critical {
            background: rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
        }

        .event-icon.warning {
            background: rgba(var(--warning-color-rgb), 0.2);
            color: var(--warning-color);
        }

        .event-icon.info {
            background: rgba(var(--info-color-rgb), 0.2);
            color: var(--info-color);
        }

        .event-icon.success {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .event-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .event-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .chart-placeholder {
            height: 300px;
            background: var(--hover-background);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .alert-banner {
            background: linear-gradient(135deg, var(--error-color), var(--warning-color));
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: pulse 2s infinite;
        }

        .alert-banner.hidden {
            display: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .alert-icon {
            font-size: 1.5rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .alert-description {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .time-range-selector {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .time-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .time-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .time-btn:hover {
            background: var(--hover-background);
        }

        @media (max-width: 1024px) {
            .monitoring-sections {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Security Monitoring Dashboard</h1>
                    <span class="subtitle">Real-time Security and Authentication Monitoring</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="dashboard-container">
                <!-- Dashboard Header -->
                <div class="dashboard-header">
                    <div class="dashboard-title">
                        <div class="dashboard-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="dashboard-info">
                            <h1>Security Monitoring Dashboard</h1>
                            <div class="dashboard-subtitle">Real-time monitoring of authentication and security events</div>
                        </div>
                    </div>
                    <div class="dashboard-controls">
                        <div class="refresh-indicator" id="refreshIndicator">
                            <i class="fas fa-sync"></i>
                            <span>Auto-refresh: ON</span>
                        </div>
                        <button class="btn btn-primary" onclick="toggleAutoRefresh()">
                            <i class="fas fa-pause" id="refreshIcon"></i>
                            <span id="refreshText">Pause</span>
                        </button>
                        <button class="btn btn-success" onclick="refreshDashboard()">
                            <i class="fas fa-sync"></i>
                            Refresh Now
                        </button>
                    </div>
                </div>

                <!-- Security Alert Banner -->
                <div class="alert-banner" id="alertBanner">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Security Alert Detected</div>
                        <div class="alert-description">Multiple failed login attempts detected from suspicious IP addresses</div>
                    </div>
                    <button class="btn btn-sm" onclick="dismissAlert()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-times"></i>
                        Dismiss
                    </button>
                </div>

                <!-- Status Overview Grid -->
                <div class="status-grid">
                    <!-- Security Status -->
                    <div class="status-card security">
                        <div class="status-header">
                            <div class="status-info">
                                <div class="status-title">Security Threats</div>
                                <div class="status-description">Active security incidents and threats</div>
                            </div>
                            <div class="status-icon security">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="status-metrics">
                            <div class="metric-item">
                                <div class="metric-number critical" id="criticalThreats">0</div>
                                <div class="metric-label">Critical</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number warning" id="warningThreats">0</div>
                                <div class="metric-label">Warnings</div>
                            </div>
                        </div>
                        <div class="status-trend up" id="securityTrend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15% from yesterday</span>
                        </div>
                    </div>

                    <!-- Authentication Status -->
                    <div class="status-card auth">
                        <div class="status-header">
                            <div class="status-info">
                                <div class="status-title">Authentication Events</div>
                                <div class="status-description">Login attempts and authentication status</div>
                            </div>
                            <div class="status-icon auth">
                                <i class="fas fa-key"></i>
                            </div>
                        </div>
                        <div class="status-metrics">
                            <div class="metric-item">
                                <div class="metric-number success" id="successfulLogins">0</div>
                                <div class="metric-label">Successful</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number critical" id="failedLogins">0</div>
                                <div class="metric-label">Failed</div>
                            </div>
                        </div>
                        <div class="status-trend down" id="authTrend">
                            <i class="fas fa-arrow-down"></i>
                            <span>-8% from yesterday</span>
                        </div>
                    </div>

                    <!-- Session Status -->
                    <div class="status-card sessions">
                        <div class="status-header">
                            <div class="status-info">
                                <div class="status-title">Active Sessions</div>
                                <div class="status-description">Current user sessions and activity</div>
                            </div>
                            <div class="status-icon sessions">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="status-metrics">
                            <div class="metric-item">
                                <div class="metric-number info" id="activeSessions">0</div>
                                <div class="metric-label">Active</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number warning" id="suspiciousSessions">0</div>
                                <div class="metric-label">Suspicious</div>
                            </div>
                        </div>
                        <div class="status-trend stable" id="sessionsTrend">
                            <i class="fas fa-minus"></i>
                            <span>Stable</span>
                        </div>
                    </div>

                    <!-- User Activity Status -->
                    <div class="status-card users">
                        <div class="status-header">
                            <div class="status-info">
                                <div class="status-title">User Activity</div>
                                <div class="status-description">User registrations and account status</div>
                            </div>
                            <div class="status-icon users">
                                <i class="fas fa-user-friends"></i>
                            </div>
                        </div>
                        <div class="status-metrics">
                            <div class="metric-item">
                                <div class="metric-number success" id="newUsers">0</div>
                                <div class="metric-label">New Users</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number critical" id="blockedUsers">0</div>
                                <div class="metric-label">Blocked</div>
                            </div>
                        </div>
                        <div class="status-trend up" id="usersTrend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+23% from yesterday</span>
                        </div>
                    </div>
                </div>

                <!-- Monitoring Sections -->
                <div class="monitoring-sections">
                    <!-- Security Events -->
                    <div class="monitoring-section">
                        <div class="section-header">
                            <div class="section-title">Recent Security Events</div>
                            <div class="section-actions">
                                <div class="time-range-selector">
                                    <button class="time-btn active" onclick="setTimeRange('1h')">1H</button>
                                    <button class="time-btn" onclick="setTimeRange('24h')">24H</button>
                                    <button class="time-btn" onclick="setTimeRange('7d')">7D</button>
                                </div>
                            </div>
                        </div>
                        <div class="event-list" id="securityEvents">
                            <!-- Security events will be populated here -->
                        </div>
                    </div>

                    <!-- Authentication Events -->
                    <div class="monitoring-section">
                        <div class="section-header">
                            <div class="section-title">Authentication Activity</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-secondary" onclick="exportAuthLogs()">
                                    <i class="fas fa-download"></i>
                                    Export
                                </button>
                            </div>
                        </div>
                        <div class="event-list" id="authEvents">
                            <!-- Authentication events will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="monitoring-sections">
                    <!-- Security Trends Chart -->
                    <div class="monitoring-section">
                        <div class="section-header">
                            <div class="section-title">Security Trends</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-info" onclick="toggleChartType('security')">
                                    <i class="fas fa-chart-bar"></i>
                                    Toggle View
                                </button>
                            </div>
                        </div>
                        <div class="chart-placeholder" id="securityChart">
                            <i class="fas fa-chart-line" style="margin-right: 0.5rem;"></i>
                            Security Trends Chart (24 Hours)
                        </div>
                    </div>

                    <!-- Authentication Patterns Chart -->
                    <div class="monitoring-section">
                        <div class="section-header">
                            <div class="section-title">Authentication Patterns</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-info" onclick="toggleChartType('auth')">
                                    <i class="fas fa-chart-pie"></i>
                                    Toggle View
                                </button>
                            </div>
                        </div>
                        <div class="chart-placeholder" id="authChart">
                            <i class="fas fa-chart-pie" style="margin-right: 0.5rem;"></i>
                            Authentication Patterns Chart
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="dashboardResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Security Monitoring Dashboard</p>
                    <p>Real-time security and authentication monitoring</p>
                </div>
                <div class="footer-links">
                    <a href="../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../assets/js/utils/api.js"></script>
    <script src="../assets/js/utils/auth.js"></script>
    <script src="../assets/js/utils/storage.js"></script>
    <script src="../assets/js/utils/notifications.js"></script>
    <script src="../assets/js/shared/components.js"></script>
    <script src="../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let dashboardData = {
            metrics: {
                criticalThreats: 3,
                warningThreats: 12,
                successfulLogins: 1247,
                failedLogins: 89,
                activeSessions: 234,
                suspiciousSessions: 7,
                newUsers: 45,
                blockedUsers: 12
            },
            securityEvents: [],
            authEvents: [],
            autoRefresh: true,
            refreshInterval: null,
            timeRange: '1h'
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load dashboard data
            await loadDashboardData();

            // Start auto-refresh
            startAutoRefresh();

            // Initialize event listeners
            initializeEventListeners();
        }

        // Dashboard Data Functions
        async function loadDashboardData() {
            window.notificationManager.info('Loading dashboard data...');

            try {
                const response = await window.apiClient.request('GET', '/monitoring/dashboard');

                if (response.success) {
                    updateDashboardData(response.data);
                    window.notificationManager.success('Dashboard data loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load dashboard data');
                }

                showResponse('dashboardResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock dashboard data
                loadMockDashboardData();
                window.notificationManager.success('Dashboard data loaded successfully (simulated)');

                showResponse('dashboardResponse', {
                    success: true,
                    data: dashboardData,
                    message: 'Mock dashboard data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockDashboardData() {
            // Generate mock security events
            const securityEventTypes = [
                { type: 'critical', icon: 'fa-exclamation-triangle', title: 'Brute Force Attack Detected', description: 'Multiple failed login attempts from IP *************' },
                { type: 'warning', icon: 'fa-shield-alt', title: 'Suspicious Activity', description: 'Unusual login pattern detected <NAME_EMAIL>' },
                { type: 'critical', icon: 'fa-ban', title: 'Account Lockout', description: 'Account locked due to multiple failed attempts' },
                { type: 'warning', icon: 'fa-eye', title: 'Privilege Escalation Attempt', description: 'User attempted to access admin resources' },
                { type: 'info', icon: 'fa-info-circle', title: 'Security Scan Completed', description: 'Automated security scan finished with 3 findings' }
            ];

            const authEventTypes = [
                { type: 'success', icon: 'fa-sign-in-alt', title: 'Successful Login', description: 'User logged in from Chrome on Windows' },
                { type: 'critical', icon: 'fa-times-circle', title: 'Failed Login', description: 'Invalid credentials provided' },
                { type: 'info', icon: 'fa-user-plus', title: 'New Registration', description: 'New user account created' },
                { type: 'warning', icon: 'fa-key', title: 'Password Reset', description: 'Password reset requested' },
                { type: 'success', icon: 'fa-mobile-alt', title: '2FA Enabled', description: 'Two-factor authentication activated' }
            ];

            // Generate recent events
            dashboardData.securityEvents = Array.from({ length: 8 }, (_, i) => {
                const eventType = securityEventTypes[Math.floor(Math.random() * securityEventTypes.length)];
                return {
                    id: `sec_${Date.now()}_${i}`,
                    ...eventType,
                    timestamp: new Date(Date.now() - Math.random() * 1000 * 60 * 60 * 24), // Random time in last 24h
                    source: `192.168.1.${Math.floor(Math.random() * 255)}`,
                    user: `user${Math.floor(Math.random() * 100)}@example.com`
                };
            });

            dashboardData.authEvents = Array.from({ length: 10 }, (_, i) => {
                const eventType = authEventTypes[Math.floor(Math.random() * authEventTypes.length)];
                return {
                    id: `auth_${Date.now()}_${i}`,
                    ...eventType,
                    timestamp: new Date(Date.now() - Math.random() * 1000 * 60 * 60 * 12), // Random time in last 12h
                    source: `192.168.1.${Math.floor(Math.random() * 255)}`,
                    user: `user${Math.floor(Math.random() * 100)}@example.com`
                };
            });

            updateDashboardDisplay();
        }

        function updateDashboardData(data) {
            if (data.metrics) dashboardData.metrics = { ...dashboardData.metrics, ...data.metrics };
            if (data.securityEvents) dashboardData.securityEvents = data.securityEvents;
            if (data.authEvents) dashboardData.authEvents = data.authEvents;

            updateDashboardDisplay();
        }

        function updateDashboardDisplay() {
            updateMetrics();
            updateSecurityEvents();
            updateAuthEvents();
        }

        function updateMetrics() {
            document.getElementById('criticalThreats').textContent = dashboardData.metrics.criticalThreats;
            document.getElementById('warningThreats').textContent = dashboardData.metrics.warningThreats;
            document.getElementById('successfulLogins').textContent = formatNumber(dashboardData.metrics.successfulLogins);
            document.getElementById('failedLogins').textContent = dashboardData.metrics.failedLogins;
            document.getElementById('activeSessions').textContent = dashboardData.metrics.activeSessions;
            document.getElementById('suspiciousSessions').textContent = dashboardData.metrics.suspiciousSessions;
            document.getElementById('newUsers').textContent = dashboardData.metrics.newUsers;
            document.getElementById('blockedUsers').textContent = dashboardData.metrics.blockedUsers;
        }

        function updateSecurityEvents() {
            const container = document.getElementById('securityEvents');

            if (dashboardData.securityEvents.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No security events in the selected time range.
                    </div>
                `;
                return;
            }

            const eventsHTML = dashboardData.securityEvents
                .sort((a, b) => b.timestamp - a.timestamp)
                .map(event => `
                    <div class="event-item">
                        <div class="event-icon ${event.type}">
                            <i class="fas ${event.icon}"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">${event.title}</div>
                            <div class="event-description">${event.description}</div>
                            <div class="event-meta">
                                <span><i class="fas fa-clock"></i> ${formatTimeAgo(event.timestamp)}</span>
                                <span><i class="fas fa-map-marker-alt"></i> ${event.source}</span>
                                <span><i class="fas fa-user"></i> ${event.user}</span>
                            </div>
                        </div>
                    </div>
                `).join('');

            container.innerHTML = eventsHTML;
        }

        function updateAuthEvents() {
            const container = document.getElementById('authEvents');

            if (dashboardData.authEvents.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No authentication events in the selected time range.
                    </div>
                `;
                return;
            }

            const eventsHTML = dashboardData.authEvents
                .sort((a, b) => b.timestamp - a.timestamp)
                .map(event => `
                    <div class="event-item">
                        <div class="event-icon ${event.type}">
                            <i class="fas ${event.icon}"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">${event.title}</div>
                            <div class="event-description">${event.description}</div>
                            <div class="event-meta">
                                <span><i class="fas fa-clock"></i> ${formatTimeAgo(event.timestamp)}</span>
                                <span><i class="fas fa-map-marker-alt"></i> ${event.source}</span>
                                <span><i class="fas fa-user"></i> ${event.user}</span>
                            </div>
                        </div>
                    </div>
                `).join('');

            container.innerHTML = eventsHTML;
        }

        // Dashboard Control Functions
        function toggleAutoRefresh() {
            dashboardData.autoRefresh = !dashboardData.autoRefresh;

            const refreshIcon = document.getElementById('refreshIcon');
            const refreshText = document.getElementById('refreshText');
            const refreshIndicator = document.getElementById('refreshIndicator');

            if (dashboardData.autoRefresh) {
                refreshIcon.className = 'fas fa-pause';
                refreshText.textContent = 'Pause';
                refreshIndicator.classList.add('active');
                refreshIndicator.querySelector('span').textContent = 'Auto-refresh: ON';
                startAutoRefresh();
                window.notificationManager.success('Auto-refresh enabled');
            } else {
                refreshIcon.className = 'fas fa-play';
                refreshText.textContent = 'Resume';
                refreshIndicator.classList.remove('active');
                refreshIndicator.querySelector('span').textContent = 'Auto-refresh: OFF';
                stopAutoRefresh();
                window.notificationManager.info('Auto-refresh disabled');
            }
        }

        function startAutoRefresh() {
            if (dashboardData.refreshInterval) {
                clearInterval(dashboardData.refreshInterval);
            }

            if (dashboardData.autoRefresh) {
                dashboardData.refreshInterval = setInterval(() => {
                    refreshDashboard(true);
                }, 30000); // Refresh every 30 seconds
            }
        }

        function stopAutoRefresh() {
            if (dashboardData.refreshInterval) {
                clearInterval(dashboardData.refreshInterval);
                dashboardData.refreshInterval = null;
            }
        }

        async function refreshDashboard(silent = false) {
            if (!silent) {
                window.notificationManager.info('Refreshing dashboard...');
            }

            // Animate refresh indicator
            const refreshIndicator = document.getElementById('refreshIndicator');
            const icon = refreshIndicator.querySelector('i');
            icon.classList.add('fa-spin');

            try {
                await loadDashboardData();

                if (!silent) {
                    window.notificationManager.success('Dashboard refreshed successfully');
                }
            } catch (error) {
                if (!silent) {
                    window.notificationManager.error('Failed to refresh dashboard');
                }
            } finally {
                // Stop animation
                setTimeout(() => {
                    icon.classList.remove('fa-spin');
                }, 1000);
            }
        }

        function setTimeRange(range) {
            dashboardData.timeRange = range;

            // Update active button
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Refresh data for new time range
            refreshDashboard();

            window.notificationManager.info(`Time range set to ${range}`);
        }

        function dismissAlert() {
            document.getElementById('alertBanner').classList.add('hidden');
            window.notificationManager.success('Alert dismissed');
        }

        // Export Functions
        function exportAuthLogs() {
            window.notificationManager.info('Exporting authentication logs...');

            const exportData = {
                timestamp: new Date().toISOString(),
                timeRange: dashboardData.timeRange,
                totalEvents: dashboardData.authEvents.length,
                events: dashboardData.authEvents.map(event => ({
                    ...event,
                    timestamp: event.timestamp.toISOString()
                }))
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `auth-logs-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Authentication logs exported successfully');
        }

        function toggleChartType(chartType) {
            const chartElement = document.getElementById(`${chartType}Chart`);
            const currentIcon = chartElement.querySelector('i');

            if (currentIcon.classList.contains('fa-chart-line')) {
                currentIcon.className = 'fas fa-chart-bar';
                chartElement.innerHTML = `<i class="fas fa-chart-bar" style="margin-right: 0.5rem;"></i>${chartType === 'security' ? 'Security Threats Bar Chart' : 'Authentication Methods Bar Chart'}`;
            } else if (currentIcon.classList.contains('fa-chart-bar')) {
                currentIcon.className = 'fas fa-chart-pie';
                chartElement.innerHTML = `<i class="fas fa-chart-pie" style="margin-right: 0.5rem;"></i>${chartType === 'security' ? 'Security Distribution Pie Chart' : 'Authentication Success Rate Pie Chart'}`;
            } else {
                currentIcon.className = 'fas fa-chart-line';
                chartElement.innerHTML = `<i class="fas fa-chart-line" style="margin-right: 0.5rem;"></i>${chartType === 'security' ? 'Security Trends Line Chart' : 'Authentication Patterns Line Chart'}`;
            }

            window.notificationManager.info(`${chartType} chart view toggled`);
        }

        // Event Listeners
        function initializeEventListeners() {
            // Handle page visibility change to pause/resume auto-refresh
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    stopAutoRefresh();
                } else if (dashboardData.autoRefresh) {
                    startAutoRefresh();
                }
            });

            // Handle window beforeunload to cleanup intervals
            window.addEventListener('beforeunload', function() {
                stopAutoRefresh();
            });
        }

        // Utility Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }

        // Simulate real-time updates
        function simulateRealTimeUpdates() {
            setInterval(() => {
                if (dashboardData.autoRefresh && !document.hidden) {
                    // Randomly update some metrics
                    if (Math.random() < 0.3) {
                        dashboardData.metrics.activeSessions += Math.floor(Math.random() * 5) - 2;
                        dashboardData.metrics.activeSessions = Math.max(0, dashboardData.metrics.activeSessions);
                    }

                    if (Math.random() < 0.2) {
                        dashboardData.metrics.failedLogins += Math.floor(Math.random() * 3);
                    }

                    if (Math.random() < 0.1) {
                        dashboardData.metrics.criticalThreats += Math.floor(Math.random() * 2);
                    }

                    updateMetrics();
                }
            }, 5000); // Update every 5 seconds
        }

        // Start real-time simulation
        setTimeout(() => {
            simulateRealTimeUpdates();
        }, 2000);
    </script>
</body>
</html>
