<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Backend API - Testing Interface</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Secure Backend API</h1>
                    <span class="subtitle">Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <h2>Welcome to the Secure Backend Testing Interface</h2>
                <p>This comprehensive testing interface allows you to interact with all backend API features including authentication, MFA, session management, user administration, and security monitoring.</p>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" id="quickLoginBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        Quick Login
                    </button>
                    <button class="btn btn-secondary" id="checkServerHealthBtn">
                        <i class="fas fa-heartbeat"></i>
                        Check Server Health
                    </button>
                    <button class="btn btn-info" id="viewApiDocsBtn">
                        <i class="fas fa-book"></i>
                        API Documentation
                    </button>
                </div>
            </section>

            <!-- Feature Categories -->
            <section class="features-grid">
                <h2>API Feature Categories</h2>
                <div class="grid">
                    <!-- Authentication -->
                    <div class="feature-card" data-category="authentication">
                        <div class="card-header">
                            <i class="fas fa-key"></i>
                            <h3>Authentication</h3>
                        </div>
                        <div class="card-content">
                            <p>Test user registration, login, password reset, email verification, and profile management.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    15+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-shield-alt"></i>
                                    JWT + OAuth
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="authentication">
                                <i class="fas fa-arrow-right"></i>
                                Test Authentication
                            </button>
                        </div>
                    </div>

                    <!-- Multi-Factor Authentication -->
                    <div class="feature-card" data-category="mfa">
                        <div class="card-header">
                            <i class="fas fa-mobile-alt"></i>
                            <h3>Multi-Factor Authentication</h3>
                        </div>
                        <div class="card-content">
                            <p>Test TOTP setup, QR code generation, backup codes, and MFA verification flows.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    8 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-qrcode"></i>
                                    TOTP + Backup
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="mfa">
                                <i class="fas fa-arrow-right"></i>
                                Test MFA
                            </button>
                        </div>
                    </div>

                    <!-- Session Management -->
                    <div class="feature-card" data-category="sessions">
                        <div class="card-header">
                            <i class="fas fa-desktop"></i>
                            <h3>Session Management</h3>
                        </div>
                        <div class="card-content">
                            <p>Test device fingerprinting, session tracking, remote termination, and activity monitoring.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    7 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-fingerprint"></i>
                                    Device Tracking
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="sessions">
                                <i class="fas fa-arrow-right"></i>
                                Test Sessions
                            </button>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="feature-card" data-category="user-management">
                        <div class="card-header">
                            <i class="fas fa-users-cog"></i>
                            <h3>User Management</h3>
                        </div>
                        <div class="card-content">
                            <p>Test admin functions including user listing, role management, and user administration.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    10+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-user-shield"></i>
                                    Admin Only
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="user-management">
                                <i class="fas fa-arrow-right"></i>
                                Test User Mgmt
                            </button>
                        </div>
                    </div>

                    <!-- Security Features -->
                    <div class="feature-card" data-category="security">
                        <div class="card-header">
                            <i class="fas fa-shield-virus"></i>
                            <h3>Security Features</h3>
                        </div>
                        <div class="card-content">
                            <p>Test rate limiting, DDoS protection, threat monitoring, and security analytics.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    5+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-chart-line"></i>
                                    Real-time
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="security">
                                <i class="fas fa-arrow-right"></i>
                                Test Security
                            </button>
                        </div>
                    </div>

                    <!-- OAuth Integration -->
                    <div class="feature-card" data-category="oauth">
                        <div class="card-header">
                            <i class="fab fa-google"></i>
                            <h3>OAuth Integration</h3>
                        </div>
                        <div class="card-content">
                            <p>Test OAuth flows with Google, Facebook, and GitHub authentication providers.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    8 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-link"></i>
                                    3 Providers
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="oauth">
                                <i class="fas fa-arrow-right"></i>
                                Test OAuth
                            </button>
                        </div>
                    </div>

                    <!-- Email Verification -->
                    <div class="feature-card" data-category="email-verification">
                        <div class="card-header">
                            <i class="fas fa-envelope-circle-check"></i>
                            <h3>Email Verification</h3>
                        </div>
                        <div class="card-content">
                            <p>Test email verification requests and token validation processes.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    2 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-envelope"></i>
                                    Email Flow
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="email-verification">
                                <i class="fas fa-arrow-right"></i>
                                Test Email
                            </button>
                        </div>
                    </div>

                    <!-- Security Monitoring Dashboard -->
                    <div class="feature-card" data-category="monitoring">
                        <div class="card-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Security Monitoring Dashboard</h3>
                        </div>
                        <div class="card-content">
                            <p>Real-time monitoring of authentication events, security threats, and system activity.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-shield-alt"></i>
                                    Live Monitoring
                                </span>
                                <span class="stat">
                                    <i class="fas fa-chart-bar"></i>
                                    Analytics
                                </span>
                                <span class="stat">
                                    <i class="fas fa-bell"></i>
                                    Alerts
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" id="navigateToMonitoringBtn">
                                <i class="fas fa-arrow-right"></i>
                                Open Dashboard
                            </button>
                        </div>
                    </div>

                    <!-- API Testing Tools -->
                    <div class="feature-card" data-category="tools">
                        <div class="card-header">
                            <i class="fas fa-tools"></i>
                            <h3>Testing Tools</h3>
                        </div>
                        <div class="card-content">
                            <p>Advanced testing utilities, request logging, and API exploration tools.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-bug"></i>
                                    Debug Tools
                                </span>
                                <span class="stat">
                                    <i class="fas fa-history"></i>
                                    Request Log
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary navigate-btn" data-feature="tools">
                                <i class="fas fa-arrow-right"></i>
                                Open Tools
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="recent-activity">
                <h2>Recent API Activity</h2>
                <div class="activity-log" id="activityLog">
                    <div class="activity-item">
                        <i class="fas fa-info-circle"></i>
                        <span class="activity-text">Welcome! Start by checking server health or testing authentication.</span>
                        <span class="activity-time">Just now</span>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Secure Backend API Testing Interface</p>
                    <p>Built for comprehensive API testing and development</p>
                </div>
                <div class="footer-links">
                    <a href="docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="http://localhost:3000/health" target="_blank">
                        <i class="fas fa-heartbeat"></i>
                        Health Check
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/js/utils/api.js"></script>
    <script src="assets/js/utils/auth.js"></script>
    <script src="assets/js/utils/storage.js"></script>
    <script src="assets/js/utils/notifications.js"></script>
    <script src="assets/js/shared/components.js"></script>
    <script src="assets/js/shared/constants.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
