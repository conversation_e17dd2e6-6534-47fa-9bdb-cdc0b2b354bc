<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DDoS Protection Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .ddos-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .protection-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .status-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .status-icon.protected {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
        }

        .status-icon.vulnerable {
            background: linear-gradient(135deg, var(--error-color), var(--warning-color));
        }

        .status-info {
            flex: 1;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .status-metrics {
            display: flex;
            gap: 2rem;
        }

        .metric-item {
            text-align: center;
        }

        .metric-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .test-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .test-section {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .attack-simulator {
            margin-bottom: 2rem;
        }

        .attack-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .control-input {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .attack-progress {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: none;
        }

        .attack-progress.active {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .protection-logs {
            max-height: 300px;
            overflow-y: auto;
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
        }

        .log-entry {
            display: flex;
            align-items: flex-start;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
            font-family: monospace;
            font-size: 0.9rem;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-timestamp {
            color: var(--text-secondary);
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .log-level {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .log-level.info {
            background: var(--info-color);
            color: white;
        }

        .log-level.warning {
            background: var(--warning-color);
            color: white;
        }

        .log-level.error {
            background: var(--error-color);
            color: white;
        }

        .log-level.success {
            background: var(--success-color);
            color: white;
        }

        .log-message {
            flex: 1;
            color: var(--text-primary);
        }

        .mitigation-panel {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .mitigation-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .mitigation-option {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mitigation-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .mitigation-option.active {
            border-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.1);
        }

        .option-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .option-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-value.blocked {
            color: var(--error-color);
        }

        .stat-value.allowed {
            color: var(--success-color);
        }

        .stat-value.suspicious {
            color: var(--warning-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .test-sections {
                grid-template-columns: 1fr;
            }

            .protection-status {
                flex-direction: column;
                text-align: center;
            }

            .status-metrics {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-virus"></i>
                    <h1>DDoS Protection Testing</h1>
                    <span class="subtitle">Distributed Denial of Service Protection Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="ddos-container">
                <!-- Protection Status -->
                <div class="protection-status">
                    <div class="status-icon protected" id="statusIcon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="status-info">
                        <div class="status-title" id="statusTitle">DDoS Protection Active</div>
                        <div class="status-description" id="statusDescription">
                            Advanced DDoS protection is currently active and monitoring all incoming traffic.
                        </div>
                        <div class="status-metrics">
                            <div class="metric-item">
                                <div class="metric-number" id="requestsPerSecond">0</div>
                                <div class="metric-label">Requests/sec</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number" id="blockedRequests">0</div>
                                <div class="metric-label">Blocked</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number" id="suspiciousIPs">0</div>
                                <div class="metric-label">Suspicious IPs</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value blocked" id="totalBlocked">0</div>
                        <div class="stat-label">Total Blocked Requests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value allowed" id="totalAllowed">0</div>
                        <div class="stat-label">Legitimate Requests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value suspicious" id="totalSuspicious">0</div>
                        <div class="stat-label">Suspicious Activities</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="protectionLevel">High</div>
                        <div class="stat-label">Protection Level</div>
                    </div>
                </div>

                <!-- Test Sections -->
                <div class="test-sections">
                    <!-- Attack Simulation -->
                    <div class="test-section">
                        <div class="section-header">
                            <div class="section-title">Attack Simulation</div>
                        </div>
                        <div class="attack-simulator">
                            <div class="attack-controls">
                                <div class="control-group">
                                    <label class="control-label">Attack Type</label>
                                    <select class="control-input" id="attackType">
                                        <option value="volumetric">Volumetric Attack</option>
                                        <option value="protocol">Protocol Attack</option>
                                        <option value="application">Application Layer Attack</option>
                                        <option value="slowloris">Slowloris Attack</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Intensity</label>
                                    <select class="control-input" id="attackIntensity">
                                        <option value="low">Low (100 req/s)</option>
                                        <option value="medium">Medium (1000 req/s)</option>
                                        <option value="high">High (10000 req/s)</option>
                                        <option value="extreme">Extreme (50000 req/s)</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Duration (seconds)</label>
                                    <input type="number" class="control-input" id="attackDuration" value="30" min="5" max="300">
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Source IPs</label>
                                    <input type="number" class="control-input" id="sourceIPs" value="100" min="1" max="10000">
                                </div>
                            </div>
                            <div class="attack-progress" id="attackProgress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill"></div>
                                </div>
                                <div class="progress-text" id="progressText">Preparing attack simulation...</div>
                            </div>
                            <button class="btn btn-danger" id="startAttackBtn" onclick="startAttackSimulation()">
                                <i class="fas fa-play"></i>
                                Start Attack Simulation
                            </button>
                            <button class="btn btn-secondary" id="stopAttackBtn" onclick="stopAttackSimulation()" style="display: none;">
                                <i class="fas fa-stop"></i>
                                Stop Attack
                            </button>
                        </div>
                    </div>

                    <!-- Protection Logs -->
                    <div class="test-section">
                        <div class="section-header">
                            <div class="section-title">Protection Logs</div>
                            <button class="btn btn-sm btn-secondary" onclick="clearLogs()">
                                <i class="fas fa-trash"></i>
                                Clear Logs
                            </button>
                        </div>
                        <div class="protection-logs" id="protectionLogs">
                            <!-- Logs will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Mitigation Panel -->
                <div class="mitigation-panel">
                    <h3>DDoS Mitigation Options</h3>
                    <div class="mitigation-options">
                        <div class="mitigation-option active" onclick="toggleMitigation('rateLimit')">
                            <div class="option-title">Rate Limiting</div>
                            <div class="option-description">Limit requests per IP address per time window</div>
                        </div>
                        <div class="mitigation-option active" onclick="toggleMitigation('geoBlocking')">
                            <div class="option-title">Geo-blocking</div>
                            <div class="option-description">Block traffic from specific geographic regions</div>
                        </div>
                        <div class="mitigation-option active" onclick="toggleMitigation('behaviorAnalysis')">
                            <div class="option-title">Behavior Analysis</div>
                            <div class="option-description">Analyze traffic patterns for anomaly detection</div>
                        </div>
                        <div class="mitigation-option" onclick="toggleMitigation('captcha')">
                            <div class="option-title">CAPTCHA Challenge</div>
                            <div class="option-description">Challenge suspicious requests with CAPTCHA</div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="applyMitigationSettings()">
                        <i class="fas fa-save"></i>
                        Apply Mitigation Settings
                    </button>
                </div>

                <!-- Response Viewer -->
                <div id="ddosResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 DDoS Protection Testing Interface</p>
                    <p>Distributed Denial of Service protection testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let ddosData = {
            isAttackRunning: false,
            attackProgress: 0,
            attackInterval: null,
            logs: [],
            stats: {
                requestsPerSecond: 0,
                blockedRequests: 0,
                suspiciousIPs: 0,
                totalBlocked: 1247,
                totalAllowed: 45632,
                totalSuspicious: 89
            },
            mitigationSettings: {
                rateLimit: true,
                geoBlocking: true,
                behaviorAnalysis: true,
                captcha: false
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load initial data
            loadDDoSStatus();

            // Start real-time updates
            startRealTimeUpdates();
        }

        // DDoS Protection Functions
        async function loadDDoSStatus() {
            window.notificationManager.info('Loading DDoS protection status...');

            try {
                const response = await window.apiClient.request('GET', '/security/ddos/status');

                if (response.success) {
                    updateDDoSStatus(response.data);
                    window.notificationManager.success('DDoS status loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load DDoS status');
                }

                showResponse('ddosResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock data
                loadMockData();
                window.notificationManager.success('DDoS status loaded successfully (simulated)');

                showResponse('ddosResponse', {
                    success: true,
                    data: ddosData.stats,
                    message: 'Mock DDoS status loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockData() {
            updateStatsDisplay();
            addLog('info', 'DDoS protection system initialized');
            addLog('success', 'Rate limiting enabled: 100 requests per minute per IP');
            addLog('success', 'Geo-blocking enabled: Blocking traffic from high-risk regions');
            addLog('info', 'Behavior analysis engine started');
        }

        function updateDDoSStatus(data) {
            if (data.stats) {
                ddosData.stats = { ...ddosData.stats, ...data.stats };
            }
            updateStatsDisplay();
        }

        function updateStatsDisplay() {
            document.getElementById('requestsPerSecond').textContent = ddosData.stats.requestsPerSecond;
            document.getElementById('blockedRequests').textContent = ddosData.stats.blockedRequests;
            document.getElementById('suspiciousIPs').textContent = ddosData.stats.suspiciousIPs;
            document.getElementById('totalBlocked').textContent = formatNumber(ddosData.stats.totalBlocked);
            document.getElementById('totalAllowed').textContent = formatNumber(ddosData.stats.totalAllowed);
            document.getElementById('totalSuspicious').textContent = ddosData.stats.totalSuspicious;
        }

        // Attack Simulation Functions
        async function startAttackSimulation() {
            if (ddosData.isAttackRunning) return;

            const attackType = document.getElementById('attackType').value;
            const intensity = document.getElementById('attackIntensity').value;
            const duration = parseInt(document.getElementById('attackDuration').value);
            const sourceIPs = parseInt(document.getElementById('sourceIPs').value);

            ddosData.isAttackRunning = true;
            ddosData.attackProgress = 0;

            // Update UI
            document.getElementById('startAttackBtn').style.display = 'none';
            document.getElementById('stopAttackBtn').style.display = 'inline-block';
            document.getElementById('attackProgress').classList.add('active');

            window.notificationManager.warning(`Starting ${attackType} attack simulation with ${intensity} intensity`);
            addLog('warning', `Attack simulation started: ${attackType} (${intensity} intensity, ${duration}s duration, ${sourceIPs} source IPs)`);

            try {
                const response = await window.apiClient.request('POST', '/security/ddos/simulate', {
                    type: attackType,
                    intensity: intensity,
                    duration: duration,
                    sourceIPs: sourceIPs
                });

                if (response.success) {
                    simulateAttack(duration);
                } else {
                    window.notificationManager.error(response.error || 'Failed to start attack simulation');
                    stopAttackSimulation();
                }

                showResponse('ddosResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate attack locally
                simulateAttack(duration);

                showResponse('ddosResponse', {
                    success: true,
                    data: { attackType, intensity, duration, sourceIPs },
                    message: 'Mock attack simulation started (endpoint may not be available)'
                }, 'warning');
            }
        }

        function simulateAttack(duration) {
            const progressIncrement = 100 / (duration * 10); // Update every 100ms

            ddosData.attackInterval = setInterval(() => {
                ddosData.attackProgress += progressIncrement;

                if (ddosData.attackProgress >= 100) {
                    ddosData.attackProgress = 100;
                    stopAttackSimulation();
                    return;
                }

                // Update progress bar
                document.getElementById('progressFill').style.width = ddosData.attackProgress + '%';
                document.getElementById('progressText').textContent =
                    `Attack in progress... ${Math.round(ddosData.attackProgress)}% complete`;

                // Simulate traffic and blocking
                if (Math.random() < 0.3) {
                    const rps = Math.floor(Math.random() * 5000) + 1000;
                    const blocked = Math.floor(rps * 0.8);
                    const suspicious = Math.floor(Math.random() * 10) + 1;

                    ddosData.stats.requestsPerSecond = rps;
                    ddosData.stats.blockedRequests += blocked;
                    ddosData.stats.suspiciousIPs += suspicious;
                    ddosData.stats.totalBlocked += blocked;

                    updateStatsDisplay();

                    if (Math.random() < 0.4) {
                        addLog('error', `High traffic detected: ${rps} requests/sec, ${blocked} blocked`);
                    }
                    if (Math.random() < 0.2) {
                        addLog('warning', `${suspicious} new suspicious IP addresses detected`);
                    }
                }
            }, 100);
        }

        function stopAttackSimulation() {
            if (!ddosData.isAttackRunning) return;

            ddosData.isAttackRunning = false;

            if (ddosData.attackInterval) {
                clearInterval(ddosData.attackInterval);
                ddosData.attackInterval = null;
            }

            // Update UI
            document.getElementById('startAttackBtn').style.display = 'inline-block';
            document.getElementById('stopAttackBtn').style.display = 'none';
            document.getElementById('attackProgress').classList.remove('active');
            document.getElementById('progressFill').style.width = '0%';

            // Reset stats
            ddosData.stats.requestsPerSecond = 0;
            updateStatsDisplay();

            window.notificationManager.success('Attack simulation stopped');
            addLog('success', 'Attack simulation completed - Protection systems performed effectively');
        }

        // Mitigation Functions
        function toggleMitigation(option) {
            const element = event.target.closest('.mitigation-option');
            const isActive = element.classList.contains('active');

            if (isActive) {
                element.classList.remove('active');
                ddosData.mitigationSettings[option] = false;
            } else {
                element.classList.add('active');
                ddosData.mitigationSettings[option] = true;
            }
        }

        async function applyMitigationSettings() {
            window.notificationManager.info('Applying mitigation settings...');

            try {
                const response = await window.apiClient.request('PUT', '/security/ddos/mitigation', ddosData.mitigationSettings);

                if (response.success) {
                    window.notificationManager.success('Mitigation settings applied successfully');
                    addLog('success', 'DDoS mitigation settings updated');

                    // Log enabled mitigations
                    Object.keys(ddosData.mitigationSettings).forEach(setting => {
                        if (ddosData.mitigationSettings[setting]) {
                            addLog('info', `${setting} mitigation enabled`);
                        }
                    });
                } else {
                    window.notificationManager.error(response.error || 'Failed to apply mitigation settings');
                }

                showResponse('ddosResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate settings application
                window.notificationManager.success('Mitigation settings applied successfully (simulated)');
                addLog('success', 'DDoS mitigation settings updated (simulated)');

                showResponse('ddosResponse', {
                    success: true,
                    data: ddosData.mitigationSettings,
                    message: 'Mock mitigation settings applied (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Logging Functions
        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp: timestamp,
                level: level,
                message: message
            };

            ddosData.logs.unshift(logEntry);

            // Keep only last 50 logs
            if (ddosData.logs.length > 50) {
                ddosData.logs = ddosData.logs.slice(0, 50);
            }

            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const logsContainer = document.getElementById('protectionLogs');

            if (ddosData.logs.length === 0) {
                logsContainer.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No protection logs available.
                    </div>
                `;
                return;
            }

            const logsHTML = ddosData.logs.map(log => `
                <div class="log-entry">
                    <div class="log-timestamp">${log.timestamp}</div>
                    <div class="log-level ${log.level}">${log.level.toUpperCase()}</div>
                    <div class="log-message">${log.message}</div>
                </div>
            `).join('');

            logsContainer.innerHTML = logsHTML;
        }

        function clearLogs() {
            ddosData.logs = [];
            updateLogsDisplay();
            window.notificationManager.success('Protection logs cleared');
        }

        // Real-time Updates
        function startRealTimeUpdates() {
            setInterval(() => {
                if (!ddosData.isAttackRunning) {
                    // Simulate normal traffic
                    if (Math.random() < 0.1) {
                        const normalTraffic = Math.floor(Math.random() * 50) + 10;
                        ddosData.stats.requestsPerSecond = normalTraffic;
                        ddosData.stats.totalAllowed += normalTraffic;
                        updateStatsDisplay();
                    }

                    // Occasionally add normal logs
                    if (Math.random() < 0.05) {
                        const logMessages = [
                            'Normal traffic patterns detected',
                            'All systems operating normally',
                            'Protection systems on standby',
                            'Traffic analysis completed - no threats detected'
                        ];
                        addLog('info', logMessages[Math.floor(Math.random() * logMessages.length)]);
                    }
                }
            }, 2000);
        }

        // Utility Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
