<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Audit Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .audit-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .audit-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .security-score {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .security-score.excellent {
            color: var(--success-color);
        }

        .security-score.good {
            color: #28a745;
        }

        .security-score.warning {
            color: var(--warning-color);
        }

        .security-score.critical {
            color: var(--error-color);
        }

        .score-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .audit-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .category-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.2s ease;
        }

        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .category-icon {
            font-size: 2rem;
            margin-right: 1rem;
            width: 50px;
            text-align: center;
        }

        .category-icon.excellent {
            color: var(--success-color);
        }

        .category-icon.good {
            color: #28a745;
        }

        .category-icon.warning {
            color: var(--warning-color);
        }

        .category-icon.critical {
            color: var(--error-color);
        }

        .category-info {
            flex: 1;
        }

        .category-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .category-score {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .category-details {
            margin-top: 1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .detail-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .detail-status.pass {
            background: var(--success-color);
            color: white;
        }

        .detail-status.warn {
            background: var(--warning-color);
            color: white;
        }

        .detail-status.fail {
            background: var(--error-color);
            color: white;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .audit-actions {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .vulnerability-list {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .vulnerability-item {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .vulnerability-item:last-child {
            margin-bottom: 0;
        }

        .vulnerability-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .vulnerability-severity {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin-left: auto;
        }

        .vulnerability-severity.critical {
            background: var(--error-color);
            color: white;
        }

        .vulnerability-severity.high {
            background: #dc3545;
            color: white;
        }

        .vulnerability-severity.medium {
            background: var(--warning-color);
            color: white;
        }

        .vulnerability-severity.low {
            background: #17a2b8;
            color: white;
        }

        .vulnerability-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .vulnerability-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .vulnerability-recommendation {
            background: rgba(var(--info-color-rgb), 0.1);
            border: 1px solid var(--info-color);
            border-radius: 4px;
            padding: 0.5rem;
            font-size: 0.8rem;
            color: var(--info-color);
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .progress-fill.excellent {
            background: var(--success-color);
        }

        .progress-fill.good {
            background: #28a745;
        }

        .progress-fill.warning {
            background: var(--warning-color);
        }

        .progress-fill.critical {
            background: var(--error-color);
        }

        .audit-log {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 2rem;
        }

        .log-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
        }

        .log-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
        }

        .log-icon.info {
            background: var(--info-color);
        }

        .log-icon.warning {
            background: var(--warning-color);
        }

        .log-icon.error {
            background: var(--error-color);
        }

        .log-icon.success {
            background: var(--success-color);
        }

        .log-content {
            flex: 1;
        }

        .log-action {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .log-details {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .log-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .scan-status {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .scan-status.scanning {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .scan-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Security Audit Testing</h1>
                    <span class="subtitle">Security Audit and Vulnerability Assessment Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="audit-container">
                <!-- Security Overview -->
                <div class="audit-overview">
                    <div class="security-score excellent" id="securityScore">85</div>
                    <div class="score-description">Overall Security Score - Good security posture with minor improvements needed</div>
                    <div class="progress-bar">
                        <div class="progress-fill good" id="progressFill" style="width: 85%;"></div>
                    </div>
                    <div class="scan-status" id="scanStatus" style="display: none;">
                        <div class="scan-spinner"></div>
                        <span>Security scan in progress...</span>
                    </div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="runFullAudit()">
                        <i class="fas fa-search"></i>
                        Run Full Audit
                    </button>
                    <button class="btn btn-warning" onclick="runVulnerabilityScan()">
                        <i class="fas fa-bug"></i>
                        Vulnerability Scan
                    </button>
                    <button class="btn btn-info" onclick="runComplianceCheck()">
                        <i class="fas fa-clipboard-check"></i>
                        Compliance Check
                    </button>
                    <button class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i>
                        Generate Report
                    </button>
                </div>

                <!-- Audit Categories -->
                <div class="audit-categories">
                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon excellent">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-title">Authentication</div>
                                <div class="category-score">Score: 92/100</div>
                            </div>
                        </div>
                        <div class="category-details">
                            <div class="detail-item">
                                <div class="detail-label">Password Policy</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">MFA Implementation</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Session Management</div>
                                <div class="detail-status warn">WARN</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Account Lockout</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon warning">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-title">Data Protection</div>
                                <div class="category-score">Score: 78/100</div>
                            </div>
                        </div>
                        <div class="category-details">
                            <div class="detail-item">
                                <div class="detail-label">Encryption at Rest</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Encryption in Transit</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Data Backup</div>
                                <div class="detail-status warn">WARN</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">PII Handling</div>
                                <div class="detail-status fail">FAIL</div>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon good">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-title">Network Security</div>
                                <div class="category-score">Score: 88/100</div>
                            </div>
                        </div>
                        <div class="category-details">
                            <div class="detail-item">
                                <div class="detail-label">HTTPS Enforcement</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">CORS Configuration</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Rate Limiting</div>
                                <div class="detail-status warn">WARN</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Firewall Rules</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon critical">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-title">Application Security</div>
                                <div class="category-score">Score: 65/100</div>
                            </div>
                        </div>
                        <div class="category-details">
                            <div class="detail-item">
                                <div class="detail-label">Input Validation</div>
                                <div class="detail-status fail">FAIL</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">SQL Injection Protection</div>
                                <div class="detail-status warn">WARN</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">XSS Protection</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">CSRF Protection</div>
                                <div class="detail-status pass">PASS</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vulnerability List -->
                <div class="vulnerability-list">
                    <h3>Identified Vulnerabilities</h3>

                    <div class="vulnerability-item">
                        <div class="vulnerability-header">
                            <div class="vulnerability-title">Insufficient Input Validation</div>
                            <div class="vulnerability-severity critical">CRITICAL</div>
                        </div>
                        <div class="vulnerability-description">
                            User input is not properly validated, allowing potential injection attacks.
                        </div>
                        <div class="vulnerability-recommendation">
                            Implement comprehensive input validation and sanitization for all user inputs.
                        </div>
                    </div>

                    <div class="vulnerability-item">
                        <div class="vulnerability-header">
                            <div class="vulnerability-title">Weak Session Configuration</div>
                            <div class="vulnerability-severity medium">MEDIUM</div>
                        </div>
                        <div class="vulnerability-description">
                            Session cookies lack secure flags and have extended expiration times.
                        </div>
                        <div class="vulnerability-recommendation">
                            Configure session cookies with Secure, HttpOnly, and SameSite flags.
                        </div>
                    </div>

                    <div class="vulnerability-item">
                        <div class="vulnerability-header">
                            <div class="vulnerability-title">Missing Security Headers</div>
                            <div class="vulnerability-severity medium">MEDIUM</div>
                        </div>
                        <div class="vulnerability-description">
                            Important security headers like Content-Security-Policy are missing.
                        </div>
                        <div class="vulnerability-recommendation">
                            Implement security headers: CSP, HSTS, X-Frame-Options, X-Content-Type-Options.
                        </div>
                    </div>

                    <div class="vulnerability-item">
                        <div class="vulnerability-header">
                            <div class="vulnerability-title">Inadequate Rate Limiting</div>
                            <div class="vulnerability-severity low">LOW</div>
                        </div>
                        <div class="vulnerability-description">
                            API endpoints lack proper rate limiting, potentially allowing abuse.
                        </div>
                        <div class="vulnerability-recommendation">
                            Implement rate limiting on all API endpoints, especially authentication.
                        </div>
                    </div>
                </div>

                <!-- Audit Actions -->
                <div class="audit-actions">
                    <h3>Audit Actions</h3>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="runPenetrationTest()">
                            <i class="fas fa-crosshairs"></i>
                            Penetration Test
                        </button>
                        <button class="btn btn-warning" onclick="runSQLInjectionTest()">
                            <i class="fas fa-database"></i>
                            SQL Injection Test
                        </button>
                        <button class="btn btn-danger" onclick="runXSSTest()">
                            <i class="fas fa-code"></i>
                            XSS Vulnerability Test
                        </button>
                        <button class="btn btn-info" onclick="runCSRFTest()">
                            <i class="fas fa-shield-alt"></i>
                            CSRF Protection Test
                        </button>
                        <button class="btn btn-secondary" onclick="runSecurityHeadersTest()">
                            <i class="fas fa-list"></i>
                            Security Headers Test
                        </button>
                        <button class="btn btn-success" onclick="runSSLTest()">
                            <i class="fas fa-lock"></i>
                            SSL/TLS Test
                        </button>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Authentication Bypass</div>
                        <div class="scenario-description">Test for authentication bypass vulnerabilities</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="testAuthBypass()">
                                <i class="fas fa-user-slash"></i>
                                Test Bypass
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Privilege Escalation</div>
                        <div class="scenario-description">Test for privilege escalation vulnerabilities</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testPrivilegeEscalation()">
                                <i class="fas fa-user-cog"></i>
                                Test Escalation
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Data Exposure</div>
                        <div class="scenario-description">Test for sensitive data exposure</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testDataExposure()">
                                <i class="fas fa-eye"></i>
                                Test Exposure
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Session Hijacking</div>
                        <div class="scenario-description">Test for session hijacking vulnerabilities</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testSessionHijacking()">
                                <i class="fas fa-user-secret"></i>
                                Test Hijacking
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Brute Force Attack</div>
                        <div class="scenario-description">Test resistance to brute force attacks</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="testBruteForce()">
                                <i class="fas fa-hammer"></i>
                                Test Brute Force
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">API Security</div>
                        <div class="scenario-description">Test API security and authorization</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="testAPISecurity()">
                                <i class="fas fa-cogs"></i>
                                Test API Security
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Audit Log -->
                <div class="audit-log" id="auditLog">
                    <div class="log-header">Security Audit Log</div>
                    <!-- Log items will be populated here -->
                </div>

                <!-- Response Viewer -->
                <div id="auditResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Security Audit Testing Interface</p>
                    <p>Security audit and vulnerability assessment testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let auditData = {
            overallScore: 85,
            categories: {
                authentication: { score: 92, status: 'excellent' },
                dataProtection: { score: 78, status: 'warning' },
                networkSecurity: { score: 88, status: 'good' },
                applicationSecurity: { score: 65, status: 'critical' }
            },
            vulnerabilities: [],
            auditLog: []
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load audit data
            loadAuditData();

            // Load audit log
            loadAuditLog();
        }

        // Audit Functions
        async function runFullAudit() {
            showScanStatus(true);
            window.notificationManager.info('Starting comprehensive security audit...');

            try {
                const response = await window.apiClient.request('POST', '/security/audit/full');

                if (response.success) {
                    auditData = response.data;
                    updateAuditDisplay();
                    addAuditLogItem('Full Audit Completed', 'Comprehensive security audit finished', 'success');
                    window.notificationManager.success('Security audit completed successfully');
                } else {
                    window.notificationManager.error(response.error || 'Audit failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate audit completion
                setTimeout(() => {
                    simulateAuditResults();
                    addAuditLogItem('Full Audit Completed', 'Comprehensive security audit finished (simulated)', 'success');
                    window.notificationManager.success('Security audit completed successfully (simulated)');

                    showResponse('auditResponse', {
                        success: true,
                        data: auditData,
                        message: 'Mock security audit completed (endpoint may not be available)'
                    }, 'warning');
                }, 3000);
            }

            setTimeout(() => showScanStatus(false), 3000);
        }

        async function runVulnerabilityScan() {
            window.notificationManager.info('Running vulnerability scan...');
            addAuditLogItem('Vulnerability Scan Started', 'Scanning for security vulnerabilities', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/audit/vulnerabilities');

                if (response.success) {
                    window.notificationManager.success('Vulnerability scan completed');
                    addAuditLogItem('Vulnerability Scan Completed', `Found ${response.data.vulnerabilities.length} vulnerabilities`, 'warning');
                } else {
                    window.notificationManager.error(response.error || 'Vulnerability scan failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate vulnerability scan
                setTimeout(() => {
                    window.notificationManager.success('Vulnerability scan completed (simulated)');
                    addAuditLogItem('Vulnerability Scan Completed', 'Found 4 vulnerabilities (simulated)', 'warning');

                    showResponse('auditResponse', {
                        success: true,
                        data: { vulnerabilities: ['Input validation', 'Session config', 'Security headers', 'Rate limiting'] },
                        message: 'Mock vulnerability scan completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runComplianceCheck() {
            window.notificationManager.info('Running compliance check...');
            addAuditLogItem('Compliance Check Started', 'Checking security compliance standards', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/audit/compliance');

                if (response.success) {
                    window.notificationManager.success('Compliance check completed');
                    addAuditLogItem('Compliance Check Completed', 'Security compliance verified', 'success');
                } else {
                    window.notificationManager.error(response.error || 'Compliance check failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate compliance check
                setTimeout(() => {
                    window.notificationManager.success('Compliance check completed (simulated)');
                    addAuditLogItem('Compliance Check Completed', 'OWASP Top 10 compliance: 70% (simulated)', 'warning');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            standards: ['OWASP Top 10', 'NIST', 'ISO 27001'],
                            compliance: { owasp: 70, nist: 65, iso: 80 }
                        },
                        message: 'Mock compliance check completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        function generateReport() {
            window.notificationManager.info('Generating security audit report...');

            const reportData = {
                timestamp: new Date().toISOString(),
                overallScore: auditData.overallScore,
                categories: auditData.categories,
                vulnerabilities: auditData.vulnerabilities.length || 4,
                recommendations: [
                    'Implement comprehensive input validation',
                    'Configure secure session management',
                    'Add missing security headers',
                    'Implement proper rate limiting'
                ]
            };

            // Simulate report generation
            setTimeout(() => {
                const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `security-audit-report-${Date.now()}.json`;
                a.click();
                window.URL.revokeObjectURL(url);

                window.notificationManager.success('Security audit report generated');
                addAuditLogItem('Report Generated', 'Security audit report downloaded', 'success');
            }, 1000);
        }

        // Security Test Functions
        async function runPenetrationTest() {
            window.notificationManager.info('Running penetration test...');
            addAuditLogItem('Penetration Test Started', 'Comprehensive penetration testing initiated', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/test/penetration');

                if (response.success) {
                    window.notificationManager.success('Penetration test completed');
                    addAuditLogItem('Penetration Test Completed', 'Security vulnerabilities identified', 'warning');
                } else {
                    window.notificationManager.error(response.error || 'Penetration test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate penetration test
                setTimeout(() => {
                    window.notificationManager.success('Penetration test completed (simulated)');
                    addAuditLogItem('Penetration Test Completed', 'Found 3 critical vulnerabilities (simulated)', 'error');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            vulnerabilities: ['SQL Injection', 'XSS', 'Authentication Bypass'],
                            severity: 'High',
                            recommendations: ['Immediate patching required']
                        },
                        message: 'Mock penetration test completed (endpoint may not be available)'
                    }, 'warning');
                }, 3000);
            }
        }

        async function runSQLInjectionTest() {
            window.notificationManager.info('Testing for SQL injection vulnerabilities...');
            addAuditLogItem('SQL Injection Test Started', 'Testing database security', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/test/sql-injection');

                if (response.success) {
                    const vulnerable = response.data.vulnerable;
                    if (vulnerable) {
                        window.notificationManager.error('SQL injection vulnerabilities found!');
                        addAuditLogItem('SQL Injection Vulnerability', 'Critical database security issue detected', 'error');
                    } else {
                        window.notificationManager.success('No SQL injection vulnerabilities found');
                        addAuditLogItem('SQL Injection Test Passed', 'Database security verified', 'success');
                    }
                } else {
                    window.notificationManager.error(response.error || 'SQL injection test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate SQL injection test
                setTimeout(() => {
                    window.notificationManager.warning('SQL injection vulnerabilities found (simulated)');
                    addAuditLogItem('SQL Injection Vulnerability', 'Input validation bypass detected (simulated)', 'error');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            vulnerable: true,
                            endpoints: ['/api/users', '/api/search'],
                            severity: 'Critical',
                            recommendation: 'Use parameterized queries and input validation'
                        },
                        message: 'Mock SQL injection test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runXSSTest() {
            window.notificationManager.info('Testing for XSS vulnerabilities...');
            addAuditLogItem('XSS Test Started', 'Testing cross-site scripting protection', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/test/xss');

                if (response.success) {
                    const vulnerable = response.data.vulnerable;
                    if (vulnerable) {
                        window.notificationManager.error('XSS vulnerabilities found!');
                        addAuditLogItem('XSS Vulnerability', 'Cross-site scripting vulnerability detected', 'error');
                    } else {
                        window.notificationManager.success('No XSS vulnerabilities found');
                        addAuditLogItem('XSS Test Passed', 'XSS protection verified', 'success');
                    }
                } else {
                    window.notificationManager.error(response.error || 'XSS test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate XSS test
                setTimeout(() => {
                    window.notificationManager.success('No XSS vulnerabilities found (simulated)');
                    addAuditLogItem('XSS Test Passed', 'Output encoding and CSP protection verified (simulated)', 'success');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            vulnerable: false,
                            protection: ['Output encoding', 'Content Security Policy'],
                            recommendation: 'Continue monitoring for new XSS vectors'
                        },
                        message: 'Mock XSS test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runCSRFTest() {
            window.notificationManager.info('Testing CSRF protection...');
            addAuditLogItem('CSRF Test Started', 'Testing cross-site request forgery protection', 'info');

            try {
                const response = await window.apiClient.request('POST', '/security/test/csrf');

                if (response.success) {
                    const protected = response.data.protected;
                    if (protected) {
                        window.notificationManager.success('CSRF protection is active');
                        addAuditLogItem('CSRF Test Passed', 'CSRF tokens properly implemented', 'success');
                    } else {
                        window.notificationManager.error('CSRF protection missing!');
                        addAuditLogItem('CSRF Vulnerability', 'Missing CSRF protection detected', 'error');
                    }
                } else {
                    window.notificationManager.error(response.error || 'CSRF test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate CSRF test
                setTimeout(() => {
                    window.notificationManager.success('CSRF protection is active (simulated)');
                    addAuditLogItem('CSRF Test Passed', 'CSRF tokens and SameSite cookies verified (simulated)', 'success');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            protected: true,
                            mechanisms: ['CSRF tokens', 'SameSite cookies', 'Origin validation'],
                            recommendation: 'CSRF protection is properly configured'
                        },
                        message: 'Mock CSRF test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runSecurityHeadersTest() {
            window.notificationManager.info('Testing security headers...');
            addAuditLogItem('Security Headers Test Started', 'Checking HTTP security headers', 'info');

            try {
                const response = await window.apiClient.request('GET', '/security/test/headers');

                if (response.success) {
                    const headers = response.data.headers;
                    const missing = response.data.missing || [];

                    if (missing.length === 0) {
                        window.notificationManager.success('All security headers present');
                        addAuditLogItem('Security Headers Test Passed', 'All required security headers found', 'success');
                    } else {
                        window.notificationManager.warning(`Missing ${missing.length} security headers`);
                        addAuditLogItem('Security Headers Warning', `Missing headers: ${missing.join(', ')}`, 'warning');
                    }
                } else {
                    window.notificationManager.error(response.error || 'Security headers test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate security headers test
                setTimeout(() => {
                    const missingHeaders = ['Content-Security-Policy', 'X-Frame-Options'];
                    window.notificationManager.warning(`Missing ${missingHeaders.length} security headers (simulated)`);
                    addAuditLogItem('Security Headers Warning', `Missing headers: ${missingHeaders.join(', ')} (simulated)`, 'warning');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            present: ['Strict-Transport-Security', 'X-Content-Type-Options'],
                            missing: missingHeaders,
                            recommendation: 'Implement missing security headers for better protection'
                        },
                        message: 'Mock security headers test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runSSLTest() {
            window.notificationManager.info('Testing SSL/TLS configuration...');
            addAuditLogItem('SSL/TLS Test Started', 'Checking SSL/TLS security configuration', 'info');

            try {
                const response = await window.apiClient.request('GET', '/security/test/ssl');

                if (response.success) {
                    const grade = response.data.grade;
                    if (grade === 'A' || grade === 'A+') {
                        window.notificationManager.success(`SSL/TLS grade: ${grade}`);
                        addAuditLogItem('SSL/TLS Test Passed', `Excellent SSL/TLS configuration (${grade})`, 'success');
                    } else {
                        window.notificationManager.warning(`SSL/TLS grade: ${grade}`);
                        addAuditLogItem('SSL/TLS Warning', `SSL/TLS configuration needs improvement (${grade})`, 'warning');
                    }
                } else {
                    window.notificationManager.error(response.error || 'SSL/TLS test failed');
                }

                showResponse('auditResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate SSL test
                setTimeout(() => {
                    const grade = 'A';
                    window.notificationManager.success(`SSL/TLS grade: ${grade} (simulated)`);
                    addAuditLogItem('SSL/TLS Test Passed', `Strong SSL/TLS configuration verified (${grade}) (simulated)`, 'success');

                    showResponse('auditResponse', {
                        success: true,
                        data: {
                            grade: grade,
                            protocols: ['TLS 1.2', 'TLS 1.3'],
                            ciphers: ['Strong ciphers only'],
                            recommendation: 'SSL/TLS configuration is secure'
                        },
                        message: 'Mock SSL/TLS test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        // Test Scenario Functions
        function testAuthBypass() {
            window.notificationManager.info('Testing authentication bypass...');
            addAuditLogItem('Auth Bypass Test Started', 'Testing for authentication bypass vulnerabilities', 'info');

            setTimeout(() => {
                window.notificationManager.warning('Authentication bypass vulnerability found (simulated)');
                addAuditLogItem('Auth Bypass Vulnerability', 'JWT token validation bypass detected (simulated)', 'error');
            }, 2000);
        }

        function testPrivilegeEscalation() {
            window.notificationManager.info('Testing privilege escalation...');
            addAuditLogItem('Privilege Escalation Test Started', 'Testing for privilege escalation vulnerabilities', 'info');

            setTimeout(() => {
                window.notificationManager.success('No privilege escalation vulnerabilities found (simulated)');
                addAuditLogItem('Privilege Escalation Test Passed', 'Role-based access control verified (simulated)', 'success');
            }, 2000);
        }

        function testDataExposure() {
            window.notificationManager.info('Testing for data exposure...');
            addAuditLogItem('Data Exposure Test Started', 'Testing for sensitive data exposure', 'info');

            setTimeout(() => {
                window.notificationManager.warning('Sensitive data exposure found (simulated)');
                addAuditLogItem('Data Exposure Warning', 'PII data found in API responses (simulated)', 'warning');
            }, 2000);
        }

        function testSessionHijacking() {
            window.notificationManager.info('Testing session hijacking resistance...');
            addAuditLogItem('Session Hijacking Test Started', 'Testing session security', 'info');

            setTimeout(() => {
                window.notificationManager.warning('Session security issues found (simulated)');
                addAuditLogItem('Session Security Warning', 'Session fixation vulnerability detected (simulated)', 'warning');
            }, 2000);
        }

        function testBruteForce() {
            window.notificationManager.info('Testing brute force resistance...');
            addAuditLogItem('Brute Force Test Started', 'Testing account lockout and rate limiting', 'info');

            setTimeout(() => {
                window.notificationManager.success('Brute force protection active (simulated)');
                addAuditLogItem('Brute Force Test Passed', 'Account lockout and rate limiting verified (simulated)', 'success');
            }, 2000);
        }

        function testAPISecurity() {
            window.notificationManager.info('Testing API security...');
            addAuditLogItem('API Security Test Started', 'Testing API authentication and authorization', 'info');

            setTimeout(() => {
                window.notificationManager.warning('API security issues found (simulated)');
                addAuditLogItem('API Security Warning', 'Missing API rate limiting detected (simulated)', 'warning');
            }, 2000);
        }

        // Helper Functions
        function simulateAuditResults() {
            // Simulate random score variations
            auditData.overallScore = Math.floor(Math.random() * 20) + 75; // 75-95
            auditData.categories.authentication.score = Math.floor(Math.random() * 15) + 85;
            auditData.categories.dataProtection.score = Math.floor(Math.random() * 25) + 70;
            auditData.categories.networkSecurity.score = Math.floor(Math.random() * 20) + 80;
            auditData.categories.applicationSecurity.score = Math.floor(Math.random() * 30) + 60;

            updateAuditDisplay();
        }

        function updateAuditDisplay() {
            const scoreElement = document.getElementById('securityScore');
            const progressElement = document.getElementById('progressFill');

            scoreElement.textContent = auditData.overallScore;
            progressElement.style.width = auditData.overallScore + '%';

            // Update score class
            scoreElement.className = 'security-score ' + getScoreClass(auditData.overallScore);
            progressElement.className = 'progress-fill ' + getScoreClass(auditData.overallScore);
        }

        function getScoreClass(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 70) return 'warning';
            return 'critical';
        }

        function showScanStatus(show) {
            const scanStatus = document.getElementById('scanStatus');
            scanStatus.style.display = show ? 'block' : 'none';

            if (show) {
                scanStatus.classList.add('scanning');
            } else {
                scanStatus.classList.remove('scanning');
            }
        }

        function loadAuditData() {
            updateAuditDisplay();
        }

        // Audit Log Functions
        function loadAuditLog() {
            auditData.auditLog = [
                {
                    action: 'Security Audit Initialized',
                    details: 'Security audit system started',
                    timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
                    type: 'info'
                },
                {
                    action: 'Vulnerability Scan Completed',
                    details: 'Found 4 security vulnerabilities',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    type: 'warning'
                },
                {
                    action: 'SSL/TLS Test Passed',
                    details: 'Strong SSL/TLS configuration verified',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                    type: 'success'
                }
            ];

            displayAuditLog();
        }

        function displayAuditLog() {
            const auditLog = document.getElementById('auditLog');

            const logHTML = auditData.auditLog.map(log => `
                <div class="log-item">
                    <div class="log-icon ${log.type}">
                        <i class="fas ${getLogIcon(log.type)}"></i>
                    </div>
                    <div class="log-content">
                        <div class="log-action">${log.action}</div>
                        <div class="log-details">${log.details}</div>
                    </div>
                    <div class="log-time">${formatTimeAgo(log.timestamp)}</div>
                </div>
            `).join('');

            auditLog.innerHTML = `
                <div class="log-header">Security Audit Log</div>
                ${logHTML}
            `;
        }

        function addAuditLogItem(action, details, type) {
            auditData.auditLog.unshift({
                action: action,
                details: details,
                timestamp: new Date(),
                type: type
            });

            // Keep only last 20 items
            if (auditData.auditLog.length > 20) {
                auditData.auditLog = auditData.auditLog.slice(0, 20);
            }

            displayAuditLog();
        }

        function getLogIcon(type) {
            switch (type) {
                case 'success': return 'fa-check';
                case 'warning': return 'fa-exclamation-triangle';
                case 'error': return 'fa-times';
                default: return 'fa-info';
            }
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
