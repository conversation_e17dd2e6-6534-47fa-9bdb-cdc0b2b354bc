<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penetration Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pentest-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .pentest-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .pentest-status {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .pentest-status.ready {
            color: var(--success-color);
        }

        .pentest-status.running {
            color: var(--warning-color);
        }

        .pentest-status.completed {
            color: var(--info-color);
        }

        .pentest-phases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .phase-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.2s ease;
            cursor: pointer;
        }

        .phase-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .phase-card.active {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .phase-card.completed {
            border-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.1);
        }

        .phase-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem auto;
        }

        .phase-number.active {
            background: var(--primary-color);
            color: white;
        }

        .phase-number.completed {
            background: var(--success-color);
            color: white;
        }

        .phase-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .phase-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .attack-vectors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .vector-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .vector-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .vector-icon {
            font-size: 2rem;
            margin-right: 1rem;
            width: 50px;
            text-align: center;
        }

        .vector-icon.web {
            color: #007bff;
        }

        .vector-icon.network {
            color: #28a745;
        }

        .vector-icon.social {
            color: #ffc107;
        }

        .vector-icon.physical {
            color: #dc3545;
        }

        .vector-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .vector-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .vector-techniques {
            margin-top: 1rem;
        }

        .technique-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .technique-item:last-child {
            border-bottom: none;
        }

        .technique-name {
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .technique-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .technique-status.pending {
            background: var(--border-color);
            color: var(--text-secondary);
        }

        .technique-status.running {
            background: var(--warning-color);
            color: white;
        }

        .technique-status.success {
            background: var(--success-color);
            color: white;
        }

        .technique-status.failed {
            background: var(--error-color);
            color: white;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .pentest-controls {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .pentest-button {
            padding: 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .pentest-button.start {
            background: var(--success-color);
            color: white;
        }

        .pentest-button.start:hover {
            background: var(--success-color-dark);
            transform: translateY(-2px);
        }

        .pentest-button.stop {
            background: var(--error-color);
            color: white;
        }

        .pentest-button.stop:hover {
            background: var(--error-color-dark);
            transform: translateY(-2px);
        }

        .pentest-button.pause {
            background: var(--warning-color);
            color: white;
        }

        .pentest-button.pause:hover {
            background: var(--warning-color-dark);
            transform: translateY(-2px);
        }

        .pentest-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .test-results {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .result-metric {
            text-align: center;
            padding: 1rem;
            background: var(--hover-background);
            border-radius: 8px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .metric-value.critical {
            color: var(--error-color);
        }

        .metric-value.high {
            color: #dc3545;
        }

        .metric-value.medium {
            color: var(--warning-color);
        }

        .metric-value.success {
            color: var(--success-color);
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .findings-list {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .finding-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-info {
            flex: 1;
        }

        .finding-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .finding-description {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .finding-severity {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
        }

        .finding-severity.critical {
            background: var(--error-color);
            color: white;
        }

        .finding-severity.high {
            background: #dc3545;
            color: white;
        }

        .finding-severity.medium {
            background: var(--warning-color);
            color: white;
        }

        .finding-severity.low {
            background: #17a2b8;
            color: white;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .progress-indicator {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .progress-indicator.active {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <h1>Penetration Testing</h1>
                    <span class="subtitle">Penetration Testing and Security Assessment Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="pentest-container">
                <!-- Penetration Test Overview -->
                <div class="pentest-overview">
                    <div class="pentest-status ready" id="pentestStatus">Ready to Start Penetration Test</div>
                    <p>Comprehensive security assessment using automated and manual testing techniques</p>
                    
                    <div class="progress-indicator" id="progressIndicator" style="display: none;">
                        <div class="spinner"></div>
                        <span id="progressText">Initializing penetration test...</span>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%;">0%</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="startFullPentest()">
                        <i class="fas fa-play"></i>
                        Start Full Pentest
                    </button>
                    <button class="btn btn-warning" onclick="runWebAppTest()">
                        <i class="fas fa-globe"></i>
                        Web App Test
                    </button>
                    <button class="btn btn-danger" onclick="runNetworkTest()">
                        <i class="fas fa-network-wired"></i>
                        Network Test
                    </button>
                    <button class="btn btn-info" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i>
                        Generate Report
                    </button>
                </div>

                <!-- Penetration Test Phases -->
                <div class="pentest-phases">
                    <div class="phase-card" id="phase1">
                        <div class="phase-number">1</div>
                        <div class="phase-title">Reconnaissance</div>
                        <div class="phase-description">Information gathering and target analysis</div>
                    </div>
                    <div class="phase-card" id="phase2">
                        <div class="phase-number">2</div>
                        <div class="phase-title">Scanning</div>
                        <div class="phase-description">Network and service discovery</div>
                    </div>
                    <div class="phase-card" id="phase3">
                        <div class="phase-number">3</div>
                        <div class="phase-title">Enumeration</div>
                        <div class="phase-description">Service and vulnerability identification</div>
                    </div>
                    <div class="phase-card" id="phase4">
                        <div class="phase-number">4</div>
                        <div class="phase-title">Exploitation</div>
                        <div class="phase-description">Vulnerability exploitation and access</div>
                    </div>
                    <div class="phase-card" id="phase5">
                        <div class="phase-number">5</div>
                        <div class="phase-title">Post-Exploitation</div>
                        <div class="phase-description">Privilege escalation and persistence</div>
                    </div>
                    <div class="phase-card" id="phase6">
                        <div class="phase-number">6</div>
                        <div class="phase-title">Reporting</div>
                        <div class="phase-description">Documentation and recommendations</div>
                    </div>
                </div>

                <!-- Attack Vectors -->
                <div class="attack-vectors">
                    <div class="vector-card">
                        <div class="vector-header">
                            <div class="vector-icon web">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div>
                                <div class="vector-title">Web Application</div>
                                <div class="vector-description">Testing web application security</div>
                            </div>
                        </div>
                        <div class="vector-techniques">
                            <div class="technique-item">
                                <div class="technique-name">SQL Injection</div>
                                <div class="technique-status pending" id="sqlInjectionStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Cross-Site Scripting</div>
                                <div class="technique-status pending" id="xssStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">CSRF</div>
                                <div class="technique-status pending" id="csrfStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Authentication Bypass</div>
                                <div class="technique-status pending" id="authBypassStatus">PENDING</div>
                            </div>
                        </div>
                    </div>

                    <div class="vector-card">
                        <div class="vector-header">
                            <div class="vector-icon network">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div>
                                <div class="vector-title">Network Security</div>
                                <div class="vector-description">Network infrastructure testing</div>
                            </div>
                        </div>
                        <div class="vector-techniques">
                            <div class="technique-item">
                                <div class="technique-name">Port Scanning</div>
                                <div class="technique-status pending" id="portScanStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Service Enumeration</div>
                                <div class="technique-status pending" id="serviceEnumStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Vulnerability Scanning</div>
                                <div class="technique-status pending" id="vulnScanStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Network Sniffing</div>
                                <div class="technique-status pending" id="sniffingStatus">PENDING</div>
                            </div>
                        </div>
                    </div>

                    <div class="vector-card">
                        <div class="vector-header">
                            <div class="vector-icon social">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <div class="vector-title">Social Engineering</div>
                                <div class="vector-description">Human factor security testing</div>
                            </div>
                        </div>
                        <div class="vector-techniques">
                            <div class="technique-item">
                                <div class="technique-name">Phishing Simulation</div>
                                <div class="technique-status pending" id="phishingStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Pretexting</div>
                                <div class="technique-status pending" id="pretextingStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Baiting</div>
                                <div class="technique-status pending" id="baitingStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Tailgating</div>
                                <div class="technique-status pending" id="tailgatingStatus">PENDING</div>
                            </div>
                        </div>
                    </div>

                    <div class="vector-card">
                        <div class="vector-header">
                            <div class="vector-icon physical">
                                <i class="fas fa-building"></i>
                            </div>
                            <div>
                                <div class="vector-title">Physical Security</div>
                                <div class="vector-description">Physical access and security testing</div>
                            </div>
                        </div>
                        <div class="vector-techniques">
                            <div class="technique-item">
                                <div class="technique-name">Lock Picking</div>
                                <div class="technique-status pending" id="lockPickingStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Badge Cloning</div>
                                <div class="technique-status pending" id="badgeCloningStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">RFID Skimming</div>
                                <div class="technique-status pending" id="rfidSkimmingStatus">PENDING</div>
                            </div>
                            <div class="technique-item">
                                <div class="technique-name">Dumpster Diving</div>
                                <div class="technique-status pending" id="dumpsterDivingStatus">PENDING</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Penetration Test Controls -->
                <div class="pentest-controls">
                    <h3>Penetration Test Controls</h3>
                    <div class="control-buttons">
                        <button class="pentest-button start" id="startBtn" onclick="startPenetrationTest()">
                            <i class="fas fa-play"></i>
                            Start Pentest
                        </button>
                        <button class="pentest-button pause" id="pauseBtn" onclick="pausePenetrationTest()" disabled>
                            <i class="fas fa-pause"></i>
                            Pause Pentest
                        </button>
                        <button class="pentest-button stop" id="stopBtn" onclick="stopPenetrationTest()" disabled>
                            <i class="fas fa-stop"></i>
                            Stop Pentest
                        </button>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="test-results">
                    <h3>Penetration Test Results</h3>
                    <div class="results-summary">
                        <div class="result-metric">
                            <div class="metric-value critical" id="criticalFindings">0</div>
                            <div class="metric-label">Critical Findings</div>
                        </div>
                        <div class="result-metric">
                            <div class="metric-value high" id="highFindings">0</div>
                            <div class="metric-label">High Risk</div>
                        </div>
                        <div class="result-metric">
                            <div class="metric-value medium" id="mediumFindings">0</div>
                            <div class="metric-label">Medium Risk</div>
                        </div>
                        <div class="result-metric">
                            <div class="metric-value success" id="testsCompleted">0</div>
                            <div class="metric-label">Tests Completed</div>
                        </div>
                    </div>

                    <div class="findings-list" id="findingsList">
                        <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                            No findings yet. Start a penetration test to see results.
                        </p>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">OWASP Testing</div>
                        <div class="scenario-description">Comprehensive OWASP methodology testing</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="runOWASPTest()">
                                <i class="fas fa-list"></i>
                                Run OWASP Test
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Red Team Exercise</div>
                        <div class="scenario-description">Full adversarial simulation testing</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="runRedTeamExercise()">
                                <i class="fas fa-user-ninja"></i>
                                Run Red Team
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">API Security Test</div>
                        <div class="scenario-description">Focused API endpoint security testing</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="runAPISecurityTest()">
                                <i class="fas fa-cogs"></i>
                                Run API Test
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Infrastructure Test</div>
                        <div class="scenario-description">Network and infrastructure security testing</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="runInfrastructureTest()">
                                <i class="fas fa-server"></i>
                                Run Infra Test
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Wireless Security</div>
                        <div class="scenario-description">Wireless network security assessment</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-secondary" onclick="runWirelessTest()">
                                <i class="fas fa-wifi"></i>
                                Run Wireless Test
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Social Engineering</div>
                        <div class="scenario-description">Human factor security testing</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="runSocialEngineeringTest()">
                                <i class="fas fa-users"></i>
                                Run Social Test
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="pentestResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Penetration Testing Interface</p>
                    <p>Penetration testing and security assessment testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let pentestData = {
            isRunning: false,
            currentPhase: 0,
            findings: [],
            progress: 0,
            testResults: {
                critical: 0,
                high: 0,
                medium: 0,
                completed: 0
            }
        };

        let pentestInterval = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load sample findings for demo
            loadSampleFindings();
        }

        // Main Penetration Test Functions
        async function startPenetrationTest() {
            if (pentestData.isRunning) {
                window.notificationManager.warning('Penetration test already in progress');
                return;
            }

            pentestData.isRunning = true;
            pentestData.currentPhase = 0;
            pentestData.progress = 0;

            updatePentestUI(true);
            window.notificationManager.info('Starting comprehensive penetration test...');

            try {
                const response = await window.apiClient.request('POST', '/security/pentest/start');

                if (response.success) {
                    // Real pentest would be handled here
                    simulatePenetrationTest();
                } else {
                    throw new Error(response.error || 'Pentest failed to start');
                }

                showResponse('pentestResponse', response, 'success');
            } catch (error) {
                // Simulate penetration test
                simulatePenetrationTest();

                showResponse('pentestResponse', {
                    success: true,
                    data: { pentestId: 'pentest_' + Date.now() },
                    message: 'Mock penetration test started (endpoint may not be available)'
                }, 'warning');
            }
        }

        function simulatePenetrationTest() {
            const phases = [
                'Reconnaissance phase - Gathering target information...',
                'Scanning phase - Discovering network services...',
                'Enumeration phase - Identifying vulnerabilities...',
                'Exploitation phase - Attempting to exploit vulnerabilities...',
                'Post-exploitation phase - Escalating privileges...',
                'Reporting phase - Documenting findings...'
            ];

            let phaseIndex = 0;
            pentestData.progress = 0;

            pentestInterval = setInterval(() => {
                if (phaseIndex < phases.length) {
                    updatePhaseStatus(phaseIndex + 1, 'active');
                    if (phaseIndex > 0) {
                        updatePhaseStatus(phaseIndex, 'completed');
                    }

                    document.getElementById('progressText').textContent = phases[phaseIndex];
                    pentestData.progress = Math.floor(((phaseIndex + 1) / phases.length) * 100);
                    updateProgressBar(pentestData.progress);

                    // Simulate technique execution
                    simulateTechniqueExecution(phaseIndex);

                    phaseIndex++;
                } else {
                    completePenetrationTest();
                }
            }, 3000);
        }

        function simulateTechniqueExecution(phaseIndex) {
            const techniques = [
                ['sqlInjectionStatus', 'xssStatus'],
                ['portScanStatus', 'serviceEnumStatus'],
                ['vulnScanStatus', 'authBypassStatus'],
                ['csrfStatus', 'sniffingStatus'],
                ['phishingStatus', 'pretextingStatus'],
                ['lockPickingStatus', 'badgeCloningStatus']
            ];

            if (techniques[phaseIndex]) {
                techniques[phaseIndex].forEach((techniqueId, index) => {
                    setTimeout(() => {
                        const element = document.getElementById(techniqueId);
                        if (element) {
                            element.textContent = 'RUNNING';
                            element.className = 'technique-status running';

                            setTimeout(() => {
                                const success = Math.random() > 0.3; // 70% success rate
                                element.textContent = success ? 'SUCCESS' : 'FAILED';
                                element.className = `technique-status ${success ? 'success' : 'failed'}`;

                                if (success && Math.random() > 0.5) {
                                    generateFinding(techniqueId);
                                }
                            }, 1500);
                        }
                    }, index * 500);
                });
            }
        }

        function generateFinding(techniqueId) {
            const findingTemplates = {
                sqlInjectionStatus: {
                    title: 'SQL Injection Vulnerability',
                    description: 'SQL injection vulnerability found in user input field',
                    severity: 'critical'
                },
                xssStatus: {
                    title: 'Cross-Site Scripting',
                    description: 'Stored XSS vulnerability in comment system',
                    severity: 'high'
                },
                authBypassStatus: {
                    title: 'Authentication Bypass',
                    description: 'Authentication mechanism can be bypassed',
                    severity: 'critical'
                },
                csrfStatus: {
                    title: 'CSRF Vulnerability',
                    description: 'Missing CSRF protection on state-changing operations',
                    severity: 'medium'
                }
            };

            const template = findingTemplates[techniqueId];
            if (template) {
                pentestData.findings.push({
                    id: 'finding_' + Date.now(),
                    ...template,
                    timestamp: new Date()
                });

                pentestData.testResults[template.severity]++;
                pentestData.testResults.completed++;

                updateTestResults();
                displayFindings();
            }
        }

        function completePenetrationTest() {
            clearInterval(pentestInterval);
            pentestData.isRunning = false;

            updatePhaseStatus(6, 'completed');
            updatePentestUI(false);

            document.getElementById('pentestStatus').textContent = 'Penetration Test Completed';
            document.getElementById('pentestStatus').className = 'pentest-status completed';

            window.notificationManager.success(`Penetration test completed! Found ${pentestData.findings.length} findings.`);
        }

        function pausePenetrationTest() {
            if (!pentestData.isRunning) return;

            clearInterval(pentestInterval);
            pentestData.isRunning = false;

            document.getElementById('pentestStatus').textContent = 'Penetration Test Paused';
            document.getElementById('pentestStatus').className = 'pentest-status ready';

            updatePentestUI(false);
            window.notificationManager.warning('Penetration test paused');
        }

        function stopPenetrationTest() {
            if (!pentestData.isRunning) return;

            clearInterval(pentestInterval);
            pentestData.isRunning = false;

            // Reset all phases
            for (let i = 1; i <= 6; i++) {
                updatePhaseStatus(i, '');
            }

            document.getElementById('pentestStatus').textContent = 'Penetration Test Stopped';
            document.getElementById('pentestStatus').className = 'pentest-status ready';

            updatePentestUI(false);
            window.notificationManager.error('Penetration test stopped');
        }

        function updatePentestUI(running) {
            const startBtn = document.getElementById('startBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const stopBtn = document.getElementById('stopBtn');
            const progressIndicator = document.getElementById('progressIndicator');

            startBtn.disabled = running;
            pauseBtn.disabled = !running;
            stopBtn.disabled = !running;
            progressIndicator.style.display = running ? 'block' : 'none';

            if (running) {
                progressIndicator.classList.add('active');
                document.getElementById('pentestStatus').textContent = 'Penetration Test Running';
                document.getElementById('pentestStatus').className = 'pentest-status running';
            } else {
                progressIndicator.classList.remove('active');
                pentestData.progress = 0;
                updateProgressBar(0);
            }
        }

        function updatePhaseStatus(phaseNumber, status) {
            const phaseElement = document.getElementById(`phase${phaseNumber}`);
            const numberElement = phaseElement.querySelector('.phase-number');

            // Remove all status classes
            phaseElement.classList.remove('active', 'completed');
            numberElement.classList.remove('active', 'completed');

            // Add new status class
            if (status) {
                phaseElement.classList.add(status);
                numberElement.classList.add(status);
            }
        }

        function updateProgressBar(progress) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress + '%';
            progressFill.textContent = progress + '%';
        }

        // Test Results Management
        function updateTestResults() {
            document.getElementById('criticalFindings').textContent = pentestData.testResults.critical;
            document.getElementById('highFindings').textContent = pentestData.testResults.high;
            document.getElementById('mediumFindings').textContent = pentestData.testResults.medium;
            document.getElementById('testsCompleted').textContent = pentestData.testResults.completed;
        }

        function displayFindings() {
            const container = document.getElementById('findingsList');

            if (pentestData.findings.length === 0) {
                container.innerHTML = `
                    <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                        No findings yet. Start a penetration test to see results.
                    </p>
                `;
                return;
            }

            const findingsHTML = pentestData.findings.map(finding => `
                <div class="finding-item">
                    <div class="finding-info">
                        <div class="finding-title">${finding.title}</div>
                        <div class="finding-description">${finding.description}</div>
                    </div>
                    <div class="finding-severity ${finding.severity}">${finding.severity.toUpperCase()}</div>
                </div>
            `).join('');

            container.innerHTML = findingsHTML;
        }

        function loadSampleFindings() {
            pentestData.findings = [
                {
                    id: 'finding_001',
                    title: 'SQL Injection in Login Form',
                    description: 'Authentication bypass possible through SQL injection',
                    severity: 'critical',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30)
                },
                {
                    id: 'finding_002',
                    title: 'Stored XSS in Comments',
                    description: 'User input not properly sanitized in comment system',
                    severity: 'high',
                    timestamp: new Date(Date.now() - 1000 * 60 * 45)
                },
                {
                    id: 'finding_003',
                    title: 'Missing CSRF Protection',
                    description: 'State-changing operations lack CSRF tokens',
                    severity: 'medium',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60)
                }
            ];

            pentestData.testResults = {
                critical: 1,
                high: 1,
                medium: 1,
                completed: 3
            };

            updateTestResults();
            displayFindings();
        }

        // Quick Test Functions
        function startFullPentest() {
            startPenetrationTest();
        }

        async function runWebAppTest() {
            window.notificationManager.info('Running web application security test...');

            try {
                const response = await window.apiClient.request('POST', '/security/pentest/webapp');

                if (response.success) {
                    window.notificationManager.success('Web application test completed');
                } else {
                    window.notificationManager.error(response.error || 'Web app test failed');
                }

                showResponse('pentestResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate web app test
                setTimeout(() => {
                    window.notificationManager.success('Web application test completed (simulated)');

                    showResponse('pentestResponse', {
                        success: true,
                        data: {
                            vulnerabilities: ['SQL Injection', 'XSS', 'CSRF'],
                            endpoints_tested: 25,
                            vulnerable_endpoints: 3
                        },
                        message: 'Mock web application test completed (endpoint may not be available)'
                    }, 'warning');
                }, 3000);
            }
        }

        async function runNetworkTest() {
            window.notificationManager.info('Running network security test...');

            try {
                const response = await window.apiClient.request('POST', '/security/pentest/network');

                if (response.success) {
                    window.notificationManager.success('Network security test completed');
                } else {
                    window.notificationManager.error(response.error || 'Network test failed');
                }

                showResponse('pentestResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate network test
                setTimeout(() => {
                    window.notificationManager.success('Network security test completed (simulated)');

                    showResponse('pentestResponse', {
                        success: true,
                        data: {
                            open_ports: [22, 80, 443, 3000],
                            services: ['SSH', 'HTTP', 'HTTPS', 'Node.js'],
                            vulnerabilities: ['Weak SSH configuration']
                        },
                        message: 'Mock network test completed (endpoint may not be available)'
                    }, 'warning');
                }, 2500);
            }
        }

        function generateReport() {
            window.notificationManager.info('Generating penetration test report...');

            const reportData = {
                timestamp: new Date().toISOString(),
                testType: 'Comprehensive Penetration Test',
                findings: pentestData.findings,
                summary: pentestData.testResults,
                recommendations: [
                    'Implement input validation and parameterized queries',
                    'Add proper output encoding and CSP headers',
                    'Implement CSRF protection tokens',
                    'Regular security assessments and code reviews'
                ]
            };

            // Simulate report generation
            setTimeout(() => {
                const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `pentest-report-${Date.now()}.json`;
                a.click();
                window.URL.revokeObjectURL(url);

                window.notificationManager.success('Penetration test report generated');
            }, 1000);
        }

        // Test Scenario Functions
        function runOWASPTest() {
            window.notificationManager.info('Running OWASP methodology test...');

            setTimeout(() => {
                window.notificationManager.success('OWASP test completed (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        owasp_categories: [
                            'A01:2021 – Broken Access Control',
                            'A02:2021 – Cryptographic Failures',
                            'A03:2021 – Injection'
                        ],
                        vulnerabilities_found: 3,
                        compliance_score: 75
                    },
                    message: 'OWASP methodology test completed'
                }, 'info');
            }, 3000);
        }

        function runRedTeamExercise() {
            window.notificationManager.info('Starting red team exercise...');

            setTimeout(() => {
                window.notificationManager.warning('Red team exercise completed - Multiple vulnerabilities exploited (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        attack_chains: [
                            'Initial access via phishing',
                            'Privilege escalation through misconfiguration',
                            'Lateral movement via credential reuse',
                            'Data exfiltration simulation'
                        ],
                        objectives_achieved: 4,
                        detection_rate: 25
                    },
                    message: 'Red team exercise completed'
                }, 'warning');
            }, 5000);
        }

        function runAPISecurityTest() {
            window.notificationManager.info('Running API security test...');

            setTimeout(() => {
                window.notificationManager.success('API security test completed (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        endpoints_tested: 15,
                        authentication_issues: 2,
                        authorization_issues: 1,
                        input_validation_issues: 3
                    },
                    message: 'API security test completed'
                }, 'info');
            }, 2500);
        }

        function runInfrastructureTest() {
            window.notificationManager.info('Running infrastructure security test...');

            setTimeout(() => {
                window.notificationManager.success('Infrastructure test completed (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        servers_scanned: 5,
                        services_identified: 12,
                        misconfigurations: 3,
                        patch_level: 'Current'
                    },
                    message: 'Infrastructure security test completed'
                }, 'info');
            }, 3000);
        }

        function runWirelessTest() {
            window.notificationManager.info('Running wireless security test...');

            setTimeout(() => {
                window.notificationManager.success('Wireless security test completed (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        networks_found: 8,
                        encryption_types: ['WPA2', 'WPA3', 'Open'],
                        weak_passwords: 2,
                        rogue_access_points: 0
                    },
                    message: 'Wireless security test completed'
                }, 'info');
            }, 2000);
        }

        function runSocialEngineeringTest() {
            window.notificationManager.info('Running social engineering test...');

            setTimeout(() => {
                window.notificationManager.warning('Social engineering test completed - High susceptibility detected (simulated)');

                showResponse('pentestResponse', {
                    success: true,
                    data: {
                        phishing_success_rate: 35,
                        employees_tested: 50,
                        security_awareness_score: 65,
                        recommendations: ['Security awareness training needed']
                    },
                    message: 'Social engineering test completed'
                }, 'warning');
            }, 2500);
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
