<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Threat Monitoring Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .threat-container {
            max-width: 1400px;
            margin: 2rem auto;
        }

        .threat-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .threat-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .threat-stat {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .threat-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .threat-stat.critical::before {
            background: var(--error-color);
        }

        .threat-stat.high::before {
            background: var(--warning-color);
        }

        .threat-stat.medium::before {
            background: var(--info-color);
        }

        .threat-stat.low::before {
            background: var(--success-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-number.critical {
            color: var(--error-color);
        }

        .stat-number.high {
            color: var(--warning-color);
        }

        .stat-number.medium {
            color: var(--info-color);
        }

        .stat-number.low {
            color: var(--success-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .threat-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .threat-section {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .threat-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .threat-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .threat-item:last-child {
            border-bottom: none;
        }

        .threat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
            font-size: 1.2rem;
        }

        .threat-icon.critical {
            background: rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
        }

        .threat-icon.high {
            background: rgba(var(--warning-color-rgb), 0.2);
            color: var(--warning-color);
        }

        .threat-icon.medium {
            background: rgba(var(--info-color-rgb), 0.2);
            color: var(--info-color);
        }

        .threat-icon.low {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .threat-content {
            flex: 1;
        }

        .threat-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .threat-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .threat-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .threat-severity {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 1rem;
        }

        .threat-severity.critical {
            background: var(--error-color);
            color: white;
        }

        .threat-severity.high {
            background: var(--warning-color);
            color: white;
        }

        .threat-severity.medium {
            background: var(--info-color);
            color: white;
        }

        .threat-severity.low {
            background: var(--success-color);
            color: white;
        }

        .threat-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.investigate {
            background: var(--info-color);
            color: white;
        }

        .action-btn.block {
            background: var(--error-color);
            color: white;
        }

        .action-btn.dismiss {
            background: var(--text-secondary);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .threat-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .filter-btn:hover {
            background: var(--hover-background);
        }

        .threat-map {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .map-placeholder {
            height: 200px;
            background: var(--card-background);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .ip-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .ip-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .ip-item:last-child {
            border-bottom: none;
        }

        .ip-address {
            font-family: monospace;
            font-weight: bold;
        }

        .ip-country {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .ip-threat-level {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .ip-threat-level.high {
            background: var(--error-color);
            color: white;
        }

        .ip-threat-level.medium {
            background: var(--warning-color);
            color: white;
        }

        .ip-threat-level.low {
            background: var(--success-color);
            color: white;
        }

        @media (max-width: 1024px) {
            .threat-sections {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .threat-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-eye"></i>
                    <h1>Threat Monitoring Testing</h1>
                    <span class="subtitle">Real-time Threat Detection and Monitoring Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="threat-container">
                <!-- Threat Overview -->
                <div class="threat-overview">
                    <h2>Threat Monitoring Overview</h2>
                    <div class="threat-stats">
                        <div class="threat-stat critical">
                            <div class="stat-number critical" id="criticalThreats">0</div>
                            <div class="stat-label">Critical Threats</div>
                        </div>
                        <div class="threat-stat high">
                            <div class="stat-number high" id="highThreats">0</div>
                            <div class="stat-label">High Priority</div>
                        </div>
                        <div class="threat-stat medium">
                            <div class="stat-number medium" id="mediumThreats">0</div>
                            <div class="stat-label">Medium Priority</div>
                        </div>
                        <div class="threat-stat low">
                            <div class="stat-number low" id="lowThreats">0</div>
                            <div class="stat-label">Low Priority</div>
                        </div>
                        <div class="threat-stat">
                            <div class="stat-number" id="blockedIPs">0</div>
                            <div class="stat-label">Blocked IPs</div>
                        </div>
                        <div class="threat-stat">
                            <div class="stat-number" id="activeMonitors">0</div>
                            <div class="stat-label">Active Monitors</div>
                        </div>
                    </div>
                </div>

                <!-- Threat Sections -->
                <div class="threat-sections">
                    <!-- Active Threats -->
                    <div class="threat-section">
                        <div class="section-header">
                            <div class="section-title">Active Threats</div>
                            <button class="btn btn-sm btn-primary" onclick="refreshThreats()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="threat-filters">
                            <button class="filter-btn active" onclick="filterThreats('all')">All</button>
                            <button class="filter-btn" onclick="filterThreats('critical')">Critical</button>
                            <button class="filter-btn" onclick="filterThreats('high')">High</button>
                            <button class="filter-btn" onclick="filterThreats('medium')">Medium</button>
                            <button class="filter-btn" onclick="filterThreats('low')">Low</button>
                        </div>
                        <div class="threat-list" id="threatList">
                            <!-- Threats will be populated here -->
                        </div>
                    </div>

                    <!-- Threat Intelligence -->
                    <div class="threat-section">
                        <div class="section-header">
                            <div class="section-title">Threat Intelligence</div>
                        </div>

                        <!-- Threat Map -->
                        <div class="threat-map">
                            <h4>Geographic Threat Distribution</h4>
                            <div class="map-placeholder">
                                <i class="fas fa-globe" style="margin-right: 0.5rem;"></i>
                                Interactive Threat Map
                            </div>
                        </div>

                        <!-- Suspicious IPs -->
                        <div>
                            <h4>Suspicious IP Addresses</h4>
                            <div class="ip-list" id="suspiciousIPs">
                                <!-- Suspicious IPs will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="threat-section">
                    <div class="section-header">
                        <div class="section-title">Quick Actions</div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <button class="btn btn-primary" onclick="runThreatScan()">
                            <i class="fas fa-search"></i>
                            Run Threat Scan
                        </button>
                        <button class="btn btn-warning" onclick="updateThreatDatabase()">
                            <i class="fas fa-database"></i>
                            Update Threat DB
                        </button>
                        <button class="btn btn-info" onclick="exportThreatReport()">
                            <i class="fas fa-file-export"></i>
                            Export Report
                        </button>
                        <button class="btn btn-danger" onclick="emergencyBlock()">
                            <i class="fas fa-ban"></i>
                            Emergency Block
                        </button>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="threatResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Threat Monitoring Testing Interface</p>
                    <p>Real-time threat detection and monitoring</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let threatData = {
            threats: [],
            filteredThreats: [],
            suspiciousIPs: [],
            stats: {
                criticalThreats: 0,
                highThreats: 0,
                mediumThreats: 0,
                lowThreats: 0,
                blockedIPs: 0,
                activeMonitors: 0
            },
            currentFilter: 'all'
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await checkServerHealth();
            updateAuthStatus();
            initializeTheme();
            await loadThreatData();
            startRealTimeUpdates();
        }

        // Threat Data Functions
        async function loadThreatData() {
            window.notificationManager.info('Loading threat data...');

            try {
                const response = await window.apiClient.request('GET', '/security/threats');

                if (response.success) {
                    updateThreatData(response.data);
                    window.notificationManager.success('Threat data loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load threat data');
                }

                showResponse('threatResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                loadMockThreatData();
                window.notificationManager.success('Threat data loaded successfully (simulated)');

                showResponse('threatResponse', {
                    success: true,
                    data: threatData,
                    message: 'Mock threat data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockThreatData() {
            const mockThreats = [
                {
                    id: 'threat_001',
                    title: 'SQL Injection Attempt',
                    description: 'Multiple SQL injection attempts detected from IP *************',
                    severity: 'critical',
                    source: '*************',
                    country: 'Unknown',
                    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    status: 'active'
                },
                {
                    id: 'threat_002',
                    title: 'Brute Force Attack',
                    description: 'Failed login attempts exceeding threshold from multiple IPs',
                    severity: 'high',
                    source: '************',
                    country: 'Russia',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    status: 'active'
                },
                {
                    id: 'threat_003',
                    title: 'Suspicious User Agent',
                    description: 'Automated bot detected with suspicious user agent string',
                    severity: 'medium',
                    source: '*************',
                    country: 'China',
                    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
                    status: 'investigating'
                },
                {
                    id: 'threat_004',
                    title: 'Rate Limit Exceeded',
                    description: 'Client exceeded rate limits multiple times',
                    severity: 'low',
                    source: '***********',
                    country: 'USA',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                    status: 'resolved'
                },
                {
                    id: 'threat_005',
                    title: 'XSS Attack Vector',
                    description: 'Cross-site scripting attempt in form submission',
                    severity: 'high',
                    source: '*********',
                    country: 'Brazil',
                    timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
                    status: 'blocked'
                }
            ];

            const mockSuspiciousIPs = [
                { ip: '*************', country: 'Unknown', threatLevel: 'high', attempts: 45 },
                { ip: '************', country: 'Russia', threatLevel: 'high', attempts: 32 },
                { ip: '*************', country: 'China', threatLevel: 'medium', attempts: 18 },
                { ip: '***********', country: 'USA', threatLevel: 'low', attempts: 8 },
                { ip: '*********', country: 'Brazil', threatLevel: 'medium', attempts: 12 }
            ];

            threatData.threats = mockThreats;
            threatData.suspiciousIPs = mockSuspiciousIPs;
            updateThreatDisplay();
        }

        function updateThreatData(data) {
            if (data.threats) threatData.threats = data.threats;
            if (data.suspiciousIPs) threatData.suspiciousIPs = data.suspiciousIPs;
            if (data.stats) threatData.stats = { ...threatData.stats, ...data.stats };
            updateThreatDisplay();
        }

        function updateThreatDisplay() {
            updateThreatStats();
            filterThreats(threatData.currentFilter);
            updateSuspiciousIPsDisplay();
        }

        function updateThreatStats() {
            const stats = {
                criticalThreats: threatData.threats.filter(t => t.severity === 'critical').length,
                highThreats: threatData.threats.filter(t => t.severity === 'high').length,
                mediumThreats: threatData.threats.filter(t => t.severity === 'medium').length,
                lowThreats: threatData.threats.filter(t => t.severity === 'low').length,
                blockedIPs: threatData.suspiciousIPs.length,
                activeMonitors: 12
            };

            document.getElementById('criticalThreats').textContent = stats.criticalThreats;
            document.getElementById('highThreats').textContent = stats.highThreats;
            document.getElementById('mediumThreats').textContent = stats.mediumThreats;
            document.getElementById('lowThreats').textContent = stats.lowThreats;
            document.getElementById('blockedIPs').textContent = stats.blockedIPs;
            document.getElementById('activeMonitors').textContent = stats.activeMonitors;
        }

        function filterThreats(severity) {
            threatData.currentFilter = severity;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Filter threats
            if (severity === 'all') {
                threatData.filteredThreats = threatData.threats;
            } else {
                threatData.filteredThreats = threatData.threats.filter(threat => threat.severity === severity);
            }

            updateThreatListDisplay();
        }

        function updateThreatListDisplay() {
            const threatList = document.getElementById('threatList');

            if (threatData.filteredThreats.length === 0) {
                threatList.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No threats found for the selected filter.
                    </div>
                `;
                return;
            }

            const threatsHTML = threatData.filteredThreats.map(threat => `
                <div class="threat-item">
                    <div class="threat-icon ${threat.severity}">
                        <i class="fas ${getThreatIcon(threat.title)}"></i>
                    </div>
                    <div class="threat-content">
                        <div class="threat-title">${threat.title}</div>
                        <div class="threat-description">${threat.description}</div>
                        <div class="threat-meta">
                            <span><i class="fas fa-map-marker-alt"></i> ${threat.source}</span>
                            <span><i class="fas fa-flag"></i> ${threat.country}</span>
                            <span><i class="fas fa-clock"></i> ${formatTimeAgo(threat.timestamp)}</span>
                        </div>
                        <div class="threat-actions">
                            <button class="action-btn investigate" onclick="investigateThreat('${threat.id}')">
                                <i class="fas fa-search"></i>
                                Investigate
                            </button>
                            <button class="action-btn block" onclick="blockThreat('${threat.id}')">
                                <i class="fas fa-ban"></i>
                                Block
                            </button>
                            <button class="action-btn dismiss" onclick="dismissThreat('${threat.id}')">
                                <i class="fas fa-times"></i>
                                Dismiss
                            </button>
                        </div>
                    </div>
                    <div class="threat-severity ${threat.severity}">${threat.severity.toUpperCase()}</div>
                </div>
            `).join('');

            threatList.innerHTML = threatsHTML;
        }

        function updateSuspiciousIPsDisplay() {
            const ipList = document.getElementById('suspiciousIPs');

            const ipsHTML = threatData.suspiciousIPs.map(ip => `
                <div class="ip-item">
                    <div>
                        <div class="ip-address">${ip.ip}</div>
                        <div class="ip-country">${ip.country} • ${ip.attempts} attempts</div>
                    </div>
                    <div class="ip-threat-level ${ip.threatLevel}">${ip.threatLevel.toUpperCase()}</div>
                </div>
            `).join('');

            ipList.innerHTML = ipsHTML;
        }

        // Threat Actions
        function investigateThreat(threatId) {
            const threat = threatData.threats.find(t => t.id === threatId);
            if (threat) {
                window.notificationManager.info(`Investigating threat: ${threat.title}`);
                threat.status = 'investigating';
                updateThreatListDisplay();
            }
        }

        function blockThreat(threatId) {
            const threat = threatData.threats.find(t => t.id === threatId);
            if (threat) {
                window.notificationManager.success(`Blocked threat source: ${threat.source}`);
                threat.status = 'blocked';
                updateThreatListDisplay();
            }
        }

        function dismissThreat(threatId) {
            const threat = threatData.threats.find(t => t.id === threatId);
            if (threat) {
                window.notificationManager.info(`Dismissed threat: ${threat.title}`);
                threatData.threats = threatData.threats.filter(t => t.id !== threatId);
                updateThreatDisplay();
            }
        }

        // Quick Actions
        function runThreatScan() {
            window.notificationManager.info('Running comprehensive threat scan...');
            setTimeout(() => {
                window.notificationManager.success('Threat scan completed - 3 new threats detected');
            }, 2000);
        }

        function updateThreatDatabase() {
            window.notificationManager.info('Updating threat intelligence database...');
            setTimeout(() => {
                window.notificationManager.success('Threat database updated successfully');
            }, 3000);
        }

        function exportThreatReport() {
            window.notificationManager.info('Generating threat report...');
            const reportData = {
                timestamp: new Date().toISOString(),
                threats: threatData.threats,
                suspiciousIPs: threatData.suspiciousIPs,
                stats: threatData.stats
            };
            const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `threat-report-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);
            window.notificationManager.success('Threat report exported successfully');
        }

        function emergencyBlock() {
            if (confirm('This will block all suspicious IP addresses immediately. Continue?')) {
                window.notificationManager.warning('Emergency block activated - All suspicious IPs blocked');
            }
        }

        function refreshThreats() {
            loadThreatData();
        }

        // Utility Functions
        function getThreatIcon(title) {
            if (title.includes('SQL')) return 'fa-database';
            if (title.includes('Brute Force')) return 'fa-hammer';
            if (title.includes('XSS')) return 'fa-code';
            if (title.includes('Bot')) return 'fa-robot';
            return 'fa-exclamation-triangle';
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            return `${hours}h ago`;
        }

        function startRealTimeUpdates() {
            setInterval(() => {
                if (Math.random() < 0.1) {
                    // Simulate new threat
                    const newThreat = {
                        id: 'threat_' + Date.now(),
                        title: 'New Suspicious Activity',
                        description: 'Automated threat detection triggered',
                        severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
                        source: `192.168.1.${Math.floor(Math.random() * 255)}`,
                        country: ['USA', 'China', 'Russia', 'Brazil'][Math.floor(Math.random() * 4)],
                        timestamp: new Date(),
                        status: 'active'
                    };
                    threatData.threats.unshift(newThreat);
                    if (threatData.threats.length > 20) threatData.threats = threatData.threats.slice(0, 20);
                    updateThreatDisplay();
                }
            }, 10000);
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else throw new Error('Server error');
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');
            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');
            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
