<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Scanner Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .scanner-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .scanner-control {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .scan-button {
            font-size: 1.2rem;
            padding: 1rem 2rem;
            margin: 0.5rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
        }

        .scan-button.primary {
            background: var(--primary-color);
            color: white;
        }

        .scan-button.primary:hover {
            background: var(--primary-color-dark);
            transform: translateY(-2px);
        }

        .scan-button.danger {
            background: var(--error-color);
            color: white;
        }

        .scan-button.danger:hover {
            background: var(--error-color-dark);
            transform: translateY(-2px);
        }

        .scan-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .scan-status {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }

        .scan-status.scanning {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .scan-progress {
            width: 100%;
            height: 20px;
            background: var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-bar {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .scan-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .vulnerability-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.2s ease;
        }

        .summary-card:hover {
            transform: translateY(-2px);
        }

        .summary-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .summary-number.critical {
            color: var(--error-color);
        }

        .summary-number.high {
            color: #dc3545;
        }

        .summary-number.medium {
            color: var(--warning-color);
        }

        .summary-number.low {
            color: #17a2b8;
        }

        .summary-number.info {
            color: var(--info-color);
        }

        .summary-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .scan-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scan-type-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scan-type-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .scan-type-icon {
            font-size: 2rem;
            margin-right: 1rem;
            width: 50px;
            text-align: center;
            color: var(--primary-color);
        }

        .scan-type-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .scan-type-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .scan-type-actions {
            margin-top: 1rem;
        }

        .vulnerability-list {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .vulnerability-item {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: transform 0.2s ease;
        }

        .vulnerability-item:hover {
            transform: translateY(-1px);
        }

        .vulnerability-item:last-child {
            margin-bottom: 0;
        }

        .vulnerability-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .vulnerability-title {
            font-weight: bold;
            color: var(--text-primary);
        }

        .vulnerability-severity {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
        }

        .vulnerability-severity.critical {
            background: var(--error-color);
            color: white;
        }

        .vulnerability-severity.high {
            background: #dc3545;
            color: white;
        }

        .vulnerability-severity.medium {
            background: var(--warning-color);
            color: white;
        }

        .vulnerability-severity.low {
            background: #17a2b8;
            color: white;
        }

        .vulnerability-details {
            margin-bottom: 1rem;
        }

        .vulnerability-description {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .vulnerability-location {
            font-family: monospace;
            font-size: 0.8rem;
            background: var(--border-color);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        .vulnerability-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .scan-history {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 2rem;
        }

        .history-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-info {
            flex: 1;
        }

        .history-type {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .history-details {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .history-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .history-results {
            text-align: center;
            margin: 0 1rem;
        }

        .history-count {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--error-color);
        }

        .filter-controls {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .filter-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-bug"></i>
                    <h1>Vulnerability Scanner Testing</h1>
                    <span class="subtitle">Vulnerability Scanner and Security Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="scanner-container">
                <!-- Scanner Control -->
                <div class="scanner-control">
                    <h2>Vulnerability Scanner</h2>
                    <p>Comprehensive security vulnerability scanning and assessment</p>
                    
                    <div>
                        <button class="scan-button primary" id="startScanBtn" onclick="startComprehensiveScan()">
                            <i class="fas fa-play"></i>
                            Start Comprehensive Scan
                        </button>
                        <button class="scan-button danger" id="stopScanBtn" onclick="stopScan()" disabled>
                            <i class="fas fa-stop"></i>
                            Stop Scan
                        </button>
                    </div>

                    <div class="scan-status" id="scanStatus" style="display: none;">
                        <div class="scan-spinner"></div>
                        <span id="scanStatusText">Initializing scan...</span>
                        <div class="scan-progress">
                            <div class="progress-bar" id="progressBar" style="width: 0%;">0%</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="quickSQLInjectionScan()">
                        <i class="fas fa-database"></i>
                        SQL Injection Scan
                    </button>
                    <button class="btn btn-warning" onclick="quickXSSScan()">
                        <i class="fas fa-code"></i>
                        XSS Vulnerability Scan
                    </button>
                    <button class="btn btn-danger" onclick="quickCSRFScan()">
                        <i class="fas fa-shield-alt"></i>
                        CSRF Protection Scan
                    </button>
                    <button class="btn btn-info" onclick="quickAuthScan()">
                        <i class="fas fa-lock"></i>
                        Authentication Scan
                    </button>
                </div>

                <!-- Vulnerability Summary -->
                <div class="vulnerability-summary">
                    <div class="summary-card">
                        <div class="summary-number critical" id="criticalCount">0</div>
                        <div class="summary-label">Critical</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number high" id="highCount">0</div>
                        <div class="summary-label">High</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number medium" id="mediumCount">0</div>
                        <div class="summary-label">Medium</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number low" id="lowCount">0</div>
                        <div class="summary-label">Low</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number info" id="infoCount">0</div>
                        <div class="summary-label">Info</div>
                    </div>
                </div>

                <!-- Scan Types -->
                <div class="scan-types">
                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">SQL Injection</div>
                                <div class="scan-type-description">Test for SQL injection vulnerabilities</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-primary" onclick="runSQLInjectionScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>

                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">Cross-Site Scripting (XSS)</div>
                                <div class="scan-type-description">Detect XSS vulnerabilities</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-warning" onclick="runXSSScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>

                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">CSRF Protection</div>
                                <div class="scan-type-description">Test CSRF protection mechanisms</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-danger" onclick="runCSRFScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>

                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">Authentication</div>
                                <div class="scan-type-description">Test authentication mechanisms</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-info" onclick="runAuthenticationScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>

                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-user-secret"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">Session Management</div>
                                <div class="scan-type-description">Analyze session security</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-secondary" onclick="runSessionScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>

                    <div class="scan-type-card">
                        <div class="scan-type-header">
                            <div class="scan-type-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div>
                                <div class="scan-type-title">Network Security</div>
                                <div class="scan-type-description">Test network-level security</div>
                            </div>
                        </div>
                        <div class="scan-type-actions">
                            <button class="btn btn-sm btn-success" onclick="runNetworkScan()">
                                <i class="fas fa-play"></i>
                                Run Scan
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="filter-controls">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>Severity:</label>
                            <select class="filter-select" id="severityFilter" onchange="filterVulnerabilities()">
                                <option value="all">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Type:</label>
                            <select class="filter-select" id="typeFilter" onchange="filterVulnerabilities()">
                                <option value="all">All Types</option>
                                <option value="sql-injection">SQL Injection</option>
                                <option value="xss">Cross-Site Scripting</option>
                                <option value="csrf">CSRF</option>
                                <option value="auth">Authentication</option>
                                <option value="session">Session Management</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="btn btn-sm btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                                Clear Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Vulnerability List -->
                <div class="vulnerability-list" id="vulnerabilityList">
                    <h3>Detected Vulnerabilities</h3>
                    <div id="vulnerabilityItems">
                        <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                            No vulnerabilities detected. Run a scan to identify security issues.
                        </p>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">OWASP Top 10 Scan</div>
                        <div class="scenario-description">Comprehensive scan for OWASP Top 10 vulnerabilities</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="runOWASPScan()">
                                <i class="fas fa-list"></i>
                                Run OWASP Scan
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">API Security Scan</div>
                        <div class="scenario-description">Test API endpoints for security vulnerabilities</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="runAPIScan()">
                                <i class="fas fa-cogs"></i>
                                Run API Scan
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Input Validation Scan</div>
                        <div class="scenario-description">Test input validation and sanitization</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="runInputValidationScan()">
                                <i class="fas fa-keyboard"></i>
                                Run Input Scan
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Configuration Scan</div>
                        <div class="scenario-description">Check for security misconfigurations</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-secondary" onclick="runConfigurationScan()">
                                <i class="fas fa-cog"></i>
                                Run Config Scan
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Scan History -->
                <div class="scan-history" id="scanHistory">
                    <div class="history-header">Scan History</div>
                    <!-- History items will be populated here -->
                </div>

                <!-- Response Viewer -->
                <div id="scannerResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Vulnerability Scanner Testing Interface</p>
                    <p>Security vulnerability scanning and assessment testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let scanData = {
            isScanning: false,
            vulnerabilities: [],
            scanHistory: [],
            currentScanId: null
        };

        let scanProgress = 0;
        let scanInterval = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load scan history
            loadScanHistory();

            // Load sample vulnerabilities for demo
            loadSampleVulnerabilities();
        }

        // Main Scan Functions
        async function startComprehensiveScan() {
            if (scanData.isScanning) {
                window.notificationManager.warning('Scan already in progress');
                return;
            }

            scanData.isScanning = true;
            scanData.currentScanId = 'scan_' + Date.now();

            updateScanUI(true);
            window.notificationManager.info('Starting comprehensive vulnerability scan...');

            try {
                const response = await window.apiClient.request('POST', '/security/scan/comprehensive');

                if (response.success) {
                    // Real scan would be handled here
                    simulateComprehensiveScan();
                } else {
                    throw new Error(response.error || 'Scan failed to start');
                }

                showResponse('scannerResponse', response, 'success');
            } catch (error) {
                // Simulate comprehensive scan
                simulateComprehensiveScan();

                showResponse('scannerResponse', {
                    success: true,
                    data: { scanId: scanData.currentScanId },
                    message: 'Mock comprehensive scan started (endpoint may not be available)'
                }, 'warning');
            }
        }

        function simulateComprehensiveScan() {
            const scanSteps = [
                'Initializing scan...',
                'Scanning for SQL injection vulnerabilities...',
                'Testing for XSS vulnerabilities...',
                'Checking CSRF protection...',
                'Analyzing authentication mechanisms...',
                'Testing session management...',
                'Scanning network security...',
                'Generating vulnerability report...',
                'Scan completed!'
            ];

            let stepIndex = 0;
            scanProgress = 0;

            scanInterval = setInterval(() => {
                if (stepIndex < scanSteps.length) {
                    document.getElementById('scanStatusText').textContent = scanSteps[stepIndex];
                    scanProgress = Math.floor(((stepIndex + 1) / scanSteps.length) * 100);
                    updateProgressBar(scanProgress);
                    stepIndex++;
                } else {
                    completeScan();
                }
            }, 2000);
        }

        function completeScan() {
            clearInterval(scanInterval);
            scanData.isScanning = false;

            // Generate mock vulnerabilities
            generateMockVulnerabilities();

            // Add to scan history
            addScanToHistory('Comprehensive Scan', scanData.vulnerabilities.length);

            updateScanUI(false);
            updateVulnerabilitySummary();
            displayVulnerabilities();

            window.notificationManager.success(`Scan completed! Found ${scanData.vulnerabilities.length} vulnerabilities.`);
        }

        function stopScan() {
            if (!scanData.isScanning) {
                return;
            }

            clearInterval(scanInterval);
            scanData.isScanning = false;
            updateScanUI(false);

            window.notificationManager.warning('Scan stopped by user');
        }

        function updateScanUI(scanning) {
            const startBtn = document.getElementById('startScanBtn');
            const stopBtn = document.getElementById('stopScanBtn');
            const scanStatus = document.getElementById('scanStatus');

            startBtn.disabled = scanning;
            stopBtn.disabled = !scanning;
            scanStatus.style.display = scanning ? 'block' : 'none';

            if (scanning) {
                scanStatus.classList.add('scanning');
            } else {
                scanStatus.classList.remove('scanning');
                scanProgress = 0;
                updateProgressBar(0);
            }
        }

        function updateProgressBar(progress) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
        }

        // Vulnerability Management Functions
        function generateMockVulnerabilities() {
            scanData.vulnerabilities = [
                {
                    id: 'vuln_001',
                    title: 'SQL Injection in User Search',
                    severity: 'critical',
                    type: 'sql-injection',
                    description: 'User input in search functionality is not properly sanitized, allowing SQL injection attacks.',
                    location: '/api/users/search?q=',
                    recommendation: 'Use parameterized queries and input validation.',
                    cve: 'CVE-2023-1234'
                },
                {
                    id: 'vuln_002',
                    title: 'Stored XSS in Comments',
                    severity: 'high',
                    type: 'xss',
                    description: 'User comments are not properly encoded, allowing stored XSS attacks.',
                    location: '/api/comments/create',
                    recommendation: 'Implement proper output encoding and Content Security Policy.',
                    cve: null
                },
                {
                    id: 'vuln_003',
                    title: 'Missing CSRF Protection',
                    severity: 'medium',
                    type: 'csrf',
                    description: 'State-changing operations lack CSRF protection tokens.',
                    location: '/api/profile/update',
                    recommendation: 'Implement CSRF tokens for all state-changing operations.',
                    cve: null
                },
                {
                    id: 'vuln_004',
                    title: 'Weak Session Configuration',
                    severity: 'medium',
                    type: 'session',
                    description: 'Session cookies lack secure flags and have extended expiration.',
                    location: 'Session Management',
                    recommendation: 'Configure session cookies with Secure, HttpOnly, and SameSite flags.',
                    cve: null
                },
                {
                    id: 'vuln_005',
                    title: 'Information Disclosure',
                    severity: 'low',
                    type: 'info-disclosure',
                    description: 'Error messages reveal sensitive system information.',
                    location: '/api/auth/login',
                    recommendation: 'Implement generic error messages that don\'t reveal system details.',
                    cve: null
                }
            ];
        }

        function updateVulnerabilitySummary() {
            const counts = {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0,
                info: 0
            };

            scanData.vulnerabilities.forEach(vuln => {
                if (counts.hasOwnProperty(vuln.severity)) {
                    counts[vuln.severity]++;
                }
            });

            document.getElementById('criticalCount').textContent = counts.critical;
            document.getElementById('highCount').textContent = counts.high;
            document.getElementById('mediumCount').textContent = counts.medium;
            document.getElementById('lowCount').textContent = counts.low;
            document.getElementById('infoCount').textContent = counts.info;
        }

        function displayVulnerabilities() {
            const container = document.getElementById('vulnerabilityItems');

            if (scanData.vulnerabilities.length === 0) {
                container.innerHTML = `
                    <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                        No vulnerabilities detected. Run a scan to identify security issues.
                    </p>
                `;
                return;
            }

            const vulnerabilityHTML = scanData.vulnerabilities.map(vuln => `
                <div class="vulnerability-item" data-severity="${vuln.severity}" data-type="${vuln.type}">
                    <div class="vulnerability-header">
                        <div class="vulnerability-title">${vuln.title}</div>
                        <div class="vulnerability-severity ${vuln.severity}">${vuln.severity.toUpperCase()}</div>
                    </div>
                    <div class="vulnerability-details">
                        <div class="vulnerability-description">${vuln.description}</div>
                        <div class="vulnerability-location">${vuln.location}</div>
                    </div>
                    <div class="vulnerability-actions">
                        <button class="btn btn-sm btn-info" onclick="viewVulnerabilityDetails('${vuln.id}')">
                            <i class="fas fa-eye"></i>
                            View Details
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="markAsFixed('${vuln.id}')">
                            <i class="fas fa-check"></i>
                            Mark as Fixed
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="exportVulnerability('${vuln.id}')">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = vulnerabilityHTML;
        }

        function filterVulnerabilities() {
            const severityFilter = document.getElementById('severityFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const vulnerabilityItems = document.querySelectorAll('.vulnerability-item');

            vulnerabilityItems.forEach(item => {
                const severity = item.getAttribute('data-severity');
                const type = item.getAttribute('data-type');

                const severityMatch = severityFilter === 'all' || severity === severityFilter;
                const typeMatch = typeFilter === 'all' || type === typeFilter;

                if (severityMatch && typeMatch) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function clearFilters() {
            document.getElementById('severityFilter').value = 'all';
            document.getElementById('typeFilter').value = 'all';
            filterVulnerabilities();
        }

        function viewVulnerabilityDetails(vulnId) {
            const vulnerability = scanData.vulnerabilities.find(v => v.id === vulnId);
            if (!vulnerability) return;

            showResponse('scannerResponse', {
                success: true,
                data: vulnerability,
                message: 'Vulnerability details'
            }, 'info');

            window.notificationManager.info(`Viewing details for: ${vulnerability.title}`);
        }

        function markAsFixed(vulnId) {
            const vulnIndex = scanData.vulnerabilities.findIndex(v => v.id === vulnId);
            if (vulnIndex === -1) return;

            scanData.vulnerabilities.splice(vulnIndex, 1);
            updateVulnerabilitySummary();
            displayVulnerabilities();

            window.notificationManager.success('Vulnerability marked as fixed');
        }

        function exportVulnerability(vulnId) {
            const vulnerability = scanData.vulnerabilities.find(v => v.id === vulnId);
            if (!vulnerability) return;

            const exportData = {
                ...vulnerability,
                exportedAt: new Date().toISOString(),
                scanId: scanData.currentScanId
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `vulnerability-${vulnId}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Vulnerability exported');
        }

        // Specific Scan Functions
        async function runSQLInjectionScan() {
            window.notificationManager.info('Running SQL injection scan...');

            try {
                const response = await window.apiClient.request('POST', '/security/scan/sql-injection');

                if (response.success) {
                    window.notificationManager.success('SQL injection scan completed');
                    addScanToHistory('SQL Injection Scan', response.data.vulnerabilities?.length || 1);
                } else {
                    window.notificationManager.error(response.error || 'SQL injection scan failed');
                }

                showResponse('scannerResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate SQL injection scan
                setTimeout(() => {
                    window.notificationManager.success('SQL injection scan completed (simulated)');
                    addScanToHistory('SQL Injection Scan', 1);

                    showResponse('scannerResponse', {
                        success: true,
                        data: {
                            vulnerabilities: [{ type: 'sql-injection', severity: 'critical' }],
                            endpoints_tested: 15,
                            vulnerable_endpoints: 1
                        },
                        message: 'Mock SQL injection scan completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runXSSScan() {
            window.notificationManager.info('Running XSS vulnerability scan...');

            try {
                const response = await window.apiClient.request('POST', '/security/scan/xss');

                if (response.success) {
                    window.notificationManager.success('XSS scan completed');
                    addScanToHistory('XSS Scan', response.data.vulnerabilities?.length || 1);
                } else {
                    window.notificationManager.error(response.error || 'XSS scan failed');
                }

                showResponse('scannerResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate XSS scan
                setTimeout(() => {
                    window.notificationManager.success('XSS scan completed (simulated)');
                    addScanToHistory('XSS Scan', 1);

                    showResponse('scannerResponse', {
                        success: true,
                        data: {
                            vulnerabilities: [{ type: 'xss', severity: 'high' }],
                            forms_tested: 8,
                            vulnerable_forms: 1
                        },
                        message: 'Mock XSS scan completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runCSRFScan() {
            window.notificationManager.info('Running CSRF protection scan...');

            try {
                const response = await window.apiClient.request('POST', '/security/scan/csrf');

                if (response.success) {
                    window.notificationManager.success('CSRF scan completed');
                    addScanToHistory('CSRF Scan', response.data.vulnerabilities?.length || 1);
                } else {
                    window.notificationManager.error(response.error || 'CSRF scan failed');
                }

                showResponse('scannerResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate CSRF scan
                setTimeout(() => {
                    window.notificationManager.success('CSRF scan completed (simulated)');
                    addScanToHistory('CSRF Scan', 1);

                    showResponse('scannerResponse', {
                        success: true,
                        data: {
                            vulnerabilities: [{ type: 'csrf', severity: 'medium' }],
                            endpoints_tested: 12,
                            unprotected_endpoints: 1
                        },
                        message: 'Mock CSRF scan completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runAuthenticationScan() {
            window.notificationManager.info('Running authentication scan...');

            try {
                const response = await window.apiClient.request('POST', '/security/scan/authentication');

                if (response.success) {
                    window.notificationManager.success('Authentication scan completed');
                    addScanToHistory('Authentication Scan', response.data.vulnerabilities?.length || 0);
                } else {
                    window.notificationManager.error(response.error || 'Authentication scan failed');
                }

                showResponse('scannerResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate authentication scan
                setTimeout(() => {
                    window.notificationManager.success('Authentication scan completed (simulated)');
                    addScanToHistory('Authentication Scan', 0);

                    showResponse('scannerResponse', {
                        success: true,
                        data: {
                            vulnerabilities: [],
                            auth_mechanisms_tested: 5,
                            secure_mechanisms: 5
                        },
                        message: 'Mock authentication scan completed (endpoint may not be available)'
                    }, 'warning');
                }, 2000);
            }
        }

        async function runSessionScan() {
            window.notificationManager.info('Running session management scan...');

            setTimeout(() => {
                window.notificationManager.success('Session scan completed (simulated)');
                addScanToHistory('Session Management Scan', 1);
            }, 2000);
        }

        async function runNetworkScan() {
            window.notificationManager.info('Running network security scan...');

            setTimeout(() => {
                window.notificationManager.success('Network scan completed (simulated)');
                addScanToHistory('Network Security Scan', 0);
            }, 2000);
        }

        // Quick Test Functions
        function quickSQLInjectionScan() {
            runSQLInjectionScan();
        }

        function quickXSSScan() {
            runXSSScan();
        }

        function quickCSRFScan() {
            runCSRFScan();
        }

        function quickAuthScan() {
            runAuthenticationScan();
        }

        // Test Scenario Functions
        function runOWASPScan() {
            window.notificationManager.info('Running OWASP Top 10 scan...');

            setTimeout(() => {
                window.notificationManager.success('OWASP Top 10 scan completed (simulated)');
                addScanToHistory('OWASP Top 10 Scan', 3);
            }, 3000);
        }

        function runAPIScan() {
            window.notificationManager.info('Running API security scan...');

            setTimeout(() => {
                window.notificationManager.success('API security scan completed (simulated)');
                addScanToHistory('API Security Scan', 2);
            }, 2500);
        }

        function runInputValidationScan() {
            window.notificationManager.info('Running input validation scan...');

            setTimeout(() => {
                window.notificationManager.success('Input validation scan completed (simulated)');
                addScanToHistory('Input Validation Scan', 1);
            }, 2000);
        }

        function runConfigurationScan() {
            window.notificationManager.info('Running configuration scan...');

            setTimeout(() => {
                window.notificationManager.success('Configuration scan completed (simulated)');
                addScanToHistory('Configuration Scan', 0);
            }, 2000);
        }

        // Scan History Functions
        function loadScanHistory() {
            scanData.scanHistory = [
                {
                    type: 'Comprehensive Scan',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    vulnerabilities: 5,
                    duration: '15 minutes'
                },
                {
                    type: 'SQL Injection Scan',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    vulnerabilities: 1,
                    duration: '3 minutes'
                },
                {
                    type: 'XSS Scan',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
                    vulnerabilities: 2,
                    duration: '5 minutes'
                }
            ];

            displayScanHistory();
        }

        function displayScanHistory() {
            const historyContainer = document.getElementById('scanHistory');

            const historyHTML = scanData.scanHistory.map(scan => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-type">${scan.type}</div>
                        <div class="history-details">Duration: ${scan.duration}</div>
                    </div>
                    <div class="history-results">
                        <div class="history-count">${scan.vulnerabilities}</div>
                        <div style="font-size: 0.7rem; color: var(--text-secondary);">vulnerabilities</div>
                    </div>
                    <div class="history-time">${formatTimeAgo(scan.timestamp)}</div>
                </div>
            `).join('');

            historyContainer.innerHTML = `
                <div class="history-header">Scan History</div>
                ${historyHTML}
            `;
        }

        function addScanToHistory(type, vulnerabilityCount) {
            scanData.scanHistory.unshift({
                type: type,
                timestamp: new Date(),
                vulnerabilities: vulnerabilityCount,
                duration: Math.floor(Math.random() * 10 + 2) + ' minutes'
            });

            // Keep only last 10 scans
            if (scanData.scanHistory.length > 10) {
                scanData.scanHistory = scanData.scanHistory.slice(0, 10);
            }

            displayScanHistory();
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        function loadSampleVulnerabilities() {
            // Load sample vulnerabilities for demo purposes
            generateMockVulnerabilities();
            updateVulnerabilitySummary();
            displayVulnerabilities();
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
