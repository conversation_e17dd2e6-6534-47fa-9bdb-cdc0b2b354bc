<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rate Limiting Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rate-limit-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .rate-limit-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .status-item {
            text-align: center;
            padding: 1rem;
            background: var(--hover-background);
            border-radius: 8px;
        }

        .status-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-number.normal {
            color: var(--success-color);
        }

        .status-number.warning {
            color: var(--warning-color);
        }

        .status-number.critical {
            color: var(--error-color);
        }

        .status-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .test-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .test-section {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .control-input {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .rate-limit-rules {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .rule-item:last-child {
            border-bottom: none;
        }

        .rule-info {
            flex: 1;
        }

        .rule-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .rule-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .rule-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .rule-status.active {
            background: var(--success-color);
            color: white;
        }

        .rule-status.inactive {
            background: var(--error-color);
            color: white;
        }

        .test-progress {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: none;
        }

        .test-progress.active {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .request-log {
            max-height: 300px;
            overflow-y: auto;
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            display: flex;
            align-items: center;
            padding: 0.25rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .log-status.success {
            background: var(--success-color);
        }

        .log-status.blocked {
            background: var(--error-color);
        }

        .log-status.throttled {
            background: var(--warning-color);
        }

        .log-message {
            flex: 1;
            color: var(--text-primary);
        }

        .log-timestamp {
            color: var(--text-secondary);
            margin-left: 0.5rem;
            flex-shrink: 0;
        }

        .rate-limit-config {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .config-item {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
        }

        .config-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .config-value {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .config-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .test-sections {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-tachometer-alt"></i>
                    <h1>Rate Limiting Testing</h1>
                    <span class="subtitle">API Rate Limiting and Throttling Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Security
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="rate-limit-container">
                <!-- Rate Limit Status -->
                <div class="rate-limit-status">
                    <h2>Rate Limiting Status</h2>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-number normal" id="currentRPS">0</div>
                            <div class="status-label">Current RPS</div>
                        </div>
                        <div class="status-item">
                            <div class="status-number" id="allowedRequests">0</div>
                            <div class="status-label">Allowed Requests</div>
                        </div>
                        <div class="status-item">
                            <div class="status-number warning" id="throttledRequests">0</div>
                            <div class="status-label">Throttled Requests</div>
                        </div>
                        <div class="status-item">
                            <div class="status-number critical" id="blockedRequests">0</div>
                            <div class="status-label">Blocked Requests</div>
                        </div>
                    </div>
                </div>

                <!-- Rate Limit Configuration -->
                <div class="rate-limit-config">
                    <h3>Current Rate Limit Configuration</h3>
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-title">Requests Per Minute</div>
                            <div class="config-value" id="configRPM">100</div>
                            <div class="config-description">Maximum requests allowed per minute per IP</div>
                        </div>
                        <div class="config-item">
                            <div class="config-title">Burst Limit</div>
                            <div class="config-value" id="configBurst">20</div>
                            <div class="config-description">Maximum burst requests allowed</div>
                        </div>
                        <div class="config-item">
                            <div class="config-title">Window Size</div>
                            <div class="config-value" id="configWindow">60s</div>
                            <div class="config-description">Time window for rate limiting</div>
                        </div>
                        <div class="config-item">
                            <div class="config-title">Penalty Duration</div>
                            <div class="config-value" id="configPenalty">300s</div>
                            <div class="config-description">Blocking duration after limit exceeded</div>
                        </div>
                    </div>
                </div>

                <!-- Test Sections -->
                <div class="test-sections">
                    <!-- Rate Limit Testing -->
                    <div class="test-section">
                        <div class="section-header">
                            <div class="section-title">Rate Limit Testing</div>
                        </div>
                        <div class="test-controls">
                            <div class="control-group">
                                <label class="control-label">Requests Per Second</label>
                                <input type="number" class="control-input" id="testRPS" value="10" min="1" max="1000">
                            </div>
                            <div class="control-group">
                                <label class="control-label">Test Duration (s)</label>
                                <input type="number" class="control-input" id="testDuration" value="30" min="5" max="300">
                            </div>
                            <div class="control-group">
                                <label class="control-label">Endpoint</label>
                                <select class="control-input" id="testEndpoint">
                                    <option value="/api/test">/api/test</option>
                                    <option value="/api/auth/login">/api/auth/login</option>
                                    <option value="/api/users">/api/users</option>
                                    <option value="/api/data">/api/data</option>
                                </select>
                            </div>
                        </div>
                        <div class="test-progress" id="testProgress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-text" id="progressText">Preparing test...</div>
                        </div>
                        <button class="btn btn-primary" id="startTestBtn" onclick="startRateLimitTest()">
                            <i class="fas fa-play"></i>
                            Start Rate Limit Test
                        </button>
                        <button class="btn btn-secondary" id="stopTestBtn" onclick="stopRateLimitTest()" style="display: none;">
                            <i class="fas fa-stop"></i>
                            Stop Test
                        </button>
                    </div>

                    <!-- Request Log -->
                    <div class="test-section">
                        <div class="section-header">
                            <div class="section-title">Request Log</div>
                            <button class="btn btn-sm btn-secondary" onclick="clearRequestLog()">
                                <i class="fas fa-trash"></i>
                                Clear Log
                            </button>
                        </div>
                        <div class="request-log" id="requestLog">
                            <!-- Request logs will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Rate Limit Rules -->
                <div class="test-section">
                    <div class="section-header">
                        <div class="section-title">Active Rate Limit Rules</div>
                        <button class="btn btn-sm btn-primary" onclick="refreshRules()">
                            <i class="fas fa-sync"></i>
                            Refresh Rules
                        </button>
                    </div>
                    <div class="rate-limit-rules" id="rateLimitRules">
                        <!-- Rules will be populated here -->
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="rateLimitResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Rate Limiting Testing Interface</p>
                    <p>API rate limiting and throttling testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let rateLimitData = {
            isTestRunning: false,
            testProgress: 0,
            testInterval: null,
            requestLog: [],
            stats: {
                currentRPS: 0,
                allowedRequests: 0,
                throttledRequests: 0,
                blockedRequests: 0
            },
            config: {
                rpm: 100,
                burst: 20,
                window: 60,
                penalty: 300
            },
            rules: []
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await checkServerHealth();
            updateAuthStatus();
            initializeTheme();
            await loadRateLimitStatus();
            loadMockRules();
            startRealTimeUpdates();
        }

        // Rate Limit Functions
        async function loadRateLimitStatus() {
            window.notificationManager.info('Loading rate limit status...');

            try {
                const response = await window.apiClient.request('GET', '/security/rate-limit/status');

                if (response.success) {
                    updateRateLimitStatus(response.data);
                    window.notificationManager.success('Rate limit status loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load rate limit status');
                }

                showResponse('rateLimitResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                loadMockData();
                window.notificationManager.success('Rate limit status loaded successfully (simulated)');

                showResponse('rateLimitResponse', {
                    success: true,
                    data: rateLimitData,
                    message: 'Mock rate limit status loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockData() {
            updateStatsDisplay();
            addRequestLog('success', 'Rate limiting system initialized');
            addRequestLog('success', 'Current configuration: 100 RPM, 20 burst limit');
        }

        function loadMockRules() {
            rateLimitData.rules = [
                {
                    id: 'global_api',
                    name: 'Global API Rate Limit',
                    description: '100 requests per minute for all API endpoints',
                    status: 'active',
                    limit: 100,
                    window: 60
                },
                {
                    id: 'auth_endpoints',
                    name: 'Authentication Endpoints',
                    description: '10 requests per minute for login/register endpoints',
                    status: 'active',
                    limit: 10,
                    window: 60
                },
                {
                    id: 'user_specific',
                    name: 'User-Specific Limits',
                    description: 'Premium users get 500 RPM, regular users 100 RPM',
                    status: 'active',
                    limit: 500,
                    window: 60
                },
                {
                    id: 'burst_protection',
                    name: 'Burst Protection',
                    description: 'Maximum 20 requests in 10 second window',
                    status: 'inactive',
                    limit: 20,
                    window: 10
                }
            ];
            updateRulesDisplay();
        }

        function updateRateLimitStatus(data) {
            if (data.stats) rateLimitData.stats = { ...rateLimitData.stats, ...data.stats };
            if (data.config) rateLimitData.config = { ...rateLimitData.config, ...data.config };
            updateStatsDisplay();
        }

        function updateStatsDisplay() {
            document.getElementById('currentRPS').textContent = rateLimitData.stats.currentRPS;
            document.getElementById('allowedRequests').textContent = rateLimitData.stats.allowedRequests;
            document.getElementById('throttledRequests').textContent = rateLimitData.stats.throttledRequests;
            document.getElementById('blockedRequests').textContent = rateLimitData.stats.blockedRequests;

            document.getElementById('configRPM').textContent = rateLimitData.config.rpm;
            document.getElementById('configBurst').textContent = rateLimitData.config.burst;
            document.getElementById('configWindow').textContent = rateLimitData.config.window + 's';
            document.getElementById('configPenalty').textContent = rateLimitData.config.penalty + 's';
        }

        function updateRulesDisplay() {
            const rulesContainer = document.getElementById('rateLimitRules');
            const rulesHTML = rateLimitData.rules.map(rule => `
                <div class="rule-item">
                    <div class="rule-info">
                        <div class="rule-name">${rule.name}</div>
                        <div class="rule-description">${rule.description}</div>
                    </div>
                    <div class="rule-status ${rule.status}">${rule.status.toUpperCase()}</div>
                </div>
            `).join('');
            rulesContainer.innerHTML = rulesHTML;
        }

        // Test Functions
        async function startRateLimitTest() {
            if (rateLimitData.isTestRunning) return;

            const rps = parseInt(document.getElementById('testRPS').value);
            const duration = parseInt(document.getElementById('testDuration').value);
            const endpoint = document.getElementById('testEndpoint').value;

            rateLimitData.isTestRunning = true;
            rateLimitData.testProgress = 0;

            document.getElementById('startTestBtn').style.display = 'none';
            document.getElementById('stopTestBtn').style.display = 'inline-block';
            document.getElementById('testProgress').classList.add('active');

            window.notificationManager.info(`Starting rate limit test: ${rps} RPS for ${duration}s on ${endpoint}`);
            addRequestLog('success', `Test started: ${rps} RPS, ${duration}s duration, endpoint: ${endpoint}`);

            simulateRateLimitTest(rps, duration, endpoint);
        }

        function simulateRateLimitTest(rps, duration, endpoint) {
            const totalRequests = rps * duration;
            let requestsSent = 0;
            const progressIncrement = 100 / (duration * 10);

            rateLimitData.testInterval = setInterval(() => {
                rateLimitData.testProgress += progressIncrement;

                if (rateLimitData.testProgress >= 100) {
                    rateLimitData.testProgress = 100;
                    stopRateLimitTest();
                    return;
                }

                document.getElementById('progressFill').style.width = rateLimitData.testProgress + '%';
                document.getElementById('progressText').textContent =
                    `Test in progress... ${Math.round(rateLimitData.testProgress)}% complete`;

                // Simulate requests
                const requestsThisSecond = Math.floor(rps / 10);
                for (let i = 0; i < requestsThisSecond; i++) {
                    requestsSent++;
                    const isBlocked = requestsSent > rateLimitData.config.rpm * (duration / 60);
                    const isThrottled = !isBlocked && Math.random() < 0.1;

                    if (isBlocked) {
                        rateLimitData.stats.blockedRequests++;
                        addRequestLog('blocked', `Request ${requestsSent} to ${endpoint} - BLOCKED (rate limit exceeded)`);
                    } else if (isThrottled) {
                        rateLimitData.stats.throttledRequests++;
                        addRequestLog('throttled', `Request ${requestsSent} to ${endpoint} - THROTTLED (approaching limit)`);
                    } else {
                        rateLimitData.stats.allowedRequests++;
                        addRequestLog('success', `Request ${requestsSent} to ${endpoint} - SUCCESS`);
                    }
                }

                rateLimitData.stats.currentRPS = requestsThisSecond * 10;
                updateStatsDisplay();
            }, 100);
        }

        function stopRateLimitTest() {
            if (!rateLimitData.isTestRunning) return;

            rateLimitData.isTestRunning = false;

            if (rateLimitData.testInterval) {
                clearInterval(rateLimitData.testInterval);
                rateLimitData.testInterval = null;
            }

            document.getElementById('startTestBtn').style.display = 'inline-block';
            document.getElementById('stopTestBtn').style.display = 'none';
            document.getElementById('testProgress').classList.remove('active');
            document.getElementById('progressFill').style.width = '0%';

            rateLimitData.stats.currentRPS = 0;
            updateStatsDisplay();

            window.notificationManager.success('Rate limit test completed');
            addRequestLog('success', 'Rate limit test completed successfully');
        }

        // Logging Functions
        function addRequestLog(status, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, status, message };
            rateLimitData.requestLog.unshift(logEntry);
            if (rateLimitData.requestLog.length > 100) rateLimitData.requestLog = rateLimitData.requestLog.slice(0, 100);
            updateRequestLogDisplay();
        }

        function updateRequestLogDisplay() {
            const logContainer = document.getElementById('requestLog');
            if (rateLimitData.requestLog.length === 0) {
                logContainer.innerHTML = '<div style="text-align: center; padding: 2rem; color: var(--text-secondary);">No request logs available.</div>';
                return;
            }
            const logsHTML = rateLimitData.requestLog.map(log => `
                <div class="log-entry">
                    <div class="log-status ${log.status}"></div>
                    <div class="log-message">${log.message}</div>
                    <div class="log-timestamp">${log.timestamp}</div>
                </div>
            `).join('');
            logContainer.innerHTML = logsHTML;
        }

        function clearRequestLog() {
            rateLimitData.requestLog = [];
            updateRequestLogDisplay();
            window.notificationManager.success('Request log cleared');
        }

        function refreshRules() {
            loadMockRules();
            window.notificationManager.success('Rate limit rules refreshed');
        }

        function startRealTimeUpdates() {
            setInterval(() => {
                if (!rateLimitData.isTestRunning && Math.random() < 0.1) {
                    const normalRPS = Math.floor(Math.random() * 20) + 5;
                    rateLimitData.stats.currentRPS = normalRPS;
                    rateLimitData.stats.allowedRequests += normalRPS;
                    updateStatsDisplay();
                }
            }, 2000);
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else throw new Error('Server error');
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');
            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');
            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
