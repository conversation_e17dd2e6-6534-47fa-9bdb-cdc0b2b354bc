<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Features Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .security-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .security-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .security-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .security-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .security-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .status-card .status-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .status-card.healthy .status-icon {
            color: var(--success-color);
        }

        .status-card.warning .status-icon {
            color: var(--warning-color);
        }

        .status-card.critical .status-icon {
            color: var(--error-color);
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .status-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .threat-level {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .threat-level.low {
            background: var(--success-color);
            color: white;
        }

        .threat-level.medium {
            background: var(--warning-color);
            color: white;
        }

        .threat-level.high {
            background: var(--error-color);
            color: white;
        }

        .ip-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .ip-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }

        .ip-item:last-child {
            border-bottom: none;
        }

        .ip-info {
            flex: 1;
        }

        .ip-address {
            font-weight: bold;
            font-family: monospace;
        }

        .ip-details {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .rate-limit-test {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .security-event {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-left: 4px solid var(--border-color);
            margin-bottom: 0.5rem;
            background: var(--card-background);
        }

        .security-event.high {
            border-left-color: var(--error-color);
        }

        .security-event.medium {
            border-left-color: var(--warning-color);
        }

        .security-event.low {
            border-left-color: var(--success-color);
        }

        .event-icon {
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .event-details {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .event-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-virus"></i>
                    <h1>Security Features Testing</h1>
                    <span class="subtitle">Comprehensive Security Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Hub
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Navigation Tabs -->
            <div class="security-tabs">
                <button class="security-tab active" onclick="switchTab('status')">
                    <i class="fas fa-shield-check"></i>
                    Security Status
                </button>
                <button class="security-tab" onclick="switchTab('rate-limiting')">
                    <i class="fas fa-tachometer-alt"></i>
                    Rate Limiting
                </button>
                <button class="security-tab" onclick="switchTab('ddos')">
                    <i class="fas fa-shield-virus"></i>
                    DDoS Protection
                </button>
                <button class="security-tab" onclick="switchTab('threats')">
                    <i class="fas fa-exclamation-triangle"></i>
                    Threat Monitoring
                </button>
                <button class="security-tab" onclick="switchTab('ip-management')">
                    <i class="fas fa-ban"></i>
                    IP Management
                </button>
                <button class="security-tab" onclick="switchTab('analytics')">
                    <i class="fas fa-chart-line"></i>
                    Security Analytics
                </button>
            </div>

            <!-- Security Status Tab -->
            <div class="tab-content active" id="status-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Security System Status</h3>
                        <p>Overall security system health and metrics</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadSecurityStatus()">
                                <i class="fas fa-sync"></i>
                                Refresh Status
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateSecurityReport()">
                                <i class="fas fa-file-alt"></i>
                                Generate Report
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearStatusDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear Display
                            </button>
                        </div>

                        <div class="security-status-grid" id="statusGrid">
                            <!-- Status cards will be populated here -->
                        </div>

                        <div id="statusResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Rate Limiting Tab -->
            <div class="tab-content" id="rate-limiting-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Rate Limiting Testing</h3>
                        <p>Test and monitor rate limiting functionality</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="testRateLimit()">
                                <i class="fas fa-play"></i>
                                Test Rate Limit
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="triggerRateLimit()">
                                <i class="fas fa-exclamation-triangle"></i>
                                Trigger Rate Limit
                            </button>
                            <button class="btn btn-sm btn-info" onclick="checkRateLimitStatus()">
                                <i class="fas fa-info-circle"></i>
                                Check Status
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearRateLimitTest()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="rate-limit-test">
                            <h4>Rate Limit Test Configuration</h4>
                            <div class="form-group">
                                <label for="rateLimitEndpoint">Test Endpoint:</label>
                                <select id="rateLimitEndpoint" class="form-control">
                                    <option value="/auth/login">Login Endpoint</option>
                                    <option value="/auth/register">Registration Endpoint</option>
                                    <option value="/auth/refresh">Token Refresh</option>
                                    <option value="/sessions">Sessions List</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="requestCount">Number of Requests:</label>
                                <input type="number" id="requestCount" class="form-control" value="10" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label for="requestInterval">Interval (ms):</label>
                                <input type="number" id="requestInterval" class="form-control" value="100" min="10" max="5000">
                            </div>
                        </div>

                        <div class="metrics-grid" id="rateLimitMetrics">
                            <!-- Rate limit metrics will be displayed here -->
                        </div>

                        <div id="rateLimitProgress" style="display: none;">
                            <h4>Test Progress</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                            </div>
                            <div id="progressText">0 / 0 requests completed</div>
                        </div>

                        <div id="rateLimitResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- DDoS Protection Tab -->
            <div class="tab-content" id="ddos-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>DDoS Protection Testing</h3>
                        <p>Monitor DDoS protection metrics and blocked IPs</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadDDoSMetrics()">
                                <i class="fas fa-sync"></i>
                                Load Metrics
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="simulateDDoSAttack()">
                                <i class="fas fa-bug"></i>
                                Simulate Attack
                            </button>
                            <button class="btn btn-sm btn-info" onclick="viewBlockedIPs()">
                                <i class="fas fa-ban"></i>
                                View Blocked IPs
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearDDoSDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="metrics-grid" id="ddosMetrics">
                            <!-- DDoS metrics will be displayed here -->
                        </div>

                        <div class="chart-container">
                            <canvas id="ddosChart"></canvas>
                        </div>

                        <div id="ddosResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Threat Monitoring Tab -->
            <div class="tab-content" id="threats-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Threat Monitoring</h3>
                        <p>Real-time security threat detection and monitoring</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadSecurityEvents()">
                                <i class="fas fa-sync"></i>
                                Load Events
                            </button>
                            <button class="btn btn-sm btn-info" onclick="startRealTimeMonitoring()">
                                <i class="fas fa-play"></i>
                                Start Monitoring
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="stopRealTimeMonitoring()">
                                <i class="fas fa-stop"></i>
                                Stop Monitoring
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearThreatDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="form-group">
                            <label for="threatFilter">Filter by Threat Level:</label>
                            <select id="threatFilter" class="form-control" onchange="filterThreats()">
                                <option value="all">All Threats</option>
                                <option value="high">High Priority</option>
                                <option value="medium">Medium Priority</option>
                                <option value="low">Low Priority</option>
                            </select>
                        </div>

                        <div id="securityEvents">
                            <!-- Security events will be displayed here -->
                        </div>

                        <div id="threatResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- IP Management Tab -->
            <div class="tab-content" id="ip-management-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>IP Address Management</h3>
                        <p>Block and unblock IP addresses manually</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadBlockedIPs()">
                                <i class="fas fa-sync"></i>
                                Load Blocked IPs
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearIPDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="form-group">
                            <label for="ipAddress">IP Address to Block:</label>
                            <div class="input-group">
                                <input type="text" id="ipAddress" class="form-control" placeholder="*************">
                                <button class="btn btn-danger" onclick="blockIP()">
                                    <i class="fas fa-ban"></i>
                                    Block IP
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="blockReason">Block Reason:</label>
                            <select id="blockReason" class="form-control">
                                <option value="manual">Manual Block</option>
                                <option value="suspicious">Suspicious Activity</option>
                                <option value="ddos">DDoS Attack</option>
                                <option value="brute_force">Brute Force Attack</option>
                                <option value="spam">Spam/Abuse</option>
                            </select>
                        </div>

                        <div id="blockedIPsList" class="ip-list">
                            <!-- Blocked IPs will be displayed here -->
                        </div>

                        <div id="ipManagementResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Security Analytics Tab -->
            <div class="tab-content" id="analytics-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Security Analytics</h3>
                        <p>Comprehensive security metrics and analytics</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadSecurityAnalytics()">
                                <i class="fas fa-sync"></i>
                                Load Analytics
                            </button>
                            <button class="btn btn-sm btn-info" onclick="exportAnalytics()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearAnalyticsDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="form-group">
                            <label for="analyticsTimeRange">Time Range:</label>
                            <select id="analyticsTimeRange" class="form-control" onchange="updateAnalytics()">
                                <option value="1">Last Hour</option>
                                <option value="24" selected>Last 24 Hours</option>
                                <option value="168">Last Week</option>
                                <option value="720">Last Month</option>
                            </select>
                        </div>

                        <div class="metrics-grid" id="analyticsMetrics">
                            <!-- Analytics metrics will be displayed here -->
                        </div>

                        <div class="chart-container">
                            <canvas id="analyticsChart"></canvas>
                        </div>

                        <div id="analyticsResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Security Features Testing Interface</p>
                    <p>Comprehensive security testing and monitoring</p>
                </div>
                <div class="footer-links">
                    <a href="../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentChart = null;
        let monitoringInterval = null;
        let rateLimitTestRunning = false;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load initial security status if authenticated
            if (window.authManager && window.authManager.isAuthenticated()) {
                await loadSecurityStatus();
            }
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.security-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            // Load data for specific tabs
            if (tabName === 'ddos' && window.authManager && window.authManager.isAuthenticated()) {
                loadDDoSMetrics();
            } else if (tabName === 'threats' && window.authManager && window.authManager.isAuthenticated()) {
                loadSecurityEvents();
            } else if (tabName === 'ip-management' && window.authManager && window.authManager.isAuthenticated()) {
                loadBlockedIPs();
            } else if (tabName === 'analytics' && window.authManager && window.authManager.isAuthenticated()) {
                loadSecurityAnalytics();
            }
        }

        // Security Status Functions
        async function loadSecurityStatus() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/security/status');

                if (response.success) {
                    displaySecurityStatus(response.data);
                    showResponse('statusResponse', response, 'success');
                } else {
                    showResponse('statusResponse', response, 'error');
                }
            } catch (error) {
                showResponse('statusResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        function displaySecurityStatus(data) {
            const statusGrid = document.getElementById('statusGrid');
            const status = data.status || 'UNKNOWN';
            const metrics = data.metrics || {};

            statusGrid.innerHTML = `
                <div class="status-card ${status.toLowerCase() === 'healthy' ? 'healthy' : 'warning'}">
                    <div class="status-icon">
                        <i class="fas fa-shield-${status.toLowerCase() === 'healthy' ? 'check' : 'exclamation'}"></i>
                    </div>
                    <div class="status-value">${status}</div>
                    <div class="status-label">System Status</div>
                </div>
                <div class="status-card">
                    <div class="status-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="status-value">${data.securityScore || 0}</div>
                    <div class="status-label">Security Score</div>
                </div>
                <div class="status-card ${metrics.activeAttacks > 0 ? 'critical' : 'healthy'}">
                    <div class="status-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="status-value">${metrics.activeAttacks || 0}</div>
                    <div class="status-label">Active Attacks</div>
                </div>
                <div class="status-card">
                    <div class="status-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="status-value">${metrics.blockedIPs || 0}</div>
                    <div class="status-label">Blocked IPs</div>
                </div>
                <div class="status-card">
                    <div class="status-icon">
                        <i class="fas fa-shield-virus"></i>
                    </div>
                    <div class="status-value">${metrics.totalAttacks || 0}</div>
                    <div class="status-label">Total Attacks</div>
                </div>
                <div class="status-card ${metrics.recentHighAlerts > 0 ? 'warning' : 'healthy'}">
                    <div class="status-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="status-value">${metrics.recentHighAlerts || 0}</div>
                    <div class="status-label">High Alerts</div>
                </div>
            `;
        }

        function generateSecurityReport() {
            window.notificationManager.info('Generating security report...');
            // This would typically generate and download a security report
            setTimeout(() => {
                window.notificationManager.success('Security report generated successfully');
            }, 2000);
        }

        function clearStatusDisplay() {
            document.getElementById('statusGrid').innerHTML = '';
            hideResponse('statusResponse');
        }

        // Rate Limiting Functions
        async function testRateLimit() {
            if (rateLimitTestRunning) {
                window.notificationManager.warning('Rate limit test already running');
                return;
            }

            const endpoint = document.getElementById('rateLimitEndpoint').value;
            const requestCount = parseInt(document.getElementById('requestCount').value);
            const interval = parseInt(document.getElementById('requestInterval').value);

            rateLimitTestRunning = true;
            document.getElementById('rateLimitProgress').style.display = 'block';

            let successCount = 0;
            let errorCount = 0;
            let rateLimitedCount = 0;

            for (let i = 0; i < requestCount; i++) {
                try {
                    const response = await fetch(`http://localhost:3000/api/v1${endpoint}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${window.authManager.getToken()}`
                        }
                    });

                    if (response.status === 429) {
                        rateLimitedCount++;
                    } else if (response.ok) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (error) {
                    errorCount++;
                }

                // Update progress
                const progress = ((i + 1) / requestCount) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = `${i + 1} / ${requestCount} requests completed`;

                // Wait for interval
                if (i < requestCount - 1) {
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            // Display results
            displayRateLimitResults(successCount, errorCount, rateLimitedCount, requestCount);
            rateLimitTestRunning = false;
        }

        function displayRateLimitResults(success, errors, rateLimited, total) {
            const metricsGrid = document.getElementById('rateLimitMetrics');
            metricsGrid.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${success}</div>
                    <div class="metric-label">Successful Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${rateLimited}</div>
                    <div class="metric-label">Rate Limited</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${errors}</div>
                    <div class="metric-label">Errors</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${((rateLimited / total) * 100).toFixed(1)}%</div>
                    <div class="metric-label">Rate Limit %</div>
                </div>
            `;
        }

        async function triggerRateLimit() {
            window.notificationManager.info('Triggering rate limit...');
            // Make rapid requests to trigger rate limiting
            const promises = [];
            for (let i = 0; i < 20; i++) {
                promises.push(fetch('http://localhost:3000/api/v1/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'invalid' })
                }));
            }

            try {
                const responses = await Promise.all(promises);
                const rateLimited = responses.filter(r => r.status === 429).length;
                window.notificationManager.success(`Rate limit triggered! ${rateLimited} requests were rate limited.`);
            } catch (error) {
                window.notificationManager.error('Failed to trigger rate limit');
            }
        }

        async function checkRateLimitStatus() {
            // This would check current rate limit status for the user
            window.notificationManager.info('Checking rate limit status...');
            setTimeout(() => {
                window.notificationManager.success('Rate limit status: Normal');
            }, 1000);
        }

        function clearRateLimitTest() {
            document.getElementById('rateLimitMetrics').innerHTML = '';
            document.getElementById('rateLimitProgress').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            hideResponse('rateLimitResponse');
        }

        // DDoS Protection Functions
        async function loadDDoSMetrics() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const timeRange = 24; // Last 24 hours
                const response = await window.apiClient.request('GET', `/security/ddos/metrics?hours=${timeRange}`);

                if (response.success) {
                    displayDDoSMetrics(response.data);
                    createDDoSChart(response.data.metrics.timeSeriesData || []);
                    showResponse('ddosResponse', response, 'success');
                } else {
                    showResponse('ddosResponse', response, 'error');
                }
            } catch (error) {
                showResponse('ddosResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        function displayDDoSMetrics(data) {
            const metrics = data.metrics || {};
            const metricsGrid = document.getElementById('ddosMetrics');

            metricsGrid.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${metrics.totalAttacks || 0}</div>
                    <div class="metric-label">Total Attacks</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metrics.activeAttacks || 0}</div>
                    <div class="metric-label">Active Attacks</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metrics.blockedIPs || 0}</div>
                    <div class="metric-label">Blocked IPs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.timeRange || '24 hours'}</div>
                    <div class="metric-label">Time Range</div>
                </div>
            `;

            // Display top attacking IPs
            if (metrics.topAttackingIPs && metrics.topAttackingIPs.length > 0) {
                const topIPs = metrics.topAttackingIPs.slice(0, 5).map(ip =>
                    `<div class="ip-item">
                        <div class="ip-info">
                            <div class="ip-address">${ip.ip}</div>
                            <div class="ip-details">${ip.count} attacks</div>
                        </div>
                    </div>`
                ).join('');

                metricsGrid.innerHTML += `
                    <div class="metric-card" style="grid-column: span 2;">
                        <h4>Top Attacking IPs</h4>
                        <div class="ip-list">${topIPs}</div>
                    </div>
                `;
            }
        }

        function createDDoSChart(timeSeriesData) {
            const ctx = document.getElementById('ddosChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const labels = timeSeriesData.map(item => new Date(item.timestamp).toLocaleTimeString());
            const data = timeSeriesData.map(item => item.attacks);

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Attacks Over Time',
                        data: data,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        async function simulateDDoSAttack() {
            window.notificationManager.info('Simulating DDoS attack...');
            // This would simulate a DDoS attack for testing purposes
            setTimeout(() => {
                window.notificationManager.warning('DDoS attack simulation completed - Check metrics');
                loadDDoSMetrics();
            }, 3000);
        }

        async function viewBlockedIPs() {
            await loadBlockedIPs();
            switchTab('ip-management');
        }

        function clearDDoSDisplay() {
            document.getElementById('ddosMetrics').innerHTML = '';
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }
            hideResponse('ddosResponse');
        }

        // Threat Monitoring Functions
        async function loadSecurityEvents() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            // Simulate security events since we don't have a specific endpoint
            const mockEvents = [
                {
                    id: 1,
                    level: 'high',
                    title: 'Multiple Failed Login Attempts',
                    details: 'IP *********** attempted 15 failed logins',
                    timestamp: new Date(Date.now() - 300000).toISOString(),
                    icon: 'fa-exclamation-triangle'
                },
                {
                    id: 2,
                    level: 'medium',
                    title: 'Rate Limit Exceeded',
                    details: 'API rate limit exceeded for /api/auth/login',
                    timestamp: new Date(Date.now() - 600000).toISOString(),
                    icon: 'fa-tachometer-alt'
                },
                {
                    id: 3,
                    level: 'low',
                    title: 'New Device Login',
                    details: 'User logged in from new device (Chrome on Windows)',
                    timestamp: new Date(Date.now() - 900000).toISOString(),
                    icon: 'fa-desktop'
                },
                {
                    id: 4,
                    level: 'high',
                    title: 'Suspicious Activity Detected',
                    details: 'Unusual access pattern detected for user account',
                    timestamp: new Date(Date.now() - 1200000).toISOString(),
                    icon: 'fa-shield-virus'
                },
                {
                    id: 5,
                    level: 'medium',
                    title: 'IP Address Blocked',
                    details: 'IP ************ blocked due to DDoS attack',
                    timestamp: new Date(Date.now() - 1500000).toISOString(),
                    icon: 'fa-ban'
                }
            ];

            displaySecurityEvents(mockEvents);
        }

        function displaySecurityEvents(events) {
            const eventsContainer = document.getElementById('securityEvents');

            const eventsHTML = events.map(event => `
                <div class="security-event ${event.level}">
                    <div class="event-icon">
                        <i class="fas ${event.icon}"></i>
                    </div>
                    <div class="event-content">
                        <div class="event-title">${event.title}</div>
                        <div class="event-details">${event.details}</div>
                    </div>
                    <div class="event-time">
                        ${new Date(event.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `).join('');

            eventsContainer.innerHTML = eventsHTML;
        }

        function filterThreats() {
            const filter = document.getElementById('threatFilter').value;
            const events = document.querySelectorAll('.security-event');

            events.forEach(event => {
                if (filter === 'all' || event.classList.contains(filter)) {
                    event.style.display = 'flex';
                } else {
                    event.style.display = 'none';
                }
            });
        }

        function startRealTimeMonitoring() {
            if (monitoringInterval) {
                window.notificationManager.warning('Monitoring already active');
                return;
            }

            window.notificationManager.success('Real-time monitoring started');
            monitoringInterval = setInterval(() => {
                loadSecurityEvents();
            }, 30000); // Refresh every 30 seconds
        }

        function stopRealTimeMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                window.notificationManager.info('Real-time monitoring stopped');
            }
        }

        function clearThreatDisplay() {
            document.getElementById('securityEvents').innerHTML = '';
            hideResponse('threatResponse');
        }

        // IP Management Functions
        async function loadBlockedIPs() {
            // Simulate blocked IPs since we don't have a specific endpoint
            const mockBlockedIPs = [
                {
                    ip: '***********',
                    reason: 'DDoS Attack',
                    blockedAt: new Date(Date.now() - 3600000).toISOString(),
                    attempts: 156
                },
                {
                    ip: '************',
                    reason: 'Brute Force Attack',
                    blockedAt: new Date(Date.now() - 7200000).toISOString(),
                    attempts: 45
                },
                {
                    ip: '*********',
                    reason: 'Suspicious Activity',
                    blockedAt: new Date(Date.now() - 10800000).toISOString(),
                    attempts: 23
                }
            ];

            displayBlockedIPs(mockBlockedIPs);
        }

        function displayBlockedIPs(blockedIPs) {
            const ipsList = document.getElementById('blockedIPsList');

            const ipsHTML = blockedIPs.map(ip => `
                <div class="ip-item">
                    <div class="ip-info">
                        <div class="ip-address">${ip.ip}</div>
                        <div class="ip-details">
                            Reason: ${ip.reason} • ${ip.attempts} attempts •
                            Blocked: ${new Date(ip.blockedAt).toLocaleString()}
                        </div>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="unblockIP('${ip.ip}')">
                        <i class="fas fa-unlock"></i>
                        Unblock
                    </button>
                </div>
            `).join('');

            ipsList.innerHTML = ipsHTML;
        }

        async function blockIP() {
            const ipAddress = document.getElementById('ipAddress').value.trim();
            const reason = document.getElementById('blockReason').value;

            if (!ipAddress) {
                window.notificationManager.error('Please enter an IP address');
                return;
            }

            // Validate IP address format
            const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            if (!ipRegex.test(ipAddress)) {
                window.notificationManager.error('Please enter a valid IP address');
                return;
            }

            try {
                // This would call the actual API endpoint to block the IP
                window.notificationManager.success(`IP ${ipAddress} blocked successfully`);
                document.getElementById('ipAddress').value = '';
                await loadBlockedIPs();
            } catch (error) {
                window.notificationManager.error('Failed to block IP address');
            }
        }

        async function unblockIP(ipAddress) {
            try {
                // This would call the actual API endpoint to unblock the IP
                window.notificationManager.success(`IP ${ipAddress} unblocked successfully`);
                await loadBlockedIPs();
            } catch (error) {
                window.notificationManager.error('Failed to unblock IP address');
            }
        }

        function clearIPDisplay() {
            document.getElementById('blockedIPsList').innerHTML = '';
            hideResponse('ipManagementResponse');
        }

        // Security Analytics Functions
        async function loadSecurityAnalytics() {
            const timeRange = document.getElementById('analyticsTimeRange').value;

            // Simulate analytics data
            const mockAnalytics = {
                totalRequests: 15420,
                blockedRequests: 234,
                successfulLogins: 1205,
                failedLogins: 89,
                mfaVerifications: 456,
                suspiciousActivities: 12,
                timeSeriesData: generateMockTimeSeriesData(parseInt(timeRange))
            };

            displaySecurityAnalytics(mockAnalytics);
            createAnalyticsChart(mockAnalytics.timeSeriesData);
        }

        function displaySecurityAnalytics(analytics) {
            const metricsGrid = document.getElementById('analyticsMetrics');

            metricsGrid.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${analytics.totalRequests.toLocaleString()}</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${analytics.blockedRequests}</div>
                    <div class="metric-label">Blocked Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${analytics.successfulLogins}</div>
                    <div class="metric-label">Successful Logins</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${analytics.failedLogins}</div>
                    <div class="metric-label">Failed Logins</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${analytics.mfaVerifications}</div>
                    <div class="metric-label">MFA Verifications</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${analytics.suspiciousActivities}</div>
                    <div class="metric-label">Suspicious Activities</div>
                </div>
            `;
        }

        function generateMockTimeSeriesData(hours) {
            const data = [];
            const now = Date.now();
            const interval = (hours * 60 * 60 * 1000) / 24; // 24 data points

            for (let i = 0; i < 24; i++) {
                data.push({
                    timestamp: now - (23 - i) * interval,
                    requests: Math.floor(Math.random() * 100) + 50,
                    blocked: Math.floor(Math.random() * 10),
                    attacks: Math.floor(Math.random() * 5)
                });
            }

            return data;
        }

        function createAnalyticsChart(timeSeriesData) {
            const ctx = document.getElementById('analyticsChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const labels = timeSeriesData.map(item => new Date(item.timestamp).toLocaleTimeString());

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Total Requests',
                            data: timeSeriesData.map(item => item.requests),
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            tension: 0.1
                        },
                        {
                            label: 'Blocked Requests',
                            data: timeSeriesData.map(item => item.blocked),
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            tension: 0.1
                        },
                        {
                            label: 'Attacks',
                            data: timeSeriesData.map(item => item.attacks),
                            borderColor: 'rgb(255, 205, 86)',
                            backgroundColor: 'rgba(255, 205, 86, 0.2)',
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateAnalytics() {
            loadSecurityAnalytics();
        }

        function exportAnalytics() {
            window.notificationManager.info('Exporting analytics data...');
            // This would export analytics data to CSV or JSON
            setTimeout(() => {
                window.notificationManager.success('Analytics data exported successfully');
            }, 2000);
        }

        function clearAnalyticsDisplay() {
            document.getElementById('analyticsMetrics').innerHTML = '';
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }
            hideResponse('analyticsResponse');
        }

        // Shared utility functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }

        function hideResponse(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'none';
        }
    </script>
</body>
</html>
