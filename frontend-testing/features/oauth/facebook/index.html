<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook OAuth Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .oauth-container {
            max-width: 800px;
            margin: 2rem auto;
        }

        .oauth-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-icon.connected {
            color: #1877f2;
        }

        .status-icon.disconnected {
            color: var(--text-secondary);
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .facebook-button {
            background: #1877f2;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .facebook-button:hover {
            background: #166fe5;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
        }

        .facebook-button:active {
            transform: translateY(0);
        }

        .facebook-button.disconnect {
            background: var(--error-color);
        }

        .facebook-button.disconnect:hover {
            background: var(--error-color-dark);
        }

        .facebook-icon {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .oauth-flow {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .flow-step {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            position: relative;
        }

        .flow-step.active {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .flow-step.completed {
            border-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.1);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 0.5rem auto;
        }

        .step-number.active {
            background: var(--primary-color);
            color: white;
        }

        .step-number.completed {
            background: var(--success-color);
            color: white;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .step-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .user-info {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #1877f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .permissions-list {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-icon {
            color: var(--success-color);
            margin-right: 0.5rem;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .oauth-config {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .config-value {
            font-family: monospace;
            font-size: 0.9rem;
            color: var(--text-secondary);
            background: var(--hover-background);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .token-display {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.8rem;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .scope-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .scope-tag {
            background: #1877f2;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .facebook-features {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .feature-item {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .feature-icon {
            font-size: 2rem;
            color: #1877f2;
            margin-bottom: 0.5rem;
        }

        .feature-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .feature-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-facebook"></i>
                    <h1>Facebook OAuth Testing</h1>
                    <span class="subtitle">Facebook OAuth Integration Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to OAuth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="oauth-container">
                <!-- OAuth Status -->
                <div class="oauth-status" id="oauthStatus">
                    <div class="status-icon disconnected">
                        <i class="fab fa-facebook"></i>
                    </div>
                    <div class="status-title">Facebook Account Not Connected</div>
                    <div class="status-description">Connect your Facebook account to enable OAuth authentication</div>
                    <button class="facebook-button" onclick="initiateFacebookOAuth()">
                        <div class="facebook-icon">
                            <i class="fab fa-facebook-f" style="color: #1877f2;"></i>
                        </div>
                        Continue with Facebook
                    </button>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="testOAuthFlow()">
                        <i class="fas fa-play"></i>
                        Test OAuth Flow
                    </button>
                    <button class="btn btn-success" onclick="testTokenValidation()">
                        <i class="fas fa-check"></i>
                        Test Token Validation
                    </button>
                    <button class="btn btn-warning" onclick="testTokenRefresh()">
                        <i class="fas fa-sync"></i>
                        Test Token Refresh
                    </button>
                    <button class="btn btn-info" onclick="testUserInfo()">
                        <i class="fas fa-user"></i>
                        Test User Info
                    </button>
                </div>

                <!-- OAuth Flow Steps -->
                <div class="oauth-flow">
                    <h3>Facebook OAuth 2.0 Authorization Flow</h3>
                    <div class="flow-steps">
                        <div class="flow-step" id="step1">
                            <div class="step-number">1</div>
                            <div class="step-title">Authorization Request</div>
                            <div class="step-description">Redirect to Facebook OAuth</div>
                        </div>
                        <div class="flow-step" id="step2">
                            <div class="step-number">2</div>
                            <div class="step-title">User Consent</div>
                            <div class="step-description">User grants permissions</div>
                        </div>
                        <div class="flow-step" id="step3">
                            <div class="step-number">3</div>
                            <div class="step-title">Authorization Code</div>
                            <div class="step-description">Receive auth code</div>
                        </div>
                        <div class="flow-step" id="step4">
                            <div class="step-number">4</div>
                            <div class="step-title">Token Exchange</div>
                            <div class="step-description">Exchange code for tokens</div>
                        </div>
                        <div class="flow-step" id="step5">
                            <div class="step-number">5</div>
                            <div class="step-title">Access Resources</div>
                            <div class="step-description">Use tokens to access API</div>
                        </div>
                    </div>
                </div>

                <!-- OAuth Configuration -->
                <div class="oauth-config">
                    <h3>Facebook OAuth Configuration</h3>
                    <div class="config-item">
                        <div class="config-label">App ID:</div>
                        <div class="config-value" id="appId">your-facebook-app-id</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Redirect URI:</div>
                        <div class="config-value" id="redirectUri">http://localhost:3000/auth/facebook/callback</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Scopes:</div>
                        <div class="scope-tags">
                            <span class="scope-tag">email</span>
                            <span class="scope-tag">public_profile</span>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Response Type:</div>
                        <div class="config-value">code</div>
                    </div>
                </div>

                <!-- User Information (Hidden by default) -->
                <div class="user-info" id="userInfo" style="display: none;">
                    <h3>Connected Facebook Account</h3>
                    <div class="user-profile">
                        <div class="user-avatar" id="userAvatar">JD</div>
                        <div class="user-details">
                            <div class="user-name" id="userName">John Doe</div>
                            <div class="user-email" id="userEmail"><EMAIL></div>
                        </div>
                        <button class="facebook-button disconnect" onclick="disconnectFacebook()">
                            <i class="fas fa-unlink"></i>
                            Disconnect
                        </button>
                    </div>

                    <div class="permissions-list">
                        <h4>Granted Permissions:</h4>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            Access your public profile
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            Access your email address
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            Access your friends list
                        </div>
                    </div>

                    <div class="token-display" id="tokenDisplay">
                        <strong>Access Token:</strong><br>
                        <span id="accessToken">No token available</span>
                    </div>
                </div>

                <!-- Facebook Features -->
                <div class="facebook-features">
                    <h3>Facebook API Features</h3>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="feature-title">Profile Data</div>
                            <div class="feature-description">Access user's basic profile information</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-title">Friends List</div>
                            <div class="feature-description">Access user's friends who also use the app</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="feature-title">Photos</div>
                            <div class="feature-description">Access user's photos and albums</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-share"></i>
                            </div>
                            <div class="feature-title">Posts</div>
                            <div class="feature-description">Read and publish posts on behalf of user</div>
                        </div>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Complete OAuth Flow</div>
                        <div class="scenario-description">Test the complete Facebook OAuth authorization flow</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="simulateCompleteFlow()">
                                <i class="fas fa-play"></i>
                                Simulate Flow
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid App ID</div>
                        <div class="scenario-description">Test OAuth flow with invalid app configuration</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="testInvalidApp()">
                                <i class="fas fa-times"></i>
                                Test Invalid App
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">User Denial</div>
                        <div class="scenario-description">Test when user denies OAuth permissions</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testUserDenial()">
                                <i class="fas fa-user-slash"></i>
                                Test User Denial
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Token Expiration</div>
                        <div class="scenario-description">Test handling of expired access tokens</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testTokenExpiration()">
                                <i class="fas fa-clock"></i>
                                Test Expiration
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Permission Scopes</div>
                        <div class="scenario-description">Test OAuth with different permission scopes</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testPermissionScopes()">
                                <i class="fas fa-list"></i>
                                Test Scopes
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Graph API Access</div>
                        <div class="scenario-description">Test accessing Facebook Graph API endpoints</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="testGraphAPI()">
                                <i class="fas fa-network-wired"></i>
                                Test Graph API
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="oauthResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Facebook OAuth Testing Interface</p>
                    <p>Facebook OAuth integration testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let oauthState = {
            connected: false,
            user: null,
            accessToken: null,
            expiresAt: null
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Check OAuth status
            await checkOAuthStatus();

            // Handle OAuth callback if present
            handleOAuthCallback();
        }

        // OAuth Flow Functions
        async function initiateFacebookOAuth() {
            updateFlowStep(1, 'active');

            const appId = document.getElementById('appId').textContent;
            const redirectUri = document.getElementById('redirectUri').textContent;
            const scopes = 'email,public_profile';
            const state = generateRandomState();

            // Store state for validation
            localStorage.setItem('oauth_state', state);

            const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
                `client_id=${encodeURIComponent(appId)}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `scope=${encodeURIComponent(scopes)}&` +
                `state=${encodeURIComponent(state)}&` +
                `response_type=code`;

            try {
                // In a real implementation, this would redirect to Facebook
                window.notificationManager.info('Redirecting to Facebook OAuth...');

                // Simulate OAuth flow
                setTimeout(() => {
                    simulateOAuthCallback();
                }, 2000);

                showResponse('oauthResponse', {
                    action: 'OAuth Initiation',
                    authUrl: authUrl,
                    state: state,
                    message: 'In real implementation, user would be redirected to Facebook'
                }, 'info');

            } catch (error) {
                window.notificationManager.error('Failed to initiate OAuth flow');
                updateFlowStep(1, '');

                showResponse('oauthResponse', {
                    success: false,
                    error: 'OAuth initiation failed',
                    details: error.message
                }, 'error');
            }
        }

        function simulateOAuthCallback() {
            updateFlowStep(1, 'completed');
            updateFlowStep(2, 'completed');
            updateFlowStep(3, 'active');

            window.notificationManager.info('User granted permissions');

            setTimeout(() => {
                const authCode = 'mock_fb_auth_code_' + Date.now();
                handleAuthorizationCode(authCode);
            }, 1000);
        }

        async function handleAuthorizationCode(code) {
            updateFlowStep(3, 'completed');
            updateFlowStep(4, 'active');

            try {
                const response = await window.apiClient.request('POST', '/auth/facebook/callback', {
                    code: code,
                    state: localStorage.getItem('oauth_state')
                });

                if (response.success) {
                    updateFlowStep(4, 'completed');
                    updateFlowStep(5, 'completed');

                    oauthState.connected = true;
                    oauthState.user = response.data.user;
                    oauthState.accessToken = response.data.accessToken;
                    oauthState.expiresAt = new Date(Date.now() + 7200000); // 2 hours (Facebook default)

                    updateOAuthDisplay();
                    window.notificationManager.success('Facebook OAuth completed successfully');
                } else {
                    throw new Error(response.error || 'Token exchange failed');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate successful OAuth
                updateFlowStep(4, 'completed');
                updateFlowStep(5, 'completed');

                oauthState.connected = true;
                oauthState.user = {
                    id: 'facebook_123456789',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    picture: {
                        data: {
                            url: null
                        }
                    }
                };
                oauthState.accessToken = 'mock_fb_access_token_' + Date.now();
                oauthState.expiresAt = new Date(Date.now() + 7200000);

                updateOAuthDisplay();
                window.notificationManager.success('Facebook OAuth completed successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        user: oauthState.user,
                        accessToken: oauthState.accessToken
                    },
                    message: 'Mock Facebook OAuth completion successful (endpoint may not be available)'
                }, 'warning');
            }

            // Clear stored state
            localStorage.removeItem('oauth_state');
        }

        function handleOAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const error = urlParams.get('error');
            const errorDescription = urlParams.get('error_description');

            if (error) {
                window.notificationManager.error(`Facebook OAuth error: ${error}`);
                showResponse('oauthResponse', {
                    success: false,
                    error: error,
                    error_description: errorDescription,
                    details: 'Facebook OAuth authorization was denied or failed'
                }, 'error');
                return;
            }

            if (code && state) {
                const storedState = localStorage.getItem('oauth_state');
                if (state !== storedState) {
                    window.notificationManager.error('Invalid OAuth state parameter');
                    return;
                }

                handleAuthorizationCode(code);
            }
        }

        async function checkOAuthStatus() {
            try {
                const response = await window.apiClient.request('GET', '/auth/facebook/status');

                if (response.success && response.data.connected) {
                    oauthState = response.data;
                    updateOAuthDisplay();
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // OAuth not connected or endpoint not available
                console.log('Facebook OAuth status check failed:', error.message);
            }
        }

        // OAuth Management Functions
        function updateOAuthDisplay() {
            const statusElement = document.getElementById('oauthStatus');
            const userInfoElement = document.getElementById('userInfo');

            if (oauthState.connected && oauthState.user) {
                // Update status to connected
                statusElement.innerHTML = `
                    <div class="status-icon connected">
                        <i class="fab fa-facebook"></i>
                    </div>
                    <div class="status-title">Facebook Account Connected</div>
                    <div class="status-description">Successfully connected to ${oauthState.user.email}</div>
                    <button class="facebook-button disconnect" onclick="disconnectFacebook()">
                        <i class="fas fa-unlink"></i>
                        Disconnect Facebook Account
                    </button>
                `;

                // Show user info
                userInfoElement.style.display = 'block';
                document.getElementById('userName').textContent = oauthState.user.name;
                document.getElementById('userEmail').textContent = oauthState.user.email;
                document.getElementById('userAvatar').textContent = getInitials(oauthState.user.name);
                document.getElementById('accessToken').textContent = oauthState.accessToken || 'No token available';

                // Update all flow steps to completed
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, 'completed');
                }
            } else {
                // Update status to disconnected
                statusElement.innerHTML = `
                    <div class="status-icon disconnected">
                        <i class="fab fa-facebook"></i>
                    </div>
                    <div class="status-title">Facebook Account Not Connected</div>
                    <div class="status-description">Connect your Facebook account to enable OAuth authentication</div>
                    <button class="facebook-button" onclick="initiateFacebookOAuth()">
                        <div class="facebook-icon">
                            <i class="fab fa-facebook-f" style="color: #1877f2;"></i>
                        </div>
                        Continue with Facebook
                    </button>
                `;

                // Hide user info
                userInfoElement.style.display = 'none';

                // Reset flow steps
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, '');
                }
            }
        }

        async function disconnectFacebook() {
            if (!confirm('Are you sure you want to disconnect your Facebook account?')) {
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/facebook/disconnect');

                if (response.success) {
                    oauthState = {
                        connected: false,
                        user: null,
                        accessToken: null,
                        expiresAt: null
                    };

                    updateOAuthDisplay();
                    window.notificationManager.success('Facebook account disconnected');
                } else {
                    window.notificationManager.error(response.error || 'Failed to disconnect Facebook account');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate disconnection
                oauthState = {
                    connected: false,
                    user: null,
                    accessToken: null,
                    expiresAt: null
                };

                updateOAuthDisplay();
                window.notificationManager.success('Facebook account disconnected (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    message: 'Mock Facebook disconnect successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function updateFlowStep(stepNumber, status) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            const numberElement = stepElement.querySelector('.step-number');

            // Remove all status classes
            stepElement.classList.remove('active', 'completed');
            numberElement.classList.remove('active', 'completed');

            // Add new status class
            if (status) {
                stepElement.classList.add(status);
                numberElement.classList.add(status);
            }
        }

        // Token Management Functions
        async function testTokenValidation() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Facebook account first.');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/auth/facebook/validate-token', {
                    headers: {
                        'Authorization': `Bearer ${oauthState.accessToken}`
                    }
                });

                if (response.success) {
                    window.notificationManager.success('Access token is valid');
                } else {
                    window.notificationManager.error('Access token is invalid or expired');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate token validation
                const isExpired = oauthState.expiresAt && new Date() > oauthState.expiresAt;

                if (isExpired) {
                    window.notificationManager.warning('Access token has expired');
                    showResponse('oauthResponse', {
                        success: false,
                        error: 'Token expired',
                        details: 'Access token has expired and needs to be refreshed'
                    }, 'warning');
                } else {
                    window.notificationManager.success('Access token is valid (simulated)');
                    showResponse('oauthResponse', {
                        success: true,
                        data: { valid: true, expiresAt: oauthState.expiresAt },
                        message: 'Mock token validation successful (endpoint may not be available)'
                    }, 'warning');
                }
            }
        }

        async function testTokenRefresh() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Facebook account first.');
                return;
            }

            // Note: Facebook doesn't use refresh tokens like Google
            // Instead, tokens are extended or re-obtained
            try {
                const response = await window.apiClient.request('POST', '/auth/facebook/extend-token', {
                    accessToken: oauthState.accessToken
                });

                if (response.success) {
                    oauthState.accessToken = response.data.accessToken;
                    oauthState.expiresAt = new Date(Date.now() + 7200000); // 2 hours

                    document.getElementById('accessToken').textContent = oauthState.accessToken;
                    window.notificationManager.success('Access token extended successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to extend token');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate token extension
                oauthState.accessToken = 'extended_fb_token_' + Date.now();
                oauthState.expiresAt = new Date(Date.now() + 7200000);

                document.getElementById('accessToken').textContent = oauthState.accessToken;
                window.notificationManager.success('Access token extended successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        accessToken: oauthState.accessToken,
                        expiresAt: oauthState.expiresAt
                    },
                    message: 'Mock token extension successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function testUserInfo() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Facebook account first.');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/auth/facebook/userinfo', {
                    headers: {
                        'Authorization': `Bearer ${oauthState.accessToken}`
                    }
                });

                if (response.success) {
                    window.notificationManager.success('User info retrieved successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to retrieve user info');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user info retrieval
                const userInfo = {
                    id: oauthState.user.id,
                    name: oauthState.user.name,
                    email: oauthState.user.email,
                    picture: oauthState.user.picture,
                    first_name: oauthState.user.name.split(' ')[0],
                    last_name: oauthState.user.name.split(' ')[1] || ''
                };

                window.notificationManager.success('User info retrieved successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: userInfo,
                    message: 'Mock user info retrieval successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Quick Test Functions
        function testOAuthFlow() {
            if (oauthState.connected) {
                window.notificationManager.info('Facebook account already connected. Disconnect first to test flow.');
                return;
            }

            initiateFacebookOAuth();
        }

        // Test Scenario Functions
        function simulateCompleteFlow() {
            window.notificationManager.info('Simulating complete Facebook OAuth flow...');

            // Reset flow
            for (let i = 1; i <= 5; i++) {
                updateFlowStep(i, '');
            }

            // Simulate each step
            setTimeout(() => {
                updateFlowStep(1, 'active');
                window.notificationManager.info('Step 1: Authorization request initiated');
            }, 500);

            setTimeout(() => {
                updateFlowStep(1, 'completed');
                updateFlowStep(2, 'active');
                window.notificationManager.info('Step 2: User consent obtained');
            }, 1500);

            setTimeout(() => {
                updateFlowStep(2, 'completed');
                updateFlowStep(3, 'active');
                window.notificationManager.info('Step 3: Authorization code received');
            }, 2500);

            setTimeout(() => {
                updateFlowStep(3, 'completed');
                updateFlowStep(4, 'active');
                window.notificationManager.info('Step 4: Token exchange in progress');
            }, 3500);

            setTimeout(() => {
                updateFlowStep(4, 'completed');
                updateFlowStep(5, 'completed');
                window.notificationManager.success('Step 5: Facebook OAuth flow completed successfully');
            }, 4500);
        }

        function testInvalidApp() {
            window.notificationManager.error('Testing invalid Facebook App ID...');

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: false,
                    error: 'invalid_client_id',
                    error_description: 'Invalid Facebook App ID.',
                    details: 'App ID is invalid or not configured properly'
                }, 'error');

                window.notificationManager.error('OAuth failed: Invalid Facebook App ID');
            }, 1000);
        }

        function testUserDenial() {
            window.notificationManager.warning('Simulating user denial...');

            updateFlowStep(1, 'completed');
            updateFlowStep(2, 'active');

            setTimeout(() => {
                updateFlowStep(2, '');

                showResponse('oauthResponse', {
                    success: false,
                    error: 'access_denied',
                    error_description: 'The user denied the request.',
                    details: 'User clicked "Cancel" on the Facebook login dialog'
                }, 'error');

                window.notificationManager.error('OAuth failed: User denied access');

                // Reset flow
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, '');
                }
            }, 2000);
        }

        function testTokenExpiration() {
            if (!oauthState.connected) {
                window.notificationManager.error('No Facebook account connected. Connect first to test token expiration.');
                return;
            }

            window.notificationManager.warning('Simulating token expiration...');

            // Set token as expired
            oauthState.expiresAt = new Date(Date.now() - 1000);

            setTimeout(() => {
                testTokenValidation();
            }, 1000);
        }

        function testPermissionScopes() {
            window.notificationManager.info('Testing different Facebook permission scopes...');

            const scopes = [
                { name: 'public_profile', description: 'Access to public profile information' },
                { name: 'email', description: 'Access to email address' },
                { name: 'user_friends', description: 'Access to friends list' },
                { name: 'user_photos', description: 'Access to user photos' },
                { name: 'publish_to_groups', description: 'Ability to publish to groups' }
            ];

            scopes.forEach((scope, index) => {
                setTimeout(() => {
                    window.notificationManager.info(`Testing scope: ${scope.name} - ${scope.description}`);
                }, (index + 1) * 1000);
            });

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: true,
                    data: { scopes: scopes },
                    message: 'Permission scope validation test completed'
                }, 'info');
            }, 6000);
        }

        function testGraphAPI() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Facebook account first.');
                return;
            }

            window.notificationManager.info('Testing Facebook Graph API access...');

            const endpoints = [
                { endpoint: '/me', description: 'Get user profile' },
                { endpoint: '/me/friends', description: 'Get friends list' },
                { endpoint: '/me/photos', description: 'Get user photos' },
                { endpoint: '/me/posts', description: 'Get user posts' }
            ];

            endpoints.forEach((api, index) => {
                setTimeout(() => {
                    window.notificationManager.info(`Testing: ${api.endpoint} - ${api.description}`);
                }, (index + 1) * 1000);
            });

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        endpoints: endpoints,
                        accessToken: oauthState.accessToken,
                        testResults: 'All Graph API endpoints accessible'
                    },
                    message: 'Facebook Graph API test completed'
                }, 'success');

                window.notificationManager.success('Facebook Graph API access test completed');
            }, 5000);
        }

        // Helper Functions
        function generateRandomState() {
            return Math.random().toString(36).substring(2, 15) +
                   Math.random().toString(36).substring(2, 15);
        }

        function getInitials(name) {
            return name.split(' ').map(n => n[0]).join('').toUpperCase();
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
