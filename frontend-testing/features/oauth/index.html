<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Integration Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .oauth-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .oauth-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .oauth-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .oauth-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .provider-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .provider-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .provider-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .provider-icon.google {
            color: #4285f4;
        }

        .provider-icon.facebook {
            color: #1877f2;
        }

        .provider-icon.github {
            color: #333;
        }

        .provider-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .provider-status {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }

        .provider-status.connected {
            background: var(--success-color);
            color: white;
        }

        .provider-status.disconnected {
            background: var(--text-secondary);
            color: white;
        }

        .provider-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .oauth-flow-steps {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .flow-step {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 1rem;
            background: var(--hover-background);
            border-radius: 4px;
            border-left: 4px solid var(--border-color);
        }

        .flow-step.active {
            border-left-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .flow-step.completed {
            border-left-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.1);
        }

        .flow-step.error {
            border-left-color: var(--error-color);
            background: rgba(var(--error-color-rgb), 0.1);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .step-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .account-list {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .account-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .account-item:last-child {
            border-bottom: none;
        }

        .account-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .account-info {
            flex: 1;
        }

        .account-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .account-email {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .account-provider {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
        }

        .account-provider.google {
            background: #4285f4;
            color: white;
        }

        .account-provider.facebook {
            background: #1877f2;
            color: white;
        }

        .account-provider.github {
            background: #333;
            color: white;
        }

        .oauth-config {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-weight: bold;
        }

        .config-value {
            font-family: monospace;
            background: var(--hover-background);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .test-scenario {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .popup-window {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: none;
        }

        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .callback-info {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .security-warning {
            background: rgba(var(--warning-color-rgb), 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .security-warning .warning-icon {
            color: var(--warning-color);
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-google"></i>
                    <h1>OAuth Integration Testing</h1>
                    <span class="subtitle">Social Authentication Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Hub
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Navigation Tabs -->
            <div class="oauth-tabs">
                <button class="oauth-tab active" onclick="switchTab('providers')">
                    <i class="fas fa-link"></i>
                    OAuth Providers
                </button>
                <button class="oauth-tab" onclick="switchTab('flow-testing')">
                    <i class="fas fa-route"></i>
                    Flow Testing
                </button>
                <button class="oauth-tab" onclick="switchTab('account-linking')">
                    <i class="fas fa-user-friends"></i>
                    Account Linking
                </button>
                <button class="oauth-tab" onclick="switchTab('configuration')">
                    <i class="fas fa-cog"></i>
                    Configuration
                </button>
                <button class="oauth-tab" onclick="switchTab('test-scenarios')">
                    <i class="fas fa-flask"></i>
                    Test Scenarios
                </button>
            </div>

            <!-- OAuth Providers Tab -->
            <div class="tab-content active" id="providers-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>OAuth Provider Integration</h3>
                        <p>Test OAuth authentication with supported providers</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="checkProviderStatus()">
                                <i class="fas fa-sync"></i>
                                Check Status
                            </button>
                            <button class="btn btn-sm btn-info" onclick="testAllProviders()">
                                <i class="fas fa-play"></i>
                                Test All
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearProviderDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="provider-grid" id="providerGrid">
                            <!-- Provider cards will be populated here -->
                        </div>

                        <div id="providersResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Flow Testing Tab -->
            <div class="tab-content" id="flow-testing-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>OAuth Flow Testing</h3>
                        <p>Test complete OAuth authentication flows</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="startOAuthFlow('google')">
                                <i class="fab fa-google"></i>
                                Test Google Flow
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="startOAuthFlow('facebook')">
                                <i class="fab fa-facebook"></i>
                                Test Facebook Flow
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="startOAuthFlow('github')">
                                <i class="fab fa-github"></i>
                                Test GitHub Flow
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearFlowDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="oauth-flow-steps" id="flowSteps">
                            <!-- Flow steps will be populated here -->
                        </div>

                        <div class="callback-info" id="callbackInfo" style="display: none;">
                            <!-- Callback information will be displayed here -->
                        </div>

                        <div id="flowTestingResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Account Linking Tab -->
            <div class="tab-content" id="account-linking-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Account Linking Management</h3>
                        <p>Link and unlink OAuth accounts for authenticated users</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadLinkedAccounts()">
                                <i class="fas fa-sync"></i>
                                Load Accounts
                            </button>
                            <button class="btn btn-sm btn-success" onclick="linkAccount('google')">
                                <i class="fab fa-google"></i>
                                Link Google
                            </button>
                            <button class="btn btn-sm btn-success" onclick="linkAccount('facebook')">
                                <i class="fab fa-facebook"></i>
                                Link Facebook
                            </button>
                            <button class="btn btn-sm btn-success" onclick="linkAccount('github')">
                                <i class="fab fa-github"></i>
                                Link GitHub
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearAccountDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="security-warning">
                            <i class="fas fa-exclamation-triangle warning-icon"></i>
                            <strong>Security Note:</strong> Account linking requires an authenticated session.
                            Make sure you're logged in before testing linking functionality.
                        </div>

                        <div class="account-list" id="linkedAccountsList">
                            <!-- Linked accounts will be displayed here -->
                        </div>

                        <div id="accountLinkingResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div class="tab-content" id="configuration-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>OAuth Configuration</h3>
                        <p>View and test OAuth provider configurations</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadOAuthConfig()">
                                <i class="fas fa-sync"></i>
                                Load Config
                            </button>
                            <button class="btn btn-sm btn-info" onclick="testProviderEndpoints()">
                                <i class="fas fa-network-wired"></i>
                                Test Endpoints
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="validateConfiguration()">
                                <i class="fas fa-check-circle"></i>
                                Validate Config
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearConfigDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="oauth-config" id="oauthConfig">
                            <!-- Configuration will be displayed here -->
                        </div>

                        <div id="configurationResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Test Scenarios Tab -->
            <div class="tab-content" id="test-scenarios-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>OAuth Test Scenarios</h3>
                        <p>Comprehensive OAuth testing scenarios and edge cases</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="runAllScenarios()">
                                <i class="fas fa-play"></i>
                                Run All Tests
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateTestReport()">
                                <i class="fas fa-file-alt"></i>
                                Generate Report
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearScenariosDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div id="testScenarios">
                            <!-- Test scenarios will be populated here -->
                        </div>

                        <div id="testScenariosResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- OAuth Popup Window -->
    <div class="popup-overlay" id="popupOverlay"></div>
    <div class="popup-window" id="oauthPopup">
        <div class="popup-header">
            <h3 id="popupTitle">OAuth Authentication</h3>
            <button class="btn btn-sm btn-secondary" onclick="closeOAuthPopup()">
                <i class="fas fa-times"></i>
                Close
            </button>
        </div>
        <div class="popup-body">
            <p id="popupMessage">Redirecting to OAuth provider...</p>
            <div class="popup-actions">
                <button class="btn btn-primary" id="popupActionBtn" onclick="proceedWithOAuth()">
                    <i class="fas fa-external-link-alt"></i>
                    Open OAuth Provider
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 OAuth Integration Testing Interface</p>
                    <p>Social authentication testing and integration</p>
                </div>
                <div class="footer-links">
                    <a href="../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentOAuthFlow = null;
        let oauthWindow = null;
        let linkedAccounts = [];
        let oauthConfig = {};

        // OAuth provider configurations
        const providers = {
            google: {
                name: 'Google',
                icon: 'fab fa-google',
                color: '#4285f4',
                scopes: ['openid', 'profile', 'email']
            },
            facebook: {
                name: 'Facebook',
                icon: 'fab fa-facebook',
                color: '#1877f2',
                scopes: ['email', 'public_profile']
            },
            github: {
                name: 'GitHub',
                icon: 'fab fa-github',
                color: '#333',
                scopes: ['user:email', 'read:user']
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load initial data
            await checkProviderStatus();
            loadTestScenarios();
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.oauth-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            // Load data for specific tabs
            if (tabName === 'account-linking' && window.authManager && window.authManager.isAuthenticated()) {
                loadLinkedAccounts();
            } else if (tabName === 'configuration') {
                loadOAuthConfig();
            }
        }

        // Provider Status Functions
        async function checkProviderStatus() {
            const providerGrid = document.getElementById('providerGrid');
            let providersHTML = '';

            for (const [key, provider] of Object.entries(providers)) {
                const isConfigured = await checkProviderConfiguration(key);
                const status = isConfigured ? 'configured' : 'not-configured';

                providersHTML += `
                    <div class="provider-card">
                        <div class="provider-icon ${key}">
                            <i class="${provider.icon}"></i>
                        </div>
                        <div class="provider-name">${provider.name}</div>
                        <div class="provider-status ${status}">
                            ${isConfigured ? 'Configured' : 'Not Configured'}
                        </div>
                        <div class="provider-actions">
                            <button class="btn btn-primary" onclick="testOAuthLogin('${key}')" ${!isConfigured ? 'disabled' : ''}>
                                <i class="fas fa-sign-in-alt"></i>
                                Test Login
                            </button>
                            <button class="btn btn-secondary" onclick="viewProviderConfig('${key}')">
                                <i class="fas fa-cog"></i>
                                View Config
                            </button>
                            <button class="btn btn-info" onclick="testProviderEndpoint('${key}')">
                                <i class="fas fa-network-wired"></i>
                                Test Endpoint
                            </button>
                        </div>
                    </div>
                `;
            }

            providerGrid.innerHTML = providersHTML;
        }

        async function checkProviderConfiguration(provider) {
            try {
                // This would check if the provider is properly configured
                // For now, we'll simulate this check
                return Math.random() > 0.3; // Randomly return true/false for demo
            } catch (error) {
                return false;
            }
        }

        async function testOAuthLogin(provider) {
            if (!providers[provider]) {
                window.notificationManager.error('Unknown OAuth provider');
                return;
            }

            try {
                // Start OAuth flow
                currentOAuthFlow = provider;
                showOAuthPopup(provider);

                // Simulate OAuth redirect
                const authUrl = `http://localhost:3000/api/v1/auth/oauth/${provider}`;
                window.notificationManager.info(`Starting OAuth flow for ${providers[provider].name}`);

                // In a real implementation, this would open the OAuth provider's authorization page
                setTimeout(() => {
                    simulateOAuthCallback(provider);
                }, 3000);

            } catch (error) {
                window.notificationManager.error('Failed to start OAuth flow: ' + error.message);
            }
        }

        function showOAuthPopup(provider) {
            const popup = document.getElementById('oauthPopup');
            const overlay = document.getElementById('popupOverlay');
            const title = document.getElementById('popupTitle');
            const message = document.getElementById('popupMessage');
            const actionBtn = document.getElementById('popupActionBtn');

            title.textContent = `${providers[provider].name} OAuth`;
            message.textContent = `Redirecting to ${providers[provider].name} for authentication...`;
            actionBtn.onclick = () => proceedWithOAuth(provider);

            overlay.style.display = 'block';
            popup.style.display = 'block';
        }

        function proceedWithOAuth(provider) {
            // In a real implementation, this would open the OAuth provider's page
            const authUrl = `http://localhost:3000/api/v1/auth/oauth/${provider}`;
            window.notificationManager.info(`Opening ${providers[provider].name} OAuth page...`);

            // Simulate the OAuth flow
            updateFlowSteps(provider, 'redirect');
            closeOAuthPopup();
        }

        function closeOAuthPopup() {
            document.getElementById('oauthPopup').style.display = 'none';
            document.getElementById('popupOverlay').style.display = 'none';
        }

        function simulateOAuthCallback(provider) {
            // Simulate successful OAuth callback
            const callbackData = {
                provider: provider,
                code: 'mock_auth_code_' + Date.now(),
                state: 'mock_state_token',
                user: {
                    id: 'user_' + Date.now(),
                    email: `user@${provider}.com`,
                    name: `${providers[provider].name} User`,
                    avatar: null
                }
            };

            updateFlowSteps(provider, 'callback', callbackData);
            displayCallbackInfo(callbackData);
            window.notificationManager.success(`OAuth flow completed for ${providers[provider].name}`);
        }

        async function testAllProviders() {
            window.notificationManager.info('Testing all OAuth providers...');

            for (const provider of Object.keys(providers)) {
                const isConfigured = await checkProviderConfiguration(provider);
                if (isConfigured) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await testProviderEndpoint(provider);
                }
            }

            window.notificationManager.success('All provider tests completed');
        }

        async function testProviderEndpoint(provider) {
            try {
                const response = await fetch(`http://localhost:3000/api/v1/auth/oauth/${provider}`, {
                    method: 'GET',
                    redirect: 'manual' // Don't follow redirects
                });

                if (response.status === 302 || response.status === 200) {
                    window.notificationManager.success(`${providers[provider].name} endpoint is working`);
                } else {
                    window.notificationManager.warning(`${providers[provider].name} endpoint returned status ${response.status}`);
                }
            } catch (error) {
                window.notificationManager.error(`${providers[provider].name} endpoint test failed: ${error.message}`);
            }
        }

        function viewProviderConfig(provider) {
            switchTab('configuration');
            loadOAuthConfig(provider);
        }

        function clearProviderDisplay() {
            document.getElementById('providerGrid').innerHTML = '';
            hideResponse('providersResponse');
        }

        // Flow Testing Functions
        function startOAuthFlow(provider) {
            currentOAuthFlow = provider;
            initializeFlowSteps(provider);
            testOAuthLogin(provider);
        }

        function initializeFlowSteps(provider) {
            const flowSteps = document.getElementById('flowSteps');

            flowSteps.innerHTML = `
                <h4>OAuth Flow Steps for ${providers[provider].name}</h4>
                <div class="flow-step" id="step-initiate">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Initiate OAuth Flow</div>
                        <div class="step-description">Generate authorization URL and redirect user</div>
                    </div>
                </div>
                <div class="flow-step" id="step-redirect">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">User Authorization</div>
                        <div class="step-description">User grants permission on provider's site</div>
                    </div>
                </div>
                <div class="flow-step" id="step-callback">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Handle Callback</div>
                        <div class="step-description">Process authorization code and exchange for tokens</div>
                    </div>
                </div>
                <div class="flow-step" id="step-profile">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">Fetch User Profile</div>
                        <div class="step-description">Retrieve user information from provider</div>
                    </div>
                </div>
                <div class="flow-step" id="step-complete">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-title">Complete Authentication</div>
                        <div class="step-description">Create or update user account and establish session</div>
                    </div>
                </div>
            `;
        }

        function updateFlowSteps(provider, currentStep, data = null) {
            const steps = ['initiate', 'redirect', 'callback', 'profile', 'complete'];
            const currentIndex = steps.indexOf(currentStep);

            // Mark previous steps as completed
            for (let i = 0; i <= currentIndex; i++) {
                const stepElement = document.getElementById(`step-${steps[i]}`);
                if (stepElement) {
                    stepElement.classList.remove('active', 'error');
                    stepElement.classList.add('completed');
                }
            }

            // Mark current step as active
            if (currentIndex < steps.length - 1) {
                const nextStepElement = document.getElementById(`step-${steps[currentIndex + 1]}`);
                if (nextStepElement) {
                    nextStepElement.classList.add('active');
                }
            }
        }

        function displayCallbackInfo(data) {
            const callbackInfo = document.getElementById('callbackInfo');

            callbackInfo.innerHTML = `
                <h4>OAuth Callback Information</h4>
                <strong>Provider:</strong> ${data.provider}<br>
                <strong>Authorization Code:</strong> ${data.code}<br>
                <strong>State Token:</strong> ${data.state}<br>
                <strong>User ID:</strong> ${data.user.id}<br>
                <strong>User Email:</strong> ${data.user.email}<br>
                <strong>User Name:</strong> ${data.user.name}
            `;

            callbackInfo.style.display = 'block';
        }

        function clearFlowDisplay() {
            document.getElementById('flowSteps').innerHTML = '';
            document.getElementById('callbackInfo').style.display = 'none';
            hideResponse('flowTestingResponse');
        }

        // Account Linking Functions
        async function loadLinkedAccounts() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first to view linked accounts');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/auth/oauth/accounts');

                if (response.success) {
                    linkedAccounts = response.data.accounts || [];
                    displayLinkedAccounts(linkedAccounts);
                    showResponse('accountLinkingResponse', response, 'success');
                } else {
                    showResponse('accountLinkingResponse', response, 'error');
                }
            } catch (error) {
                // Simulate linked accounts
                linkedAccounts = [
                    {
                        provider: 'google',
                        providerId: 'google_123456789',
                        email: '<EMAIL>',
                        name: 'John Doe',
                        linkedAt: new Date(Date.now() - ********).toISOString()
                    },
                    {
                        provider: 'github',
                        providerId: 'github_987654321',
                        email: '<EMAIL>',
                        name: 'John Doe',
                        linkedAt: new Date(Date.now() - *********).toISOString()
                    }
                ];

                displayLinkedAccounts(linkedAccounts);
                showResponse('accountLinkingResponse', {
                    success: true,
                    data: { accounts: linkedAccounts },
                    message: 'Mock data displayed (endpoint may not be available)'
                }, 'warning');
            }
        }

        function displayLinkedAccounts(accounts) {
            const accountsList = document.getElementById('linkedAccountsList');

            if (accounts.length === 0) {
                accountsList.innerHTML = `
                    <div class="account-item">
                        <div class="account-info">
                            <div class="account-name">No linked accounts</div>
                            <div class="account-email">Link OAuth accounts to see them here</div>
                        </div>
                    </div>
                `;
                return;
            }

            const accountsHTML = accounts.map(account => `
                <div class="account-item">
                    <div class="account-avatar">
                        <i class="${providers[account.provider].icon}"></i>
                    </div>
                    <div class="account-info">
                        <div class="account-name">${account.name}</div>
                        <div class="account-email">${account.email}</div>
                        <div style="margin-top: 0.5rem;">
                            <span class="account-provider ${account.provider}">${providers[account.provider].name}</span>
                            <span style="font-size: 0.8rem; color: var(--text-secondary);">
                                Linked ${new Date(account.linkedAt).toLocaleDateString()}
                            </span>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-danger" onclick="unlinkAccount('${account.provider}')">
                        <i class="fas fa-unlink"></i>
                        Unlink
                    </button>
                </div>
            `).join('');

            accountsList.innerHTML = accountsHTML;
        }

        async function linkAccount(provider) {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first to link accounts');
                return;
            }

            try {
                window.notificationManager.info(`Linking ${providers[provider].name} account...`);

                // This would redirect to the OAuth linking endpoint
                const linkUrl = `http://localhost:3000/api/v1/auth/oauth/link/${provider}`;

                // Simulate successful linking
                setTimeout(() => {
                    window.notificationManager.success(`${providers[provider].name} account linked successfully`);
                    loadLinkedAccounts();
                }, 2000);

            } catch (error) {
                window.notificationManager.error(`Failed to link ${providers[provider].name} account: ${error.message}`);
            }
        }

        async function unlinkAccount(provider) {
            if (!confirm(`Are you sure you want to unlink your ${providers[provider].name} account?`)) {
                return;
            }

            try {
                const response = await window.apiClient.request('DELETE', `/auth/oauth/unlink/${provider}`);

                if (response.success) {
                    window.notificationManager.success(`${providers[provider].name} account unlinked successfully`);
                    await loadLinkedAccounts();
                } else {
                    window.notificationManager.error(response.error || 'Failed to unlink account');
                }
            } catch (error) {
                window.notificationManager.error(`Failed to unlink account: ${error.message}`);
            }
        }

        function clearAccountDisplay() {
            document.getElementById('linkedAccountsList').innerHTML = '';
            hideResponse('accountLinkingResponse');
        }

        // Configuration Functions
        async function loadOAuthConfig(specificProvider = null) {
            const config = document.getElementById('oauthConfig');

            // Simulate OAuth configuration
            oauthConfig = {
                google: {
                    clientId: 'google-client-id-example',
                    clientSecret: '***hidden***',
                    redirectUri: 'http://localhost:3000/api/v1/auth/oauth/google/callback',
                    scopes: ['openid', 'profile', 'email'],
                    authUrl: 'https://accounts.google.com/oauth/authorize',
                    tokenUrl: 'https://oauth2.googleapis.com/token',
                    userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo'
                },
                facebook: {
                    clientId: 'facebook-app-id-example',
                    clientSecret: '***hidden***',
                    redirectUri: 'http://localhost:3000/api/v1/auth/oauth/facebook/callback',
                    scopes: ['email', 'public_profile'],
                    authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
                    tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
                    userInfoUrl: 'https://graph.facebook.com/me'
                },
                github: {
                    clientId: 'github-client-id-example',
                    clientSecret: '***hidden***',
                    redirectUri: 'http://localhost:3000/api/v1/auth/oauth/github/callback',
                    scopes: ['user:email', 'read:user'],
                    authUrl: 'https://github.com/login/oauth/authorize',
                    tokenUrl: 'https://github.com/login/oauth/access_token',
                    userInfoUrl: 'https://api.github.com/user'
                }
            };

            const providersToShow = specificProvider ? [specificProvider] : Object.keys(oauthConfig);
            let configHTML = '';

            providersToShow.forEach(provider => {
                const providerConfig = oauthConfig[provider];
                configHTML += `
                    <div class="oauth-config">
                        <h4>${providers[provider].name} Configuration</h4>
                        <div class="config-item">
                            <div class="config-label">Client ID:</div>
                            <div class="config-value">${providerConfig.clientId}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Client Secret:</div>
                            <div class="config-value">${providerConfig.clientSecret}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Redirect URI:</div>
                            <div class="config-value">${providerConfig.redirectUri}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Scopes:</div>
                            <div class="config-value">${providerConfig.scopes.join(', ')}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Auth URL:</div>
                            <div class="config-value">${providerConfig.authUrl}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Token URL:</div>
                            <div class="config-value">${providerConfig.tokenUrl}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">User Info URL:</div>
                            <div class="config-value">${providerConfig.userInfoUrl}</div>
                        </div>
                    </div>
                `;
            });

            config.innerHTML = configHTML;
        }

        async function testProviderEndpoints() {
            window.notificationManager.info('Testing OAuth provider endpoints...');

            const results = [];
            for (const [provider, config] of Object.entries(oauthConfig)) {
                try {
                    // Test auth endpoint (should return redirect)
                    const authResponse = await fetch(config.authUrl, { method: 'HEAD', mode: 'no-cors' });
                    results.push({ provider, endpoint: 'auth', status: 'reachable' });
                } catch (error) {
                    results.push({ provider, endpoint: 'auth', status: 'unreachable' });
                }
            }

            window.notificationManager.success('Endpoint testing completed');
            showResponse('configurationResponse', { success: true, data: { results } }, 'success');
        }

        function validateConfiguration() {
            window.notificationManager.info('Validating OAuth configuration...');

            const validationResults = [];
            for (const [provider, config] of Object.entries(oauthConfig)) {
                const issues = [];

                if (!config.clientId || config.clientId.includes('example')) {
                    issues.push('Client ID appears to be placeholder');
                }
                if (!config.redirectUri.startsWith('http')) {
                    issues.push('Invalid redirect URI format');
                }
                if (!config.scopes || config.scopes.length === 0) {
                    issues.push('No scopes configured');
                }

                validationResults.push({
                    provider,
                    valid: issues.length === 0,
                    issues
                });
            }

            const allValid = validationResults.every(result => result.valid);
            window.notificationManager[allValid ? 'success' : 'warning'](
                allValid ? 'All configurations are valid' : 'Some configurations have issues'
            );

            showResponse('configurationResponse', {
                success: true,
                data: { validation: validationResults }
            }, allValid ? 'success' : 'warning');
        }

        function clearConfigDisplay() {
            document.getElementById('oauthConfig').innerHTML = '';
            hideResponse('configurationResponse');
        }

        // Test Scenarios Functions
        function loadTestScenarios() {
            const scenarios = document.getElementById('testScenarios');

            const testScenarios = [
                {
                    title: 'Basic OAuth Flow',
                    description: 'Test complete OAuth authentication flow for each provider',
                    actions: ['test-google-flow', 'test-facebook-flow', 'test-github-flow']
                },
                {
                    title: 'Account Linking',
                    description: 'Test linking multiple OAuth accounts to a single user',
                    actions: ['link-google', 'link-facebook', 'link-github', 'view-linked']
                },
                {
                    title: 'Error Handling',
                    description: 'Test OAuth error scenarios and edge cases',
                    actions: ['test-invalid-state', 'test-expired-code', 'test-denied-permission']
                },
                {
                    title: 'Security Tests',
                    description: 'Test OAuth security features and CSRF protection',
                    actions: ['test-csrf-protection', 'test-state-validation', 'test-redirect-validation']
                },
                {
                    title: 'Provider Compatibility',
                    description: 'Test compatibility with different OAuth provider versions',
                    actions: ['test-provider-versions', 'test-scope-variations', 'test-response-formats']
                }
            ];

            const scenariosHTML = testScenarios.map(scenario => `
                <div class="test-scenario">
                    <div class="scenario-title">${scenario.title}</div>
                    <div class="scenario-description">${scenario.description}</div>
                    <div class="scenario-actions">
                        ${scenario.actions.map(action => `
                            <button class="btn btn-sm btn-outline" onclick="runTestScenario('${action}')">
                                <i class="fas fa-play"></i>
                                ${action.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </button>
                        `).join('')}
                    </div>
                </div>
            `).join('');

            scenarios.innerHTML = scenariosHTML;
        }

        function runTestScenario(scenario) {
            window.notificationManager.info(`Running test scenario: ${scenario.replace(/-/g, ' ')}`);

            // Simulate test execution
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% success rate
                window.notificationManager[success ? 'success' : 'error'](
                    `Test scenario "${scenario}" ${success ? 'passed' : 'failed'}`
                );
            }, 2000);
        }

        function runAllScenarios() {
            window.notificationManager.info('Running all OAuth test scenarios...');

            // Simulate running all scenarios
            let completed = 0;
            const total = 15; // Total number of test scenarios

            const interval = setInterval(() => {
                completed++;
                window.notificationManager.info(`Test progress: ${completed}/${total} scenarios completed`);

                if (completed >= total) {
                    clearInterval(interval);
                    window.notificationManager.success('All OAuth test scenarios completed');
                    generateTestReport();
                }
            }, 1000);
        }

        function generateTestReport() {
            window.notificationManager.info('Generating OAuth test report...');

            // Simulate report generation
            setTimeout(() => {
                const report = {
                    timestamp: new Date().toISOString(),
                    totalTests: 15,
                    passed: 13,
                    failed: 2,
                    providers: Object.keys(providers),
                    summary: 'OAuth integration tests completed with 86.7% success rate'
                };

                showResponse('testScenariosResponse', {
                    success: true,
                    data: { report },
                    message: 'Test report generated successfully'
                }, 'success');

                window.notificationManager.success('OAuth test report generated successfully');
            }, 2000);
        }

        function clearScenariosDisplay() {
            hideResponse('testScenariosResponse');
        }

        // Shared utility functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }

        function hideResponse(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'none';
        }
    </script>
</body>
</html>
