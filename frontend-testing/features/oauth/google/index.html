<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .oauth-container {
            max-width: 800px;
            margin: 2rem auto;
        }

        .oauth-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-icon.connected {
            color: var(--success-color);
        }

        .status-icon.disconnected {
            color: var(--text-secondary);
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .google-button {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .google-button:hover {
            background: #3367d6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }

        .google-button:active {
            transform: translateY(0);
        }

        .google-button.disconnect {
            background: var(--error-color);
        }

        .google-button.disconnect:hover {
            background: var(--error-color-dark);
        }

        .google-icon {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .oauth-flow {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .flow-step {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            position: relative;
        }

        .flow-step.active {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .flow-step.completed {
            border-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.1);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 0.5rem auto;
        }

        .step-number.active {
            background: var(--primary-color);
            color: white;
        }

        .step-number.completed {
            background: var(--success-color);
            color: white;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .step-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .user-info {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .permissions-list {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-icon {
            color: var(--success-color);
            margin-right: 0.5rem;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .oauth-config {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .config-value {
            font-family: monospace;
            font-size: 0.9rem;
            color: var(--text-secondary);
            background: var(--hover-background);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .token-display {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.8rem;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .scope-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .scope-tag {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .error-display {
            background: rgba(var(--error-color-rgb), 0.1);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: var(--error-color);
        }

        .success-display {
            background: rgba(var(--success-color-rgb), 0.1);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: var(--success-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-google"></i>
                    <h1>Google OAuth Testing</h1>
                    <span class="subtitle">Google OAuth Integration Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to OAuth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="oauth-container">
                <!-- OAuth Status -->
                <div class="oauth-status" id="oauthStatus">
                    <div class="status-icon disconnected">
                        <i class="fab fa-google"></i>
                    </div>
                    <div class="status-title">Google Account Not Connected</div>
                    <div class="status-description">Connect your Google account to enable OAuth authentication</div>
                    <button class="google-button" onclick="initiateGoogleOAuth()">
                        <div class="google-icon">
                            <i class="fab fa-google" style="color: #4285f4;"></i>
                        </div>
                        Sign in with Google
                    </button>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="testOAuthFlow()">
                        <i class="fas fa-play"></i>
                        Test OAuth Flow
                    </button>
                    <button class="btn btn-success" onclick="testTokenValidation()">
                        <i class="fas fa-check"></i>
                        Test Token Validation
                    </button>
                    <button class="btn btn-warning" onclick="testTokenRefresh()">
                        <i class="fas fa-sync"></i>
                        Test Token Refresh
                    </button>
                    <button class="btn btn-info" onclick="testUserInfo()">
                        <i class="fas fa-user"></i>
                        Test User Info
                    </button>
                </div>

                <!-- OAuth Flow Steps -->
                <div class="oauth-flow">
                    <h3>OAuth 2.0 Authorization Flow</h3>
                    <div class="flow-steps">
                        <div class="flow-step" id="step1">
                            <div class="step-number">1</div>
                            <div class="step-title">Authorization Request</div>
                            <div class="step-description">Redirect to Google OAuth</div>
                        </div>
                        <div class="flow-step" id="step2">
                            <div class="step-number">2</div>
                            <div class="step-title">User Consent</div>
                            <div class="step-description">User grants permissions</div>
                        </div>
                        <div class="flow-step" id="step3">
                            <div class="step-number">3</div>
                            <div class="step-title">Authorization Code</div>
                            <div class="step-description">Receive auth code</div>
                        </div>
                        <div class="flow-step" id="step4">
                            <div class="step-number">4</div>
                            <div class="step-title">Token Exchange</div>
                            <div class="step-description">Exchange code for tokens</div>
                        </div>
                        <div class="flow-step" id="step5">
                            <div class="step-number">5</div>
                            <div class="step-title">Access Resources</div>
                            <div class="step-description">Use tokens to access API</div>
                        </div>
                    </div>
                </div>

                <!-- OAuth Configuration -->
                <div class="oauth-config">
                    <h3>Google OAuth Configuration</h3>
                    <div class="config-item">
                        <div class="config-label">Client ID:</div>
                        <div class="config-value" id="clientId">your-google-client-id.apps.googleusercontent.com</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Redirect URI:</div>
                        <div class="config-value" id="redirectUri">http://localhost:3000/auth/google/callback</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Scopes:</div>
                        <div class="scope-tags">
                            <span class="scope-tag">openid</span>
                            <span class="scope-tag">profile</span>
                            <span class="scope-tag">email</span>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">Response Type:</div>
                        <div class="config-value">code</div>
                    </div>
                </div>

                <!-- User Information (Hidden by default) -->
                <div class="user-info" id="userInfo" style="display: none;">
                    <h3>Connected Google Account</h3>
                    <div class="user-profile">
                        <div class="user-avatar" id="userAvatar">JD</div>
                        <div class="user-details">
                            <div class="user-name" id="userName">John Doe</div>
                            <div class="user-email" id="userEmail"><EMAIL></div>
                        </div>
                        <button class="google-button disconnect" onclick="disconnectGoogle()">
                            <i class="fas fa-unlink"></i>
                            Disconnect
                        </button>
                    </div>

                    <div class="permissions-list">
                        <h4>Granted Permissions:</h4>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            Access your basic profile information
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            View your email address
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            Know who you are on Google
                        </div>
                    </div>

                    <div class="token-display" id="tokenDisplay">
                        <strong>Access Token:</strong><br>
                        <span id="accessToken">No token available</span>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Complete OAuth Flow</div>
                        <div class="scenario-description">Test the complete Google OAuth authorization flow</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="simulateCompleteFlow()">
                                <i class="fas fa-play"></i>
                                Simulate Flow
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Client ID</div>
                        <div class="scenario-description">Test OAuth flow with invalid client configuration</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="testInvalidClient()">
                                <i class="fas fa-times"></i>
                                Test Invalid Client
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">User Denial</div>
                        <div class="scenario-description">Test when user denies OAuth permissions</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testUserDenial()">
                                <i class="fas fa-user-slash"></i>
                                Test User Denial
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Token Expiration</div>
                        <div class="scenario-description">Test handling of expired access tokens</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testTokenExpiration()">
                                <i class="fas fa-clock"></i>
                                Test Expiration
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Scope Validation</div>
                        <div class="scenario-description">Test OAuth with different scope configurations</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testScopeValidation()">
                                <i class="fas fa-list"></i>
                                Test Scopes
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Account Linking</div>
                        <div class="scenario-description">Test linking Google account to existing user</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="testAccountLinking()">
                                <i class="fas fa-link"></i>
                                Test Linking
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="oauthResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Google OAuth Testing Interface</p>
                    <p>Google OAuth integration testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let oauthState = {
            connected: false,
            user: null,
            accessToken: null,
            refreshToken: null,
            expiresAt: null
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Check OAuth status
            await checkOAuthStatus();

            // Handle OAuth callback if present
            handleOAuthCallback();
        }

        // OAuth Flow Functions
        async function initiateGoogleOAuth() {
            updateFlowStep(1, 'active');

            const clientId = document.getElementById('clientId').textContent;
            const redirectUri = document.getElementById('redirectUri').textContent;
            const scopes = 'openid profile email';
            const state = generateRandomState();

            // Store state for validation
            localStorage.setItem('oauth_state', state);

            const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${encodeURIComponent(clientId)}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `response_type=code&` +
                `scope=${encodeURIComponent(scopes)}&` +
                `state=${encodeURIComponent(state)}&` +
                `access_type=offline&` +
                `prompt=consent`;

            try {
                // In a real implementation, this would redirect to Google
                window.notificationManager.info('Redirecting to Google OAuth...');

                // Simulate OAuth flow
                setTimeout(() => {
                    simulateOAuthCallback();
                }, 2000);

                showResponse('oauthResponse', {
                    action: 'OAuth Initiation',
                    authUrl: authUrl,
                    state: state,
                    message: 'In real implementation, user would be redirected to Google'
                }, 'info');

            } catch (error) {
                window.notificationManager.error('Failed to initiate OAuth flow');
                updateFlowStep(1, '');

                showResponse('oauthResponse', {
                    success: false,
                    error: 'OAuth initiation failed',
                    details: error.message
                }, 'error');
            }
        }

        function simulateOAuthCallback() {
            updateFlowStep(1, 'completed');
            updateFlowStep(2, 'completed');
            updateFlowStep(3, 'active');

            window.notificationManager.info('User granted permissions');

            setTimeout(() => {
                const authCode = 'mock_auth_code_' + Date.now();
                handleAuthorizationCode(authCode);
            }, 1000);
        }

        async function handleAuthorizationCode(code) {
            updateFlowStep(3, 'completed');
            updateFlowStep(4, 'active');

            try {
                const response = await window.apiClient.request('POST', '/auth/google/callback', {
                    code: code,
                    state: localStorage.getItem('oauth_state')
                });

                if (response.success) {
                    updateFlowStep(4, 'completed');
                    updateFlowStep(5, 'completed');

                    oauthState.connected = true;
                    oauthState.user = response.data.user;
                    oauthState.accessToken = response.data.accessToken;
                    oauthState.refreshToken = response.data.refreshToken;
                    oauthState.expiresAt = new Date(Date.now() + 3600000); // 1 hour

                    updateOAuthDisplay();
                    window.notificationManager.success('Google OAuth completed successfully');
                } else {
                    throw new Error(response.error || 'Token exchange failed');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate successful OAuth
                updateFlowStep(4, 'completed');
                updateFlowStep(5, 'completed');

                oauthState.connected = true;
                oauthState.user = {
                    id: 'google_123456789',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    picture: null,
                    verified_email: true
                };
                oauthState.accessToken = 'mock_access_token_' + Date.now();
                oauthState.refreshToken = 'mock_refresh_token_' + Date.now();
                oauthState.expiresAt = new Date(Date.now() + 3600000);

                updateOAuthDisplay();
                window.notificationManager.success('Google OAuth completed successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        user: oauthState.user,
                        accessToken: oauthState.accessToken,
                        refreshToken: oauthState.refreshToken
                    },
                    message: 'Mock OAuth completion successful (endpoint may not be available)'
                }, 'warning');
            }

            // Clear stored state
            localStorage.removeItem('oauth_state');
        }

        function handleOAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const error = urlParams.get('error');

            if (error) {
                window.notificationManager.error(`OAuth error: ${error}`);
                showResponse('oauthResponse', {
                    success: false,
                    error: error,
                    details: 'OAuth authorization was denied or failed'
                }, 'error');
                return;
            }

            if (code && state) {
                const storedState = localStorage.getItem('oauth_state');
                if (state !== storedState) {
                    window.notificationManager.error('Invalid OAuth state parameter');
                    return;
                }

                handleAuthorizationCode(code);
            }
        }

        async function checkOAuthStatus() {
            try {
                const response = await window.apiClient.request('GET', '/auth/google/status');

                if (response.success && response.data.connected) {
                    oauthState = response.data;
                    updateOAuthDisplay();
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // OAuth not connected or endpoint not available
                console.log('OAuth status check failed:', error.message);
            }
        }

        // OAuth Management Functions
        function updateOAuthDisplay() {
            const statusElement = document.getElementById('oauthStatus');
            const userInfoElement = document.getElementById('userInfo');

            if (oauthState.connected && oauthState.user) {
                // Update status to connected
                statusElement.innerHTML = `
                    <div class="status-icon connected">
                        <i class="fab fa-google"></i>
                    </div>
                    <div class="status-title">Google Account Connected</div>
                    <div class="status-description">Successfully connected to ${oauthState.user.email}</div>
                    <button class="google-button disconnect" onclick="disconnectGoogle()">
                        <i class="fas fa-unlink"></i>
                        Disconnect Google Account
                    </button>
                `;

                // Show user info
                userInfoElement.style.display = 'block';
                document.getElementById('userName').textContent = oauthState.user.name;
                document.getElementById('userEmail').textContent = oauthState.user.email;
                document.getElementById('userAvatar').textContent = getInitials(oauthState.user.name);
                document.getElementById('accessToken').textContent = oauthState.accessToken || 'No token available';

                // Update all flow steps to completed
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, 'completed');
                }
            } else {
                // Update status to disconnected
                statusElement.innerHTML = `
                    <div class="status-icon disconnected">
                        <i class="fab fa-google"></i>
                    </div>
                    <div class="status-title">Google Account Not Connected</div>
                    <div class="status-description">Connect your Google account to enable OAuth authentication</div>
                    <button class="google-button" onclick="initiateGoogleOAuth()">
                        <div class="google-icon">
                            <i class="fab fa-google" style="color: #4285f4;"></i>
                        </div>
                        Sign in with Google
                    </button>
                `;

                // Hide user info
                userInfoElement.style.display = 'none';

                // Reset flow steps
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, '');
                }
            }
        }

        async function disconnectGoogle() {
            if (!confirm('Are you sure you want to disconnect your Google account?')) {
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/google/disconnect');

                if (response.success) {
                    oauthState = {
                        connected: false,
                        user: null,
                        accessToken: null,
                        refreshToken: null,
                        expiresAt: null
                    };

                    updateOAuthDisplay();
                    window.notificationManager.success('Google account disconnected');
                } else {
                    window.notificationManager.error(response.error || 'Failed to disconnect Google account');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate disconnection
                oauthState = {
                    connected: false,
                    user: null,
                    accessToken: null,
                    refreshToken: null,
                    expiresAt: null
                };

                updateOAuthDisplay();
                window.notificationManager.success('Google account disconnected (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    message: 'Mock Google disconnect successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function updateFlowStep(stepNumber, status) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            const numberElement = stepElement.querySelector('.step-number');

            // Remove all status classes
            stepElement.classList.remove('active', 'completed');
            numberElement.classList.remove('active', 'completed');

            // Add new status class
            if (status) {
                stepElement.classList.add(status);
                numberElement.classList.add(status);
            }
        }

        // Token Management Functions
        async function testTokenValidation() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Google account first.');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/auth/google/validate-token', {
                    headers: {
                        'Authorization': `Bearer ${oauthState.accessToken}`
                    }
                });

                if (response.success) {
                    window.notificationManager.success('Access token is valid');
                } else {
                    window.notificationManager.error('Access token is invalid or expired');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate token validation
                const isExpired = oauthState.expiresAt && new Date() > oauthState.expiresAt;

                if (isExpired) {
                    window.notificationManager.warning('Access token has expired');
                    showResponse('oauthResponse', {
                        success: false,
                        error: 'Token expired',
                        details: 'Access token has expired and needs to be refreshed'
                    }, 'warning');
                } else {
                    window.notificationManager.success('Access token is valid (simulated)');
                    showResponse('oauthResponse', {
                        success: true,
                        data: { valid: true, expiresAt: oauthState.expiresAt },
                        message: 'Mock token validation successful (endpoint may not be available)'
                    }, 'warning');
                }
            }
        }

        async function testTokenRefresh() {
            if (!oauthState.refreshToken) {
                window.notificationManager.error('No refresh token available. Please reconnect Google account.');
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/google/refresh-token', {
                    refreshToken: oauthState.refreshToken
                });

                if (response.success) {
                    oauthState.accessToken = response.data.accessToken;
                    oauthState.expiresAt = new Date(Date.now() + 3600000); // 1 hour

                    document.getElementById('accessToken').textContent = oauthState.accessToken;
                    window.notificationManager.success('Access token refreshed successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to refresh token');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate token refresh
                oauthState.accessToken = 'refreshed_access_token_' + Date.now();
                oauthState.expiresAt = new Date(Date.now() + 3600000);

                document.getElementById('accessToken').textContent = oauthState.accessToken;
                window.notificationManager.success('Access token refreshed successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        accessToken: oauthState.accessToken,
                        expiresAt: oauthState.expiresAt
                    },
                    message: 'Mock token refresh successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function testUserInfo() {
            if (!oauthState.accessToken) {
                window.notificationManager.error('No access token available. Please connect Google account first.');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/auth/google/userinfo', {
                    headers: {
                        'Authorization': `Bearer ${oauthState.accessToken}`
                    }
                });

                if (response.success) {
                    window.notificationManager.success('User info retrieved successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to retrieve user info');
                }

                showResponse('oauthResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user info retrieval
                const userInfo = {
                    id: oauthState.user.id,
                    name: oauthState.user.name,
                    email: oauthState.user.email,
                    picture: oauthState.user.picture,
                    verified_email: oauthState.user.verified_email,
                    locale: 'en'
                };

                window.notificationManager.success('User info retrieved successfully (simulated)');

                showResponse('oauthResponse', {
                    success: true,
                    data: userInfo,
                    message: 'Mock user info retrieval successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Quick Test Functions
        function testOAuthFlow() {
            if (oauthState.connected) {
                window.notificationManager.info('Google account already connected. Disconnect first to test flow.');
                return;
            }

            initiateGoogleOAuth();
        }

        // Test Scenario Functions
        function simulateCompleteFlow() {
            window.notificationManager.info('Simulating complete OAuth flow...');

            // Reset flow
            for (let i = 1; i <= 5; i++) {
                updateFlowStep(i, '');
            }

            // Simulate each step
            setTimeout(() => {
                updateFlowStep(1, 'active');
                window.notificationManager.info('Step 1: Authorization request initiated');
            }, 500);

            setTimeout(() => {
                updateFlowStep(1, 'completed');
                updateFlowStep(2, 'active');
                window.notificationManager.info('Step 2: User consent obtained');
            }, 1500);

            setTimeout(() => {
                updateFlowStep(2, 'completed');
                updateFlowStep(3, 'active');
                window.notificationManager.info('Step 3: Authorization code received');
            }, 2500);

            setTimeout(() => {
                updateFlowStep(3, 'completed');
                updateFlowStep(4, 'active');
                window.notificationManager.info('Step 4: Token exchange in progress');
            }, 3500);

            setTimeout(() => {
                updateFlowStep(4, 'completed');
                updateFlowStep(5, 'completed');
                window.notificationManager.success('Step 5: OAuth flow completed successfully');
            }, 4500);
        }

        function testInvalidClient() {
            window.notificationManager.error('Testing invalid client ID...');

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: false,
                    error: 'invalid_client',
                    error_description: 'The OAuth client was not found.',
                    details: 'Client ID is invalid or not configured properly'
                }, 'error');

                window.notificationManager.error('OAuth failed: Invalid client ID');
            }, 1000);
        }

        function testUserDenial() {
            window.notificationManager.warning('Simulating user denial...');

            updateFlowStep(1, 'completed');
            updateFlowStep(2, 'active');

            setTimeout(() => {
                updateFlowStep(2, '');

                showResponse('oauthResponse', {
                    success: false,
                    error: 'access_denied',
                    error_description: 'The user denied the request.',
                    details: 'User clicked "Cancel" on the OAuth consent screen'
                }, 'error');

                window.notificationManager.error('OAuth failed: User denied access');

                // Reset flow
                for (let i = 1; i <= 5; i++) {
                    updateFlowStep(i, '');
                }
            }, 2000);
        }

        function testTokenExpiration() {
            if (!oauthState.connected) {
                window.notificationManager.error('No Google account connected. Connect first to test token expiration.');
                return;
            }

            window.notificationManager.warning('Simulating token expiration...');

            // Set token as expired
            oauthState.expiresAt = new Date(Date.now() - 1000);

            setTimeout(() => {
                testTokenValidation();
            }, 1000);
        }

        function testScopeValidation() {
            window.notificationManager.info('Testing different OAuth scopes...');

            const scopes = [
                { name: 'openid', description: 'OpenID Connect authentication' },
                { name: 'profile', description: 'Basic profile information' },
                { name: 'email', description: 'Email address access' },
                { name: 'https://www.googleapis.com/auth/drive.readonly', description: 'Google Drive read access' }
            ];

            scopes.forEach((scope, index) => {
                setTimeout(() => {
                    window.notificationManager.info(`Testing scope: ${scope.name} - ${scope.description}`);
                }, (index + 1) * 1000);
            });

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: true,
                    data: { scopes: scopes },
                    message: 'Scope validation test completed'
                }, 'info');
            }, 5000);
        }

        function testAccountLinking() {
            window.notificationManager.info('Testing account linking...');

            if (!oauthState.connected) {
                window.notificationManager.error('No Google account connected. Connect first to test linking.');
                return;
            }

            setTimeout(() => {
                showResponse('oauthResponse', {
                    success: true,
                    data: {
                        linkedAccount: {
                            provider: 'google',
                            providerId: oauthState.user.id,
                            email: oauthState.user.email,
                            linkedAt: new Date().toISOString()
                        }
                    },
                    message: 'Account linking test completed'
                }, 'success');

                window.notificationManager.success('Google account linked successfully');
            }, 2000);
        }

        // Helper Functions
        function generateRandomState() {
            return Math.random().toString(36).substring(2, 15) +
                   Math.random().toString(36).substring(2, 15);
        }

        function getInitials(name) {
            return name.split(' ').map(n => n[0]).join('').toUpperCase();
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
