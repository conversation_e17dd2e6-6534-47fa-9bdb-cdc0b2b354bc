<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MFA Verification Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .mfa-container {
            max-width: 500px;
            margin: 2rem auto;
        }

        .mfa-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .mfa-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .mfa-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .mfa-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .verification-input {
            font-family: monospace;
            font-size: 2rem;
            text-align: center;
            letter-spacing: 0.5rem;
            max-width: 250px;
            margin: 0 auto 2rem auto;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
        }

        .verification-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .verification-methods {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .method-option {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }

        .method-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .method-option.active {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .method-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            color: var(--primary-color);
            width: 30px;
            text-align: center;
        }

        .method-info {
            flex: 1;
        }

        .method-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .method-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .backup-code-input {
            font-family: monospace;
            font-size: 1.2rem;
            text-align: center;
            letter-spacing: 0.2rem;
            text-transform: uppercase;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .attempts-counter {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .attempts-counter.warning {
            background: rgba(var(--warning-color-rgb), 0.1);
            border-color: var(--warning-color);
        }

        .attempts-counter.danger {
            background: rgba(var(--error-color-rgb), 0.1);
            border-color: var(--error-color);
        }

        .timer {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: 1rem 0;
        }

        .timer.warning {
            color: var(--warning-color);
        }

        .timer.danger {
            color: var(--error-color);
        }

        .help-links {
            text-align: center;
            margin-top: 2rem;
        }

        .help-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 1rem;
            font-size: 0.9rem;
        }

        .help-links a:hover {
            text-decoration: underline;
        }

        .verification-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .status-icon.success {
            color: var(--success-color);
        }

        .status-icon.error {
            color: var(--error-color);
        }

        .status-icon.warning {
            color: var(--warning-color);
        }

        .status-icon.info {
            color: var(--info-color);
        }

        .status-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-message {
            color: var(--text-secondary);
        }

        .resend-section {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: var(--hover-background);
            border-radius: 8px;
        }

        .resend-timer {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .device-trust {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .device-trust label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .device-trust input {
            margin-right: 0.5rem;
        }

        .device-trust-info {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>MFA Verification Testing</h1>
                    <span class="subtitle">Multi-Factor Authentication Verification Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to MFA
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="mfa-container">
                <!-- Verification Status -->
                <div class="verification-status" id="verificationStatus">
                    <div class="status-icon info">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="status-title">Multi-Factor Authentication Required</div>
                    <div class="status-message">Please verify your identity using one of the methods below</div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-success" onclick="testValidCode()">
                        <i class="fas fa-check"></i>
                        Test Valid Code
                    </button>
                    <button class="btn btn-danger" onclick="testInvalidCode()">
                        <i class="fas fa-times"></i>
                        Test Invalid Code
                    </button>
                    <button class="btn btn-warning" onclick="testBackupCode()">
                        <i class="fas fa-key"></i>
                        Test Backup Code
                    </button>
                    <button class="btn btn-info" onclick="testResendCode()">
                        <i class="fas fa-paper-plane"></i>
                        Test Resend
                    </button>
                </div>

                <!-- MFA Verification Card -->
                <div class="mfa-card">
                    <div class="mfa-icon">
                        <i class="fas fa-mobile-alt" id="methodIcon"></i>
                    </div>
                    <div class="mfa-title" id="methodTitle">Enter Verification Code</div>
                    <div class="mfa-description" id="methodDescription">
                        Enter the 6-digit code from your authenticator app
                    </div>

                    <!-- Verification Methods -->
                    <div class="verification-methods">
                        <div class="method-option active" onclick="selectMethod('totp')" id="totpMethod">
                            <div class="method-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-title">Authenticator App</div>
                                <div class="method-description">Use your TOTP app to generate a code</div>
                            </div>
                        </div>

                        <div class="method-option" onclick="selectMethod('sms')" id="smsMethod">
                            <div class="method-icon">
                                <i class="fas fa-sms"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-title">SMS Code</div>
                                <div class="method-description">Receive code via text message</div>
                            </div>
                        </div>

                        <div class="method-option" onclick="selectMethod('backup')" id="backupMethod">
                            <div class="method-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-title">Backup Code</div>
                                <div class="method-description">Use one of your recovery codes</div>
                            </div>
                        </div>
                    </div>

                    <!-- Verification Form -->
                    <form id="verificationForm">
                        <div class="form-group">
                            <input type="text" id="verificationCode" class="verification-input" 
                                   placeholder="000000" maxlength="6" required autocomplete="off">
                        </div>

                        <!-- Attempts Counter -->
                        <div class="attempts-counter" id="attemptsCounter">
                            <div>Attempts remaining: <strong id="attemptsRemaining">3</strong></div>
                        </div>

                        <!-- Timer -->
                        <div class="timer" id="codeTimer" style="display: none;">
                            Code expires in: <span id="timeRemaining">05:00</span>
                        </div>

                        <!-- Device Trust -->
                        <div class="device-trust">
                            <label>
                                <input type="checkbox" id="trustDevice">
                                Trust this device for 30 days
                            </label>
                            <div class="device-trust-info">
                                You won't need to verify on this device for 30 days
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-check"></i>
                            Verify Code
                        </button>
                    </form>

                    <!-- Resend Section -->
                    <div class="resend-section" id="resendSection" style="display: none;">
                        <div class="resend-timer" id="resendTimer">
                            You can request a new code in <span id="resendCountdown">60</span> seconds
                        </div>
                        <button class="btn btn-secondary" id="resendButton" onclick="resendCode()" disabled>
                            <i class="fas fa-paper-plane"></i>
                            Resend Code
                        </button>
                    </div>

                    <!-- Help Links -->
                    <div class="help-links">
                        <a href="#" onclick="showTroubleshooting()">
                            <i class="fas fa-question-circle"></i>
                            Troubleshooting
                        </a>
                        <a href="#" onclick="contactSupport()">
                            <i class="fas fa-headset"></i>
                            Contact Support
                        </a>
                        <a href="../setup/index.html">
                            <i class="fas fa-cog"></i>
                            MFA Settings
                        </a>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Valid TOTP Code</div>
                        <div class="scenario-description">Test verification with valid authenticator code</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="fillValidTOTP()">
                                <i class="fas fa-mobile-alt"></i>
                                Fill Valid TOTP
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Code</div>
                        <div class="scenario-description">Test verification with invalid or expired code</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillInvalidCode()">
                                <i class="fas fa-times"></i>
                                Fill Invalid Code
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Backup Recovery</div>
                        <div class="scenario-description">Test verification using backup recovery code</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="fillBackupCode()">
                                <i class="fas fa-key"></i>
                                Fill Backup Code
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Rate Limiting</div>
                        <div class="scenario-description">Test rate limiting after multiple failed attempts</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testRateLimit()">
                                <i class="fas fa-tachometer-alt"></i>
                                Test Rate Limit
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">SMS Verification</div>
                        <div class="scenario-description">Test SMS-based verification flow</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testSMSVerification()">
                                <i class="fas fa-sms"></i>
                                Test SMS Flow
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Code Expiration</div>
                        <div class="scenario-description">Test verification with expired code</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testExpiredCode()">
                                <i class="fas fa-clock"></i>
                                Test Expired Code
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="mfaResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 MFA Verification Testing Interface</p>
                    <p>Multi-factor authentication verification testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentMethod = 'totp';
        let attemptsRemaining = 3;
        let codeExpiry = null;
        let resendCountdown = 0;
        let resendTimer = null;
        let expiryTimer = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Set up form handler
            document.getElementById('verificationForm').addEventListener('submit', handleVerification);

            // Initialize verification state
            selectMethod('totp');
            startCodeExpiry();
        }

        // Method Selection Functions
        function selectMethod(method) {
            // Remove active class from all methods
            document.querySelectorAll('.method-option').forEach(el => el.classList.remove('active'));

            // Add active class to selected method
            document.getElementById(method + 'Method').classList.add('active');
            currentMethod = method;

            // Update UI based on method
            updateMethodUI(method);

            // Clear previous input
            document.getElementById('verificationCode').value = '';

            window.notificationManager.info(`Switched to ${method.toUpperCase()} verification`);
        }

        function updateMethodUI(method) {
            const methodIcon = document.getElementById('methodIcon');
            const methodTitle = document.getElementById('methodTitle');
            const methodDescription = document.getElementById('methodDescription');
            const verificationInput = document.getElementById('verificationCode');
            const resendSection = document.getElementById('resendSection');

            switch (method) {
                case 'totp':
                    methodIcon.className = 'fas fa-mobile-alt';
                    methodTitle.textContent = 'Enter Authenticator Code';
                    methodDescription.textContent = 'Enter the 6-digit code from your authenticator app';
                    verificationInput.placeholder = '000000';
                    verificationInput.maxLength = 6;
                    verificationInput.className = 'verification-input';
                    resendSection.style.display = 'none';
                    break;
                case 'sms':
                    methodIcon.className = 'fas fa-sms';
                    methodTitle.textContent = 'Enter SMS Code';
                    methodDescription.textContent = 'Enter the 6-digit code sent to your phone';
                    verificationInput.placeholder = '000000';
                    verificationInput.maxLength = 6;
                    verificationInput.className = 'verification-input';
                    resendSection.style.display = 'block';
                    startResendCountdown();
                    break;
                case 'backup':
                    methodIcon.className = 'fas fa-key';
                    methodTitle.textContent = 'Enter Backup Code';
                    methodDescription.textContent = 'Enter one of your backup recovery codes';
                    verificationInput.placeholder = 'XXXX-XXXX-XXXX';
                    verificationInput.maxLength = 14;
                    verificationInput.className = 'backup-code-input form-control';
                    resendSection.style.display = 'none';
                    break;
            }
        }

        // Verification Functions
        async function handleVerification(event) {
            event.preventDefault();

            const code = document.getElementById('verificationCode').value;
            const trustDevice = document.getElementById('trustDevice').checked;

            if (!code) {
                window.notificationManager.error('Please enter a verification code');
                return;
            }

            if (currentMethod !== 'backup' && code.length !== 6) {
                window.notificationManager.error('Please enter a 6-digit code');
                return;
            }

            updateVerificationStatus('info', 'Verifying...', 'Checking your verification code');

            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/verify', {
                    method: currentMethod,
                    code: code,
                    trustDevice: trustDevice
                });

                if (response.success) {
                    updateVerificationStatus('success', 'Verification Successful', 'You have been successfully authenticated');

                    // Store authentication data
                    if (response.data.token) {
                        localStorage.setItem('auth_token', response.data.token);
                    }

                    window.notificationManager.success('MFA verification successful');

                    setTimeout(() => {
                        window.location.href = '../../../index.html';
                    }, 2000);
                } else {
                    handleVerificationFailure(response.error || 'Invalid verification code');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate verification
                const validCodes = ['123456', '000000', '111111'];
                const validBackupCodes = ['1A2B-3C4D-5E6F', 'ABCD-EFGH-IJKL'];

                let isValid = false;
                if (currentMethod === 'backup') {
                    isValid = validBackupCodes.includes(code.toUpperCase());
                } else {
                    isValid = validCodes.includes(code);
                }

                if (isValid) {
                    updateVerificationStatus('success', 'Verification Successful (Simulated)', 'You have been successfully authenticated');
                    window.notificationManager.success('MFA verification successful (simulated)');

                    showResponse('mfaResponse', {
                        success: true,
                        data: { method: currentMethod, verified: true, trustDevice: trustDevice },
                        message: 'Mock MFA verification successful (endpoint may not be available)'
                    }, 'warning');

                    setTimeout(() => {
                        window.location.href = '../../../index.html';
                    }, 2000);
                } else {
                    handleVerificationFailure('Invalid verification code');

                    showResponse('mfaResponse', {
                        success: false,
                        error: 'Invalid verification code',
                        details: 'Code verification failed in simulation'
                    }, 'error');
                }
            }
        }

        function handleVerificationFailure(errorMessage) {
            attemptsRemaining--;
            updateAttemptsCounter();

            if (attemptsRemaining <= 0) {
                updateVerificationStatus('error', 'Account Locked', 'Too many failed attempts. Please try again later.');
                document.getElementById('verificationForm').style.display = 'none';
                window.notificationManager.error('Account temporarily locked due to too many failed attempts');
            } else {
                updateVerificationStatus('error', 'Verification Failed', errorMessage);
                window.notificationManager.error(`${errorMessage}. ${attemptsRemaining} attempts remaining.`);
            }

            // Clear input
            document.getElementById('verificationCode').value = '';
        }

        function updateVerificationStatus(type, title, message) {
            const statusContainer = document.getElementById('verificationStatus');
            const iconClass = type === 'success' ? 'fas fa-check-circle' :
                             type === 'error' ? 'fas fa-times-circle' :
                             type === 'warning' ? 'fas fa-exclamation-triangle' :
                             'fas fa-shield-alt';

            statusContainer.innerHTML = `
                <div class="status-icon ${type}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="status-title">${title}</div>
                <div class="status-message">${message}</div>
            `;
        }

        function updateAttemptsCounter() {
            const counter = document.getElementById('attemptsCounter');
            const remaining = document.getElementById('attemptsRemaining');

            remaining.textContent = attemptsRemaining;

            if (attemptsRemaining <= 1) {
                counter.className = 'attempts-counter danger';
            } else if (attemptsRemaining <= 2) {
                counter.className = 'attempts-counter warning';
            } else {
                counter.className = 'attempts-counter';
            }
        }

        // Timer Functions
        function startCodeExpiry() {
            codeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
            document.getElementById('codeTimer').style.display = 'block';

            expiryTimer = setInterval(() => {
                const now = new Date();
                const timeLeft = Math.max(0, codeExpiry - now);

                if (timeLeft <= 0) {
                    clearInterval(expiryTimer);
                    updateVerificationStatus('warning', 'Code Expired', 'Your verification code has expired. Please request a new one.');
                    document.getElementById('codeTimer').style.display = 'none';
                    return;
                }

                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                document.getElementById('timeRemaining').textContent = timeString;

                const timerElement = document.getElementById('codeTimer');
                if (timeLeft < 60000) { // Less than 1 minute
                    timerElement.className = 'timer danger';
                } else if (timeLeft < 120000) { // Less than 2 minutes
                    timerElement.className = 'timer warning';
                } else {
                    timerElement.className = 'timer';
                }
            }, 1000);
        }

        function startResendCountdown() {
            resendCountdown = 60;
            document.getElementById('resendButton').disabled = true;

            resendTimer = setInterval(() => {
                resendCountdown--;
                document.getElementById('resendCountdown').textContent = resendCountdown;

                if (resendCountdown <= 0) {
                    clearInterval(resendTimer);
                    document.getElementById('resendButton').disabled = false;
                    document.getElementById('resendTimer').textContent = 'You can now request a new code';
                }
            }, 1000);
        }

        async function resendCode() {
            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/resend', {
                    method: currentMethod
                });

                if (response.success) {
                    window.notificationManager.success('New verification code sent');
                    startResendCountdown();
                    startCodeExpiry();
                } else {
                    window.notificationManager.error(response.error || 'Failed to resend code');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate resend
                window.notificationManager.success('New verification code sent (simulated)');
                startResendCountdown();
                startCodeExpiry();

                showResponse('mfaResponse', {
                    success: true,
                    data: { method: currentMethod, codeSent: true },
                    message: 'Mock code resend successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Quick Test Functions
        function testValidCode() {
            selectMethod('totp');
            document.getElementById('verificationCode').value = '123456';
            window.notificationManager.info('Valid TOTP code filled. Click Verify Code to test.');
        }

        function testInvalidCode() {
            selectMethod('totp');
            document.getElementById('verificationCode').value = '999999';
            window.notificationManager.info('Invalid code filled. This should trigger an error.');
        }

        function testBackupCode() {
            selectMethod('backup');
            document.getElementById('verificationCode').value = '1A2B-3C4D-5E6F';
            window.notificationManager.info('Valid backup code filled. Click Verify Code to test.');
        }

        function testResendCode() {
            selectMethod('sms');
            resendCode();
        }

        // Test Scenario Functions
        function fillValidTOTP() {
            selectMethod('totp');
            document.getElementById('verificationCode').value = '123456';
        }

        function fillInvalidCode() {
            selectMethod('totp');
            document.getElementById('verificationCode').value = '000001';
        }

        function fillBackupCode() {
            selectMethod('backup');
            document.getElementById('verificationCode').value = 'ABCD-EFGH-IJKL';
        }

        function testRateLimit() {
            window.notificationManager.info('Testing rate limiting with multiple failed attempts...');

            // Simulate multiple failed attempts
            for (let i = 0; i < 4; i++) {
                setTimeout(() => {
                    attemptsRemaining--;
                    updateAttemptsCounter();

                    if (attemptsRemaining <= 0) {
                        updateVerificationStatus('error', 'Account Locked', 'Too many failed attempts. Account temporarily locked.');
                        document.getElementById('verificationForm').style.display = 'none';
                        window.notificationManager.error('Account locked due to too many failed attempts');
                    } else {
                        window.notificationManager.error(`Failed attempt ${4 - i}. ${attemptsRemaining} attempts remaining.`);
                    }
                }, i * 500);
            }
        }

        function testSMSVerification() {
            selectMethod('sms');
            window.notificationManager.info('SMS verification selected. Code would be sent to your phone.');

            setTimeout(() => {
                document.getElementById('verificationCode').value = '123456';
                window.notificationManager.info('SMS code received (simulated). Click Verify Code to test.');
            }, 2000);
        }

        function testExpiredCode() {
            // Simulate expired code by setting expiry to past
            codeExpiry = new Date(Date.now() - 1000);
            clearInterval(expiryTimer);

            updateVerificationStatus('warning', 'Code Expired', 'Your verification code has expired. Please request a new one.');
            document.getElementById('codeTimer').style.display = 'none';
            window.notificationManager.warning('Code expired. Request a new one to continue.');
        }

        // Help Functions
        function showTroubleshooting() {
            window.notificationManager.info('Troubleshooting guide would be displayed here');

            const troubleshootingSteps = [
                '1. Ensure your device time is synchronized',
                '2. Check if your authenticator app is up to date',
                '3. Try generating a new code',
                '4. Use a backup recovery code if available',
                '5. Contact support if issues persist'
            ];

            setTimeout(() => {
                troubleshootingSteps.forEach((step, index) => {
                    setTimeout(() => {
                        window.notificationManager.info(step);
                    }, index * 1000);
                });
            }, 1000);
        }

        function contactSupport() {
            window.notificationManager.info('Support contact information would be displayed here');

            setTimeout(() => {
                window.notificationManager.info('Email: <EMAIL>');
            }, 1000);

            setTimeout(() => {
                window.notificationManager.info('Phone: +****************');
            }, 2000);
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (resendTimer) clearInterval(resendTimer);
            if (expiryTimer) clearInterval(expiryTimer);
        });
    </script>
</body>
</html>
