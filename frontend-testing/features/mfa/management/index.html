<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MFA Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .mfa-container {
            max-width: 800px;
            margin: 2rem auto;
        }

        .mfa-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-icon.enabled {
            color: var(--success-color);
        }

        .status-icon.disabled {
            color: var(--warning-color);
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .mfa-methods {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .method-card.enabled {
            border-color: var(--success-color);
            background: rgba(var(--success-color-rgb), 0.05);
        }

        .method-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .method-icon {
            font-size: 2rem;
            margin-right: 1rem;
            width: 50px;
            text-align: center;
        }

        .method-icon.enabled {
            color: var(--success-color);
        }

        .method-icon.disabled {
            color: var(--text-secondary);
        }

        .method-details {
            flex: 1;
        }

        .method-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .method-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .method-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .method-status.enabled {
            background: var(--success-color);
            color: white;
        }

        .method-status.disabled {
            background: var(--text-secondary);
            color: white;
        }

        .method-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .backup-codes {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .backup-codes h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .codes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .backup-code {
            font-family: monospace;
            font-size: 0.9rem;
            padding: 0.5rem;
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            text-align: center;
        }

        .backup-code.used {
            background: var(--text-secondary);
            color: white;
            text-decoration: line-through;
        }

        .security-settings {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .setting-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .activity-log {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 2rem;
        }

        .activity-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-action {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .activity-details {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .danger-zone {
            background: rgba(var(--error-color-rgb), 0.1);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .danger-zone h3 {
            color: var(--error-color);
            margin-bottom: 1rem;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--text-secondary);
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>MFA Management Testing</h1>
                    <span class="subtitle">Multi-Factor Authentication Management Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to MFA
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="mfa-container">
                <!-- MFA Status -->
                <div class="mfa-status" id="mfaStatus">
                    <div class="status-icon enabled">
                        <i class="fas fa-shield-check"></i>
                    </div>
                    <div class="status-title">Multi-Factor Authentication Enabled</div>
                    <div class="status-description">Your account is protected with multi-factor authentication</div>
                    <button class="btn btn-danger" onclick="disableMFA()">
                        <i class="fas fa-shield-slash"></i>
                        Disable MFA
                    </button>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="loadMFAStatus()">
                        <i class="fas fa-sync"></i>
                        Refresh Status
                    </button>
                    <button class="btn btn-success" onclick="testMethodToggle()">
                        <i class="fas fa-toggle-on"></i>
                        Test Method Toggle
                    </button>
                    <button class="btn btn-warning" onclick="testBackupCodes()">
                        <i class="fas fa-key"></i>
                        Test Backup Codes
                    </button>
                    <button class="btn btn-info" onclick="testActivityLog()">
                        <i class="fas fa-history"></i>
                        Test Activity Log
                    </button>
                </div>

                <!-- MFA Methods -->
                <div class="mfa-methods">
                    <div class="method-card enabled" id="totpCard">
                        <div class="method-info">
                            <div class="method-icon enabled">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="method-details">
                                <div class="method-title">Authenticator App (TOTP)</div>
                                <div class="method-description">Time-based one-time passwords</div>
                                <span class="method-status enabled">Enabled</span>
                            </div>
                        </div>
                        <div class="method-actions">
                            <button class="btn btn-sm btn-secondary" onclick="reconfigureTOTP()">
                                <i class="fas fa-cog"></i>
                                Reconfigure
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="disableMethod('totp')">
                                <i class="fas fa-times"></i>
                                Disable
                            </button>
                        </div>
                    </div>

                    <div class="method-card" id="smsCard">
                        <div class="method-info">
                            <div class="method-icon disabled">
                                <i class="fas fa-sms"></i>
                            </div>
                            <div class="method-details">
                                <div class="method-title">SMS Text Message</div>
                                <div class="method-description">Receive codes via text message</div>
                                <span class="method-status disabled">Disabled</span>
                            </div>
                        </div>
                        <div class="method-actions">
                            <button class="btn btn-sm btn-primary" onclick="enableMethod('sms')">
                                <i class="fas fa-plus"></i>
                                Enable
                            </button>
                        </div>
                    </div>

                    <div class="method-card" id="emailCard">
                        <div class="method-info">
                            <div class="method-icon disabled">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-details">
                                <div class="method-title">Email Verification</div>
                                <div class="method-description">Receive codes via email</div>
                                <span class="method-status disabled">Disabled</span>
                            </div>
                        </div>
                        <div class="method-actions">
                            <button class="btn btn-sm btn-primary" onclick="enableMethod('email')">
                                <i class="fas fa-plus"></i>
                                Enable
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Backup Codes -->
                <div class="backup-codes">
                    <h3>
                        Backup Recovery Codes
                        <button class="btn btn-sm btn-secondary" onclick="generateNewBackupCodes()">
                            <i class="fas fa-sync"></i>
                            Generate New
                        </button>
                    </h3>
                    <p>Use these codes to access your account if you lose your MFA device. Each code can only be used once.</p>

                    <div class="codes-grid" id="backupCodesGrid">
                        <div class="backup-code">1A2B-3C4D-5E6F</div>
                        <div class="backup-code">7G8H-9I0J-1K2L</div>
                        <div class="backup-code used">3M4N-5O6P-7Q8R</div>
                        <div class="backup-code">9S0T-1U2V-3W4X</div>
                        <div class="backup-code">5Y6Z-7A8B-9C0D</div>
                        <div class="backup-code">1E2F-3G4H-5I6J</div>
                        <div class="backup-code">7K8L-9M0N-1O2P</div>
                        <div class="backup-code">3Q4R-5S6T-7U8V</div>
                    </div>

                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <button class="btn btn-sm btn-secondary" onclick="downloadBackupCodes()">
                            <i class="fas fa-download"></i>
                            Download
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="printBackupCodes()">
                            <i class="fas fa-print"></i>
                            Print
                        </button>
                        <button class="btn btn-sm btn-info" onclick="viewUsedCodes()">
                            <i class="fas fa-eye"></i>
                            View Usage
                        </button>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="security-settings">
                    <h3>Security Settings</h3>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Require MFA for Login</div>
                            <div class="setting-description">Always require MFA when logging in</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="requireMFALogin" checked onchange="toggleSetting('requireMFALogin')">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Require MFA for Sensitive Actions</div>
                            <div class="setting-description">Require MFA for password changes, account settings</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="requireMFASensitive" checked onchange="toggleSetting('requireMFASensitive')">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Remember Trusted Devices</div>
                            <div class="setting-description">Skip MFA on trusted devices for 30 days</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="rememberDevices" onchange="toggleSetting('rememberDevices')">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">MFA Notifications</div>
                            <div class="setting-description">Get notified when MFA is used or changed</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="mfaNotifications" checked onchange="toggleSetting('mfaNotifications')">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Enable/Disable Methods</div>
                        <div class="scenario-description">Test enabling and disabling MFA methods</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="simulateMethodToggle()">
                                <i class="fas fa-toggle-on"></i>
                                Simulate Toggle
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Backup Code Management</div>
                        <div class="scenario-description">Test backup code generation and usage</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="simulateBackupCodeUsage()">
                                <i class="fas fa-key"></i>
                                Simulate Usage
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Security Settings</div>
                        <div class="scenario-description">Test security setting modifications</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testSecuritySettings()">
                                <i class="fas fa-cog"></i>
                                Test Settings
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Complete MFA Disable</div>
                        <div class="scenario-description">Test completely disabling MFA</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="testMFADisable()">
                                <i class="fas fa-shield-slash"></i>
                                Test Disable
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="activity-log" id="activityLog">
                    <div class="activity-header">Recent MFA Activity</div>
                    <!-- Activity items will be populated here -->
                </div>

                <!-- Danger Zone -->
                <div class="danger-zone">
                    <h3>Danger Zone</h3>
                    <p>These actions will affect your account security. Proceed with caution.</p>
                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <button class="btn btn-danger" onclick="disableAllMFA()">
                            <i class="fas fa-shield-slash"></i>
                            Disable All MFA
                        </button>
                        <button class="btn btn-danger" onclick="resetMFASettings()">
                            <i class="fas fa-undo"></i>
                            Reset MFA Settings
                        </button>
                        <button class="btn btn-danger" onclick="revokeAllTrustedDevices()">
                            <i class="fas fa-ban"></i>
                            Revoke Trusted Devices
                        </button>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="mfaResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 MFA Management Testing Interface</p>
                    <p>Multi-factor authentication management and testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let mfaSettings = {
            enabled: true,
            methods: {
                totp: { enabled: true, configured: true },
                sms: { enabled: false, configured: false },
                email: { enabled: false, configured: false }
            },
            backupCodes: [],
            settings: {
                requireMFALogin: true,
                requireMFASensitive: true,
                rememberDevices: false,
                mfaNotifications: true
            }
        };
        let activityData = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load MFA status and settings
            await loadMFAStatus();

            // Load activity log
            loadActivityLog();

            // Generate backup codes
            generateBackupCodes();
        }

        // MFA Status Functions
        async function loadMFAStatus() {
            try {
                const response = await window.apiClient.request('GET', '/auth/mfa/status');

                if (response.success) {
                    mfaSettings = response.data;
                    updateMFADisplay();
                    showResponse('mfaResponse', response, 'success');
                } else {
                    showResponse('mfaResponse', response, 'error');
                }
            } catch (error) {
                // Use mock data
                updateMFADisplay();
                showResponse('mfaResponse', {
                    success: true,
                    data: mfaSettings,
                    message: 'Mock MFA status loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function updateMFADisplay() {
            // Update main status
            const statusElement = document.getElementById('mfaStatus');
            if (mfaSettings.enabled) {
                statusElement.innerHTML = `
                    <div class="status-icon enabled">
                        <i class="fas fa-shield-check"></i>
                    </div>
                    <div class="status-title">Multi-Factor Authentication Enabled</div>
                    <div class="status-description">Your account is protected with multi-factor authentication</div>
                    <button class="btn btn-danger" onclick="disableMFA()">
                        <i class="fas fa-shield-slash"></i>
                        Disable MFA
                    </button>
                `;
            } else {
                statusElement.innerHTML = `
                    <div class="status-icon disabled">
                        <i class="fas fa-shield-slash"></i>
                    </div>
                    <div class="status-title">Multi-Factor Authentication Disabled</div>
                    <div class="status-description">Your account is not protected with multi-factor authentication</div>
                    <button class="btn btn-success" onclick="enableMFA()">
                        <i class="fas fa-shield-check"></i>
                        Enable MFA
                    </button>
                `;
            }

            // Update method cards
            updateMethodCard('totp', mfaSettings.methods.totp);
            updateMethodCard('sms', mfaSettings.methods.sms);
            updateMethodCard('email', mfaSettings.methods.email);

            // Update security settings
            document.getElementById('requireMFALogin').checked = mfaSettings.settings.requireMFALogin;
            document.getElementById('requireMFASensitive').checked = mfaSettings.settings.requireMFASensitive;
            document.getElementById('rememberDevices').checked = mfaSettings.settings.rememberDevices;
            document.getElementById('mfaNotifications').checked = mfaSettings.settings.mfaNotifications;
        }

        function updateMethodCard(method, config) {
            const card = document.getElementById(method + 'Card');
            const icon = card.querySelector('.method-icon');
            const status = card.querySelector('.method-status');
            const actions = card.querySelector('.method-actions');

            if (config.enabled) {
                card.classList.add('enabled');
                icon.classList.add('enabled');
                icon.classList.remove('disabled');
                status.textContent = 'Enabled';
                status.className = 'method-status enabled';

                actions.innerHTML = `
                    <button class="btn btn-sm btn-secondary" onclick="reconfigure${method.toUpperCase()}()">
                        <i class="fas fa-cog"></i>
                        Reconfigure
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="disableMethod('${method}')">
                        <i class="fas fa-times"></i>
                        Disable
                    </button>
                `;
            } else {
                card.classList.remove('enabled');
                icon.classList.remove('enabled');
                icon.classList.add('disabled');
                status.textContent = 'Disabled';
                status.className = 'method-status disabled';

                actions.innerHTML = `
                    <button class="btn btn-sm btn-primary" onclick="enableMethod('${method}')">
                        <i class="fas fa-plus"></i>
                        Enable
                    </button>
                `;
            }
        }

        // MFA Method Management Functions
        async function enableMethod(method) {
            try {
                const response = await window.apiClient.request('POST', `/auth/mfa/enable-${method}`);

                if (response.success) {
                    mfaSettings.methods[method].enabled = true;
                    updateMethodCard(method, mfaSettings.methods[method]);
                    addActivityItem(`${method.toUpperCase()} Enabled`, `${method.toUpperCase()} authentication method enabled`);
                    window.notificationManager.success(`${method.toUpperCase()} method enabled successfully`);
                } else {
                    window.notificationManager.error(response.error || `Failed to enable ${method.toUpperCase()}`);
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate enabling
                mfaSettings.methods[method].enabled = true;
                updateMethodCard(method, mfaSettings.methods[method]);
                addActivityItem(`${method.toUpperCase()} Enabled`, `${method.toUpperCase()} authentication method enabled`);
                window.notificationManager.success(`${method.toUpperCase()} method enabled successfully (simulated)`);

                showResponse('mfaResponse', {
                    success: true,
                    data: { method: method, enabled: true },
                    message: `Mock ${method.toUpperCase()} enable successful (endpoint may not be available)`
                }, 'warning');
            }
        }

        async function disableMethod(method) {
            if (!confirm(`Are you sure you want to disable ${method.toUpperCase()} authentication?`)) {
                return;
            }

            try {
                const response = await window.apiClient.request('POST', `/auth/mfa/disable-${method}`);

                if (response.success) {
                    mfaSettings.methods[method].enabled = false;
                    updateMethodCard(method, mfaSettings.methods[method]);
                    addActivityItem(`${method.toUpperCase()} Disabled`, `${method.toUpperCase()} authentication method disabled`);
                    window.notificationManager.warning(`${method.toUpperCase()} method disabled`);
                } else {
                    window.notificationManager.error(response.error || `Failed to disable ${method.toUpperCase()}`);
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate disabling
                mfaSettings.methods[method].enabled = false;
                updateMethodCard(method, mfaSettings.methods[method]);
                addActivityItem(`${method.toUpperCase()} Disabled`, `${method.toUpperCase()} authentication method disabled`);
                window.notificationManager.warning(`${method.toUpperCase()} method disabled (simulated)`);

                showResponse('mfaResponse', {
                    success: true,
                    data: { method: method, enabled: false },
                    message: `Mock ${method.toUpperCase()} disable successful (endpoint may not be available)`
                }, 'warning');
            }
        }

        function reconfigureTOTP() {
            window.notificationManager.info('Redirecting to TOTP reconfiguration...');
            setTimeout(() => {
                window.location.href = '../setup/index.html';
            }, 1000);
        }

        function reconfigureSMS() {
            window.notificationManager.info('SMS reconfiguration would be handled here');
            addActivityItem('SMS Reconfigured', 'SMS phone number updated');
        }

        function reconfigureEMAIL() {
            window.notificationManager.info('Email reconfiguration would be handled here');
            addActivityItem('Email Reconfigured', 'Email MFA settings updated');
        }

        // MFA Enable/Disable Functions
        async function disableMFA() {
            if (!confirm('Are you sure you want to disable multi-factor authentication? This will make your account less secure.')) {
                return;
            }

            if (!confirm('This will disable ALL MFA methods. Are you absolutely sure?')) {
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/disable');

                if (response.success) {
                    mfaSettings.enabled = false;
                    Object.keys(mfaSettings.methods).forEach(method => {
                        mfaSettings.methods[method].enabled = false;
                    });
                    updateMFADisplay();
                    addActivityItem('MFA Disabled', 'Multi-factor authentication completely disabled');
                    window.notificationManager.warning('MFA has been disabled');
                } else {
                    window.notificationManager.error(response.error || 'Failed to disable MFA');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate disabling
                mfaSettings.enabled = false;
                Object.keys(mfaSettings.methods).forEach(method => {
                    mfaSettings.methods[method].enabled = false;
                });
                updateMFADisplay();
                addActivityItem('MFA Disabled', 'Multi-factor authentication completely disabled');
                window.notificationManager.warning('MFA has been disabled (simulated)');

                showResponse('mfaResponse', {
                    success: true,
                    data: { mfaEnabled: false },
                    message: 'Mock MFA disable successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function enableMFA() {
            window.notificationManager.info('Redirecting to MFA setup...');
            setTimeout(() => {
                window.location.href = '../setup/index.html';
            }, 1000);
        }

        // Backup Code Functions
        function generateBackupCodes() {
            mfaSettings.backupCodes = [];
            for (let i = 0; i < 8; i++) {
                const code = Math.random().toString(36).substr(2, 4).toUpperCase() + '-' +
                           Math.random().toString(36).substr(2, 4).toUpperCase() + '-' +
                           Math.random().toString(36).substr(2, 4).toUpperCase();
                mfaSettings.backupCodes.push({ code: code, used: i === 2 }); // Mark one as used for demo
            }

            displayBackupCodes();
        }

        function displayBackupCodes() {
            const codesGrid = document.getElementById('backupCodesGrid');
            codesGrid.innerHTML = mfaSettings.backupCodes.map(item =>
                `<div class="backup-code ${item.used ? 'used' : ''}">${item.code}</div>`
            ).join('');
        }

        async function generateNewBackupCodes() {
            if (!confirm('Generating new backup codes will invalidate all existing codes. Continue?')) {
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/generate-backup-codes');

                if (response.success) {
                    mfaSettings.backupCodes = response.data.codes || [];
                    displayBackupCodes();
                    addActivityItem('Backup Codes Generated', 'New backup recovery codes generated');
                    window.notificationManager.success('New backup codes generated');
                } else {
                    window.notificationManager.error(response.error || 'Failed to generate backup codes');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate generation
                generateBackupCodes();
                addActivityItem('Backup Codes Generated', 'New backup recovery codes generated');
                window.notificationManager.success('New backup codes generated (simulated)');

                showResponse('mfaResponse', {
                    success: true,
                    data: { codes: mfaSettings.backupCodes },
                    message: 'Mock backup code generation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function downloadBackupCodes() {
            const content = 'MFA Backup Recovery Codes\n\n' +
                          mfaSettings.backupCodes.map(item => item.code).join('\n') +
                          '\n\nKeep these codes safe and secure. Each code can only be used once.';

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mfa-backup-codes.txt';
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Backup codes downloaded');
            addActivityItem('Backup Codes Downloaded', 'Backup codes downloaded to file');
        }

        function printBackupCodes() {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head><title>MFA Backup Codes</title></head>
                <body>
                    <h1>MFA Backup Recovery Codes</h1>
                    <p>Keep these codes safe and secure. Each code can only be used once.</p>
                    <ul>
                        ${mfaSettings.backupCodes.map(item =>
                            `<li style="font-family: monospace; font-size: 14px; margin: 10px 0; ${item.used ? 'text-decoration: line-through;' : ''}">${item.code}${item.used ? ' (USED)' : ''}</li>`
                        ).join('')}
                    </ul>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();

            window.notificationManager.success('Backup codes sent to printer');
            addActivityItem('Backup Codes Printed', 'Backup codes sent to printer');
        }

        function viewUsedCodes() {
            const usedCodes = mfaSettings.backupCodes.filter(item => item.used);
            if (usedCodes.length === 0) {
                window.notificationManager.info('No backup codes have been used yet');
            } else {
                window.notificationManager.info(`${usedCodes.length} backup codes have been used`);
                usedCodes.forEach((item, index) => {
                    setTimeout(() => {
                        window.notificationManager.warning(`Used: ${item.code}`);
                    }, (index + 1) * 500);
                });
            }
        }

        // Security Settings Functions
        async function toggleSetting(settingName) {
            const checkbox = document.getElementById(settingName);
            const newValue = checkbox.checked;

            try {
                const response = await window.apiClient.request('PUT', '/auth/mfa/settings', {
                    [settingName]: newValue
                });

                if (response.success) {
                    mfaSettings.settings[settingName] = newValue;
                    addActivityItem('Security Setting Changed', `${settingName} ${newValue ? 'enabled' : 'disabled'}`);
                    window.notificationManager.success(`Setting updated: ${settingName}`);
                } else {
                    // Revert checkbox if failed
                    checkbox.checked = !newValue;
                    window.notificationManager.error(response.error || 'Failed to update setting');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate setting change
                mfaSettings.settings[settingName] = newValue;
                addActivityItem('Security Setting Changed', `${settingName} ${newValue ? 'enabled' : 'disabled'}`);
                window.notificationManager.success(`Setting updated: ${settingName} (simulated)`);

                showResponse('mfaResponse', {
                    success: true,
                    data: { setting: settingName, value: newValue },
                    message: 'Mock setting update successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Activity Log Functions
        function loadActivityLog() {
            // Generate mock activity data
            activityData = [
                {
                    action: 'TOTP Verified',
                    details: 'Successfully verified using authenticator app',
                    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    icon: 'fas fa-mobile-alt'
                },
                {
                    action: 'Backup Code Used',
                    details: 'Backup recovery code used for login',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    icon: 'fas fa-key'
                },
                {
                    action: 'SMS Method Enabled',
                    details: 'SMS authentication method enabled',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    icon: 'fas fa-sms'
                },
                {
                    action: 'Security Settings Updated',
                    details: 'MFA notification settings changed',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
                    icon: 'fas fa-cog'
                },
                {
                    action: 'Backup Codes Generated',
                    details: 'New backup recovery codes generated',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
                    icon: 'fas fa-sync'
                }
            ];

            displayActivityLog();
        }

        function displayActivityLog() {
            const activityLog = document.getElementById('activityLog');

            const activityHTML = activityData.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-action">${activity.action}</div>
                        <div class="activity-details">${activity.details}</div>
                    </div>
                    <div class="activity-time">${formatTimeAgo(activity.timestamp)}</div>
                </div>
            `).join('');

            activityLog.innerHTML = `
                <div class="activity-header">Recent MFA Activity</div>
                ${activityHTML}
            `;
        }

        function addActivityItem(action, details) {
            activityData.unshift({
                action: action,
                details: details,
                timestamp: new Date(),
                icon: 'fas fa-edit'
            });

            // Keep only last 10 items
            if (activityData.length > 10) {
                activityData = activityData.slice(0, 10);
            }

            displayActivityLog();
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        // Quick Test Functions
        function testMethodToggle() {
            window.notificationManager.info('Testing method toggle...');

            setTimeout(() => {
                if (mfaSettings.methods.sms.enabled) {
                    disableMethod('sms');
                } else {
                    enableMethod('sms');
                }
            }, 1000);
        }

        function testBackupCodes() {
            generateNewBackupCodes();
            window.notificationManager.info('Backup codes regenerated for testing');
        }

        function testActivityLog() {
            addActivityItem('Test Activity', 'This is a test activity log entry');
            window.notificationManager.info('Test activity added to log');
        }

        // Test Scenario Functions
        function simulateMethodToggle() {
            window.notificationManager.info('Simulating method toggle sequence...');

            setTimeout(() => enableMethod('sms'), 1000);
            setTimeout(() => enableMethod('email'), 2000);
            setTimeout(() => disableMethod('sms'), 4000);
            setTimeout(() => {
                window.notificationManager.success('Method toggle simulation complete');
            }, 5000);
        }

        function simulateBackupCodeUsage() {
            window.notificationManager.info('Simulating backup code usage...');

            // Mark a random unused code as used
            const unusedCodes = mfaSettings.backupCodes.filter(item => !item.used);
            if (unusedCodes.length > 0) {
                const randomIndex = Math.floor(Math.random() * unusedCodes.length);
                const codeToUse = unusedCodes[randomIndex];
                codeToUse.used = true;

                displayBackupCodes();
                addActivityItem('Backup Code Used', `Backup code ${codeToUse.code} used for authentication`);
                window.notificationManager.warning(`Backup code used: ${codeToUse.code}`);
            } else {
                window.notificationManager.info('No unused backup codes available');
            }
        }

        function testSecuritySettings() {
            window.notificationManager.info('Testing security settings...');

            const settings = ['requireMFALogin', 'requireMFASensitive', 'rememberDevices', 'mfaNotifications'];

            settings.forEach((setting, index) => {
                setTimeout(() => {
                    const checkbox = document.getElementById(setting);
                    checkbox.checked = !checkbox.checked;
                    toggleSetting(setting);
                }, (index + 1) * 1000);
            });
        }

        function testMFADisable() {
            window.notificationManager.warning('Testing MFA disable (simulation only)...');

            setTimeout(() => {
                window.notificationManager.warning('In real implementation, this would require additional verification');
            }, 1000);

            setTimeout(() => {
                window.notificationManager.info('MFA disable test completed (no actual changes made)');
            }, 2000);
        }

        // Danger Zone Functions
        function disableAllMFA() {
            if (!confirm('This will disable ALL MFA methods and make your account vulnerable. Continue?')) {
                return;
            }

            window.notificationManager.warning('Disabling all MFA methods...');

            Object.keys(mfaSettings.methods).forEach((method, index) => {
                setTimeout(() => {
                    mfaSettings.methods[method].enabled = false;
                    updateMethodCard(method, mfaSettings.methods[method]);
                }, index * 500);
            });

            setTimeout(() => {
                mfaSettings.enabled = false;
                updateMFADisplay();
                addActivityItem('All MFA Disabled', 'All MFA methods disabled');
                window.notificationManager.error('All MFA methods have been disabled');
            }, 2000);
        }

        function resetMFASettings() {
            if (!confirm('This will reset all MFA settings to defaults. Continue?')) {
                return;
            }

            window.notificationManager.info('Resetting MFA settings...');

            // Reset to defaults
            mfaSettings.settings = {
                requireMFALogin: true,
                requireMFASensitive: true,
                rememberDevices: false,
                mfaNotifications: true
            };

            updateMFADisplay();
            addActivityItem('MFA Settings Reset', 'All MFA settings reset to defaults');
            window.notificationManager.success('MFA settings reset to defaults');
        }

        function revokeAllTrustedDevices() {
            if (!confirm('This will revoke trust from all devices. Users will need to verify MFA on all devices. Continue?')) {
                return;
            }

            window.notificationManager.warning('Revoking all trusted devices...');

            setTimeout(() => {
                addActivityItem('Trusted Devices Revoked', 'All trusted devices revoked');
                window.notificationManager.success('All trusted devices have been revoked');
            }, 2000);
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
