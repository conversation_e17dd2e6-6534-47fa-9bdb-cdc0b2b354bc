<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MFA Setup Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .mfa-container {
            max-width: 600px;
            margin: 2rem auto;
        }

        .mfa-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .step-number.active {
            background: var(--primary-color);
            color: white;
        }

        .step-number.completed {
            background: var(--success-color);
            color: white;
        }

        .step-title {
            font-weight: bold;
            color: var(--text-primary);
        }

        .step-connector {
            width: 50px;
            height: 2px;
            background: var(--border-color);
            margin: 0 1rem;
        }

        .step-connector.completed {
            background: var(--success-color);
        }

        .mfa-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .mfa-method {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mfa-method:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .mfa-method.selected {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }

        .method-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .method-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .method-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .qr-code {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .qr-placeholder {
            color: var(--text-secondary);
            text-align: center;
        }

        .secret-key {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 1rem;
            font-family: monospace;
            font-size: 1.1rem;
            letter-spacing: 2px;
            margin: 1rem 0;
            word-break: break-all;
            text-align: center;
        }

        .verification-input {
            font-family: monospace;
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
            max-width: 200px;
            margin: 0 auto;
        }

        .backup-codes {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .backup-code {
            font-family: monospace;
            font-size: 1.1rem;
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: var(--card-background);
            border-radius: 4px;
            text-align: center;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .warning-box {
            background: rgba(var(--warning-color-rgb), 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .warning-box .warning-icon {
            color: var(--warning-color);
            margin-right: 0.5rem;
        }

        .success-box {
            background: rgba(var(--success-color-rgb), 0.1);
            border: 1px solid var(--success-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .success-box .success-icon {
            color: var(--success-color);
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .app-instructions {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .app-instructions h4 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .app-instructions ol {
            margin-left: 1rem;
        }

        .app-instructions li {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .copy-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 1rem;
        }

        .copy-button:hover {
            background: var(--primary-color-dark);
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>MFA Setup Testing</h1>
                    <span class="subtitle">Multi-Factor Authentication Setup Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to MFA
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="mfa-container">
                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="startMFASetup()">
                        <i class="fas fa-play"></i>
                        Start MFA Setup
                    </button>
                    <button class="btn btn-success" onclick="testTOTPSetup()">
                        <i class="fas fa-mobile-alt"></i>
                        Test TOTP Setup
                    </button>
                    <button class="btn btn-info" onclick="testSMSSetup()">
                        <i class="fas fa-sms"></i>
                        Test SMS Setup
                    </button>
                    <button class="btn btn-warning" onclick="testBackupCodes()">
                        <i class="fas fa-key"></i>
                        Test Backup Codes
                    </button>
                </div>

                <!-- Setup Steps -->
                <div class="mfa-steps">
                    <div class="step">
                        <div class="step-number active" id="stepNumber1">1</div>
                        <div class="step-title">Choose Method</div>
                    </div>
                    <div class="step-connector" id="connector1"></div>
                    <div class="step">
                        <div class="step-number" id="stepNumber2">2</div>
                        <div class="step-title">Configure</div>
                    </div>
                    <div class="step-connector" id="connector2"></div>
                    <div class="step">
                        <div class="step-number" id="stepNumber3">3</div>
                        <div class="step-title">Verify</div>
                    </div>
                    <div class="step-connector" id="connector3"></div>
                    <div class="step">
                        <div class="step-number" id="stepNumber4">4</div>
                        <div class="step-title">Complete</div>
                    </div>
                </div>

                <!-- Step 1: Choose Method -->
                <div class="step-content active" id="step1">
                    <div class="mfa-card">
                        <h2>Choose Your MFA Method</h2>
                        <p>Select how you'd like to receive your second factor authentication codes</p>
                    </div>

                    <div class="mfa-method" onclick="selectMethod('totp')" id="totpMethod">
                        <div class="method-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="method-title">Authenticator App (TOTP)</div>
                        <div class="method-description">
                            Use apps like Google Authenticator, Authy, or 1Password to generate time-based codes
                        </div>
                    </div>

                    <div class="mfa-method" onclick="selectMethod('sms')" id="smsMethod">
                        <div class="method-icon">
                            <i class="fas fa-sms"></i>
                        </div>
                        <div class="method-title">SMS Text Message</div>
                        <div class="method-description">
                            Receive verification codes via text message to your phone
                        </div>
                    </div>

                    <div class="mfa-method" onclick="selectMethod('email')" id="emailMethod">
                        <div class="method-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="method-title">Email Verification</div>
                        <div class="method-description">
                            Receive verification codes via email (less secure but convenient)
                        </div>
                    </div>

                    <div class="navigation-buttons">
                        <div></div>
                        <button class="btn btn-primary" onclick="nextStep()" id="nextButton" disabled>
                            Next Step
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Step 2: Configure -->
                <div class="step-content" id="step2">
                    <!-- TOTP Configuration -->
                    <div id="totpConfig" style="display: none;">
                        <div class="mfa-card">
                            <h2>Set Up Authenticator App</h2>
                            <p>Scan the QR code with your authenticator app or enter the secret key manually</p>
                        </div>

                        <div class="qr-code" id="qrCode">
                            <div class="qr-placeholder">
                                <i class="fas fa-qrcode" style="font-size: 4rem; color: var(--text-secondary);"></i>
                                <p>QR Code would appear here</p>
                                <small>In real implementation, this would be a scannable QR code</small>
                            </div>
                        </div>

                        <div class="app-instructions">
                            <h4>Manual Setup Instructions:</h4>
                            <ol>
                                <li>Open your authenticator app (Google Authenticator, Authy, etc.)</li>
                                <li>Tap "Add Account" or "+"</li>
                                <li>Choose "Scan QR Code" or "Enter Key Manually"</li>
                                <li>If entering manually, use the secret key below:</li>
                            </ol>
                        </div>

                        <div class="secret-key" id="secretKey">
                            JBSWY3DPEHPK3PXP
                            <button class="copy-button" onclick="copySecretKey()">
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                        </div>
                    </div>

                    <!-- SMS Configuration -->
                    <div id="smsConfig" style="display: none;">
                        <div class="mfa-card">
                            <h2>Set Up SMS Verification</h2>
                            <p>Enter your phone number to receive verification codes via text message</p>
                        </div>

                        <form id="smsForm">
                            <div class="form-group">
                                <label for="phoneNumber">Phone Number:</label>
                                <input type="tel" id="phoneNumber" class="form-control" placeholder="+****************" required>
                                <small class="form-text">Include country code (e.g., +1 for US)</small>
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="sendTestSMS()">
                                <i class="fas fa-paper-plane"></i>
                                Send Test SMS
                            </button>
                        </form>
                    </div>

                    <!-- Email Configuration -->
                    <div id="emailConfig" style="display: none;">
                        <div class="mfa-card">
                            <h2>Set Up Email Verification</h2>
                            <p>Verification codes will be sent to your registered email address</p>
                        </div>

                        <div class="warning-box">
                            <i class="fas fa-exclamation-triangle warning-icon"></i>
                            <strong>Security Notice:</strong> Email-based MFA is less secure than app-based TOTP.
                            Consider using an authenticator app for better security.
                        </div>

                        <div style="text-align: center; padding: 2rem;">
                            <p>Email MFA will use your account email: <strong id="userEmail"><EMAIL></strong></p>
                            <button type="button" class="btn btn-secondary" onclick="sendTestEmail()">
                                <i class="fas fa-envelope"></i>
                                Send Test Email
                            </button>
                        </div>
                    </div>

                    <div class="navigation-buttons">
                        <button class="btn btn-secondary" onclick="previousStep()">
                            <i class="fas fa-arrow-left"></i>
                            Previous
                        </button>
                        <button class="btn btn-primary" onclick="nextStep()">
                            Next Step
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Verify -->
                <div class="step-content" id="step3">
                    <div class="mfa-card">
                        <h2>Verify Your Setup</h2>
                        <p>Enter the verification code from your chosen MFA method to confirm it's working</p>
                    </div>

                    <form id="verificationForm">
                        <div class="form-group">
                            <label for="verificationCode">Verification Code:</label>
                            <input type="text" id="verificationCode" class="form-control verification-input"
                                   placeholder="000000" maxlength="6" required>
                            <small class="form-text" id="verificationHelp">
                                Enter the 6-digit code from your authenticator app
                            </small>
                        </div>

                        <div style="text-align: center; margin: 2rem 0;">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Verify Code
                            </button>
                        </div>
                    </form>

                    <div class="navigation-buttons">
                        <button class="btn btn-secondary" onclick="previousStep()">
                            <i class="fas fa-arrow-left"></i>
                            Previous
                        </button>
                        <div></div>
                    </div>
                </div>

                <!-- Step 4: Complete -->
                <div class="step-content" id="step4">
                    <div class="success-box">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2>MFA Setup Complete!</h2>
                        <p>Your multi-factor authentication has been successfully configured.</p>
                    </div>

                    <div class="backup-codes">
                        <h4>Backup Recovery Codes</h4>
                        <p>Save these backup codes in a secure location. You can use them to access your account if you lose your MFA device.</p>

                        <div id="backupCodesList">
                            <div class="backup-code">1A2B-3C4D-5E6F</div>
                            <div class="backup-code">7G8H-9I0J-1K2L</div>
                            <div class="backup-code">3M4N-5O6P-7Q8R</div>
                            <div class="backup-code">9S0T-1U2V-3W4X</div>
                            <div class="backup-code">5Y6Z-7A8B-9C0D</div>
                            <div class="backup-code">1E2F-3G4H-5I6J</div>
                            <div class="backup-code">7K8L-9M0N-1O2P</div>
                            <div class="backup-code">3Q4R-5S6T-7U8V</div>
                        </div>

                        <div style="text-align: center; margin-top: 1rem;">
                            <button class="btn btn-secondary" onclick="downloadBackupCodes()">
                                <i class="fas fa-download"></i>
                                Download Codes
                            </button>
                            <button class="btn btn-secondary" onclick="printBackupCodes()">
                                <i class="fas fa-print"></i>
                                Print Codes
                            </button>
                        </div>
                    </div>

                    <div class="warning-box">
                        <i class="fas fa-exclamation-triangle warning-icon"></i>
                        <strong>Important:</strong> Each backup code can only be used once.
                        Store them securely and generate new ones if you run out.
                    </div>

                    <div style="text-align: center; margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="completeMFASetup()">
                            <i class="fas fa-check"></i>
                            Complete Setup
                        </button>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">TOTP Setup Flow</div>
                        <div class="scenario-description">Test complete authenticator app setup process</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="simulateTOTPFlow()">
                                <i class="fas fa-mobile-alt"></i>
                                Simulate TOTP
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">SMS Setup Flow</div>
                        <div class="scenario-description">Test SMS-based MFA setup process</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="simulateSMSFlow()">
                                <i class="fas fa-sms"></i>
                                Simulate SMS
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Verification</div>
                        <div class="scenario-description">Test verification with invalid codes</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testInvalidCode()">
                                <i class="fas fa-times"></i>
                                Test Invalid Code
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Backup Code Generation</div>
                        <div class="scenario-description">Test backup recovery code generation</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-secondary" onclick="generateNewBackupCodes()">
                                <i class="fas fa-key"></i>
                                Generate Codes
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="mfaResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 MFA Setup Testing Interface</p>
                    <p>Multi-factor authentication setup and testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentStep = 1;
        let selectedMethod = '';
        let secretKey = '';
        let backupCodes = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Set up form handlers
            document.getElementById('verificationForm').addEventListener('submit', handleVerification);

            // Generate initial secret key and backup codes
            generateSecretKey();
            generateBackupCodes();
        }

        // Step Navigation Functions
        function nextStep() {
            if (currentStep < 4) {
                // Hide current step
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Update step indicators
                document.getElementById(`stepNumber${currentStep}`).classList.remove('active');
                document.getElementById(`stepNumber${currentStep}`).classList.add('completed');
                if (currentStep < 4) {
                    document.getElementById(`connector${currentStep}`).classList.add('completed');
                }

                // Show next step
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');
                document.getElementById(`stepNumber${currentStep}`).classList.add('active');

                // Configure step content based on selected method
                if (currentStep === 2) {
                    configureStep2();
                } else if (currentStep === 3) {
                    configureStep3();
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`stepNumber${currentStep}`).classList.remove('active');

                // Show previous step
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.add('active');
                document.getElementById(`stepNumber${currentStep}`).classList.remove('completed');
                document.getElementById(`stepNumber${currentStep}`).classList.add('active');

                if (currentStep < 4) {
                    document.getElementById(`connector${currentStep}`).classList.remove('completed');
                }
            }
        }

        // Method Selection Functions
        function selectMethod(method) {
            // Remove previous selection
            document.querySelectorAll('.mfa-method').forEach(el => el.classList.remove('selected'));

            // Select new method
            document.getElementById(method + 'Method').classList.add('selected');
            selectedMethod = method;

            // Enable next button
            document.getElementById('nextButton').disabled = false;

            window.notificationManager.info(`Selected ${method.toUpperCase()} as MFA method`);
        }

        function configureStep2() {
            // Hide all config sections
            document.getElementById('totpConfig').style.display = 'none';
            document.getElementById('smsConfig').style.display = 'none';
            document.getElementById('emailConfig').style.display = 'none';

            // Show selected method config
            if (selectedMethod === 'totp') {
                document.getElementById('totpConfig').style.display = 'block';
            } else if (selectedMethod === 'sms') {
                document.getElementById('smsConfig').style.display = 'block';
            } else if (selectedMethod === 'email') {
                document.getElementById('emailConfig').style.display = 'block';
                // Set user email
                const user = JSON.parse(localStorage.getItem('user') || '{}');
                document.getElementById('userEmail').textContent = user.email || '<EMAIL>';
            }
        }

        function configureStep3() {
            const helpText = document.getElementById('verificationHelp');

            if (selectedMethod === 'totp') {
                helpText.textContent = 'Enter the 6-digit code from your authenticator app';
            } else if (selectedMethod === 'sms') {
                helpText.textContent = 'Enter the 6-digit code sent to your phone';
            } else if (selectedMethod === 'email') {
                helpText.textContent = 'Enter the 6-digit code sent to your email';
            }
        }

        // MFA Setup Functions
        async function handleVerification(event) {
            event.preventDefault();

            const code = document.getElementById('verificationCode').value;

            if (!code || code.length !== 6) {
                window.notificationManager.error('Please enter a 6-digit verification code');
                return;
            }

            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/verify-setup', {
                    method: selectedMethod,
                    code: code,
                    secretKey: secretKey
                });

                if (response.success) {
                    window.notificationManager.success('MFA verification successful');
                    nextStep();
                } else {
                    window.notificationManager.error(response.error || 'Invalid verification code');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate verification
                const validCodes = ['123456', '000000', '111111'];

                if (validCodes.includes(code)) {
                    window.notificationManager.success('MFA verification successful (simulated)');
                    nextStep();

                    showResponse('mfaResponse', {
                        success: true,
                        data: { method: selectedMethod, verified: true },
                        message: 'Mock MFA verification successful (endpoint may not be available)'
                    }, 'warning');
                } else {
                    window.notificationManager.error('Invalid verification code');

                    showResponse('mfaResponse', {
                        success: false,
                        error: 'Invalid verification code',
                        details: 'Code verification failed in simulation'
                    }, 'error');
                }
            }
        }

        async function completeMFASetup() {
            try {
                const response = await window.apiClient.request('POST', '/auth/mfa/complete-setup', {
                    method: selectedMethod,
                    backupCodes: backupCodes
                });

                if (response.success) {
                    window.notificationManager.success('MFA setup completed successfully');
                    setTimeout(() => {
                        window.location.href = '../management/index.html';
                    }, 2000);
                } else {
                    window.notificationManager.error(response.error || 'Failed to complete MFA setup');
                }

                showResponse('mfaResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate completion
                window.notificationManager.success('MFA setup completed successfully (simulated)');

                showResponse('mfaResponse', {
                    success: true,
                    data: {
                        method: selectedMethod,
                        backupCodes: backupCodes,
                        setupComplete: true
                    },
                    message: 'Mock MFA setup completion successful (endpoint may not be available)'
                }, 'warning');

                setTimeout(() => {
                    window.location.href = '../management/index.html';
                }, 2000);
            }
        }

        // Helper Functions
        function generateSecretKey() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
            let result = '';
            for (let i = 0; i < 16; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            secretKey = result;
            document.getElementById('secretKey').innerHTML = `
                ${secretKey}
                <button class="copy-button" onclick="copySecretKey()">
                    <i class="fas fa-copy"></i>
                    Copy
                </button>
            `;
        }

        function generateBackupCodes() {
            backupCodes = [];
            for (let i = 0; i < 8; i++) {
                const code = Math.random().toString(36).substr(2, 4).toUpperCase() + '-' +
                           Math.random().toString(36).substr(2, 4).toUpperCase() + '-' +
                           Math.random().toString(36).substr(2, 4).toUpperCase();
                backupCodes.push(code);
            }

            const codesList = document.getElementById('backupCodesList');
            codesList.innerHTML = backupCodes.map(code =>
                `<div class="backup-code">${code}</div>`
            ).join('');
        }

        function copySecretKey() {
            navigator.clipboard.writeText(secretKey).then(() => {
                window.notificationManager.success('Secret key copied to clipboard');
            }).catch(() => {
                window.notificationManager.error('Failed to copy secret key');
            });
        }

        function downloadBackupCodes() {
            const content = 'MFA Backup Recovery Codes\n\n' +
                          backupCodes.join('\n') +
                          '\n\nKeep these codes safe and secure. Each code can only be used once.';

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mfa-backup-codes.txt';
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Backup codes downloaded');
        }

        function printBackupCodes() {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head><title>MFA Backup Codes</title></head>
                <body>
                    <h1>MFA Backup Recovery Codes</h1>
                    <p>Keep these codes safe and secure. Each code can only be used once.</p>
                    <ul>
                        ${backupCodes.map(code => `<li style="font-family: monospace; font-size: 14px; margin: 10px 0;">${code}</li>`).join('')}
                    </ul>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();

            window.notificationManager.success('Backup codes sent to printer');
        }

        // Test Functions
        function sendTestSMS() {
            const phoneNumber = document.getElementById('phoneNumber').value;

            if (!phoneNumber) {
                window.notificationManager.error('Please enter a phone number');
                return;
            }

            window.notificationManager.info('Sending test SMS...');

            setTimeout(() => {
                window.notificationManager.success('Test SMS sent successfully');
            }, 2000);
        }

        function sendTestEmail() {
            window.notificationManager.info('Sending test email...');

            setTimeout(() => {
                window.notificationManager.success('Test email sent successfully');
            }, 2000);
        }

        // Quick Test Functions
        function startMFASetup() {
            currentStep = 1;
            document.querySelectorAll('.step-content').forEach(el => el.classList.remove('active'));
            document.getElementById('step1').classList.add('active');

            // Reset step indicators
            document.querySelectorAll('.step-number').forEach(el => {
                el.classList.remove('active', 'completed');
            });
            document.querySelectorAll('.step-connector').forEach(el => {
                el.classList.remove('completed');
            });

            document.getElementById('stepNumber1').classList.add('active');
            window.notificationManager.info('MFA setup started. Choose your preferred method.');
        }

        function testTOTPSetup() {
            selectMethod('totp');
            nextStep();
            window.notificationManager.info('TOTP setup configured. Use code 123456 to verify.');
        }

        function testSMSSetup() {
            selectMethod('sms');
            nextStep();
            document.getElementById('phoneNumber').value = '+****************';
            window.notificationManager.info('SMS setup configured. Use code 123456 to verify.');
        }

        function testBackupCodes() {
            generateNewBackupCodes();
            window.notificationManager.info('New backup codes generated.');
        }

        function simulateTOTPFlow() {
            startMFASetup();
            setTimeout(() => {
                selectMethod('totp');
                nextStep();
                setTimeout(() => {
                    document.getElementById('verificationCode').value = '123456';
                    window.notificationManager.info('TOTP flow simulated. Click Verify Code to continue.');
                }, 1000);
            }, 1000);
        }

        function simulateSMSFlow() {
            startMFASetup();
            setTimeout(() => {
                selectMethod('sms');
                nextStep();
                document.getElementById('phoneNumber').value = '+****************';
                setTimeout(() => {
                    nextStep();
                    document.getElementById('verificationCode').value = '123456';
                    window.notificationManager.info('SMS flow simulated. Click Verify Code to continue.');
                }, 1000);
            }, 1000);
        }

        function testInvalidCode() {
            if (currentStep === 3) {
                document.getElementById('verificationCode').value = '999999';
                window.notificationManager.warning('Invalid code filled. This should trigger an error.');
            } else {
                window.notificationManager.info('Navigate to verification step first.');
            }
        }

        function generateNewBackupCodes() {
            generateBackupCodes();
            window.notificationManager.success('New backup codes generated');
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
