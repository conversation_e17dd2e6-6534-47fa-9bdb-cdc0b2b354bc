<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User List Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-list-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .user-list-header {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .header-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--hover-background);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .user-controls {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .controls-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            min-width: 150px;
        }

        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .user-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .user-role {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            display: inline-block;
        }

        .user-role.admin {
            background: var(--error-color);
            color: white;
        }

        .user-role.moderator {
            background: var(--warning-color);
            color: white;
        }

        .user-role.user {
            background: var(--info-color);
            color: white;
        }

        .user-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .user-status.online {
            background: var(--success-color);
        }

        .user-status.offline {
            background: var(--text-secondary);
        }

        .user-details {
            margin-bottom: 1rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: var(--text-secondary);
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: bold;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: var(--info-color);
            color: white;
        }

        .action-btn.edit {
            background: var(--warning-color);
            color: white;
        }

        .action-btn.delete {
            background: var(--error-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: var(--hover-background);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .toggle-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .users-table {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            display: none;
        }

        .users-table.active {
            display: block;
        }

        .table-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 60px 1fr 120px 100px 150px 120px;
            gap: 1rem;
            align-items: center;
        }

        .table-row {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 60px 1fr 120px 100px 150px 120px;
            gap: 1rem;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .table-row:hover {
            background: var(--hover-background);
        }

        .table-row:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .users-grid {
                grid-template-columns: 1fr;
            }

            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .table-header,
            .table-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-users"></i>
                    <h1>User List Testing</h1>
                    <span class="subtitle">User List and Management Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to User Management
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="user-list-container">
                <!-- User List Header -->
                <div class="user-list-header">
                    <h2>User List Overview</h2>
                    <div class="header-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="onlineUsers">0</div>
                            <div class="stat-label">Online</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="newUsers">0</div>
                            <div class="stat-label">New This Week</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="activeUsers">0</div>
                            <div class="stat-label">Active Users</div>
                        </div>
                    </div>
                </div>

                <!-- User Controls -->
                <div class="user-controls">
                    <div class="controls-row">
                        <input type="text" class="search-box" id="userSearch" placeholder="Search users by name, email, or role..." onkeyup="filterUsers()">
                        <select class="filter-select" id="roleFilter" onchange="filterUsers()">
                            <option value="all">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="moderator">Moderator</option>
                            <option value="user">User</option>
                        </select>
                        <select class="filter-select" id="statusFilter" onchange="filterUsers()">
                            <option value="all">All Status</option>
                            <option value="online">Online</option>
                            <option value="offline">Offline</option>
                        </select>
                        <div class="view-toggle">
                            <button class="toggle-btn active" id="gridViewBtn" onclick="switchView('grid')">
                                <i class="fas fa-th"></i>
                                Grid
                            </button>
                            <button class="toggle-btn" id="tableViewBtn" onclick="switchView('table')">
                                <i class="fas fa-list"></i>
                                Table
                            </button>
                        </div>
                    </div>
                    <div class="controls-row">
                        <button class="btn btn-primary" onclick="refreshUsers()">
                            <i class="fas fa-sync"></i>
                            Refresh
                        </button>
                        <button class="btn btn-success" onclick="addUser()">
                            <i class="fas fa-user-plus"></i>
                            Add User
                        </button>
                        <button class="btn btn-info" onclick="exportUsers()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>

                <!-- Users Grid View -->
                <div class="users-grid active" id="usersGrid">
                    <!-- User cards will be populated here -->
                </div>

                <!-- Users Table View -->
                <div class="users-table" id="usersTable">
                    <div class="table-header">
                        <div>Avatar</div>
                        <div>User Info</div>
                        <div>Role</div>
                        <div>Status</div>
                        <div>Last Login</div>
                        <div>Actions</div>
                    </div>
                    <div id="usersTableBody">
                        <!-- Table rows will be populated here -->
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button class="pagination-btn" id="prevBtn" onclick="previousPage()" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <div class="pagination-info" id="paginationInfo">
                        Page 1 of 1 (0 users)
                    </div>
                    <button class="pagination-btn" id="nextBtn" onclick="nextPage()" disabled>
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- Response Viewer -->
                <div id="userListResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 User List Testing Interface</p>
                    <p>User list and management testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let userListData = {
            users: [],
            filteredUsers: [],
            currentPage: 1,
            itemsPerPage: 12,
            totalPages: 1,
            currentView: 'grid',
            stats: {
                totalUsers: 0,
                onlineUsers: 0,
                newUsers: 0,
                activeUsers: 0
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await checkServerHealth();
            updateAuthStatus();
            initializeTheme();
            await loadUsers();
        }

        // User Data Functions
        async function loadUsers() {
            window.notificationManager.info('Loading users...');

            try {
                const response = await window.apiClient.request('GET', '/users');

                if (response.success) {
                    updateUserData(response.data);
                    window.notificationManager.success('Users loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load users');
                }

                showResponse('userListResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                loadMockUsers();
                window.notificationManager.success('Users loaded successfully (simulated)');

                showResponse('userListResponse', {
                    success: true,
                    data: { users: userListData.users },
                    message: 'Mock users data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockUsers() {
            const mockUsers = [
                {
                    id: 'user_001',
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
                    loginCount: 127,
                    avatar: 'JD'
                },
                {
                    id: 'user_002',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
                    loginCount: 89,
                    avatar: 'JS'
                },
                {
                    id: 'user_003',
                    firstName: 'Bob',
                    lastName: 'Wilson',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'offline',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
                    loginCount: 45,
                    avatar: 'BW'
                },
                {
                    id: 'user_004',
                    firstName: 'Alice',
                    lastName: 'Brown',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'offline',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60), // 60 days ago
                    loginCount: 23,
                    avatar: 'AB'
                },
                {
                    id: 'user_005',
                    firstName: 'Charlie',
                    lastName: 'Davis',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
                    loginCount: 67,
                    avatar: 'CD'
                },
                {
                    id: 'user_006',
                    firstName: 'Diana',
                    lastName: 'Miller',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
                    loginCount: 34,
                    avatar: 'DM'
                }
            ];

            userListData.users = mockUsers;
            updateUserDisplay();
        }

        function updateUserData(data) {
            if (data.users) userListData.users = data.users;
            updateUserDisplay();
        }

        function updateUserDisplay() {
            updateUserStats();
            filterUsers();
        }

        function updateUserStats() {
            const stats = {
                totalUsers: userListData.users.length,
                onlineUsers: userListData.users.filter(u => u.status === 'online').length,
                newUsers: userListData.users.filter(u => {
                    const weekAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 7);
                    return u.createdAt >= weekAgo;
                }).length,
                activeUsers: userListData.users.filter(u => {
                    const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
                    return u.lastLogin >= dayAgo;
                }).length
            };

            document.getElementById('totalUsers').textContent = stats.totalUsers;
            document.getElementById('onlineUsers').textContent = stats.onlineUsers;
            document.getElementById('newUsers').textContent = stats.newUsers;
            document.getElementById('activeUsers').textContent = stats.activeUsers;

            userListData.stats = stats;
        }

        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            userListData.filteredUsers = userListData.users.filter(user => {
                const matchesSearch = !searchTerm ||
                    user.firstName.toLowerCase().includes(searchTerm) ||
                    user.lastName.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm) ||
                    user.role.toLowerCase().includes(searchTerm);

                const matchesRole = roleFilter === 'all' || user.role === roleFilter;
                const matchesStatus = statusFilter === 'all' || user.status === statusFilter;

                return matchesSearch && matchesRole && matchesStatus;
            });

            userListData.currentPage = 1;
            updatePagination();
            renderUsers();
        }

        function updatePagination() {
            const totalItems = userListData.filteredUsers.length;
            userListData.totalPages = Math.ceil(totalItems / userListData.itemsPerPage);

            const startItem = (userListData.currentPage - 1) * userListData.itemsPerPage + 1;
            const endItem = Math.min(userListData.currentPage * userListData.itemsPerPage, totalItems);

            document.getElementById('paginationInfo').textContent =
                `Page ${userListData.currentPage} of ${userListData.totalPages} (${totalItems} users)`;

            document.getElementById('prevBtn').disabled = userListData.currentPage <= 1;
            document.getElementById('nextBtn').disabled = userListData.currentPage >= userListData.totalPages;
        }

        function renderUsers() {
            if (userListData.currentView === 'grid') {
                renderUsersGrid();
            } else {
                renderUsersTable();
            }
        }

        function renderUsersGrid() {
            const grid = document.getElementById('usersGrid');
            const startIndex = (userListData.currentPage - 1) * userListData.itemsPerPage;
            const endIndex = startIndex + userListData.itemsPerPage;
            const usersToShow = userListData.filteredUsers.slice(startIndex, endIndex);

            if (usersToShow.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No users found matching the current filters.
                    </div>
                `;
                return;
            }

            const usersHTML = usersToShow.map(user => `
                <div class="user-card" style="position: relative;">
                    <div class="user-status ${user.status}"></div>
                    <div class="user-header">
                        <div class="user-avatar">${user.avatar}</div>
                        <div class="user-info">
                            <div class="user-name">${user.firstName} ${user.lastName}</div>
                            <div class="user-email">${user.email}</div>
                            <div class="user-role ${user.role}">${user.role.toUpperCase()}</div>
                        </div>
                    </div>
                    <div class="user-details">
                        <div class="detail-row">
                            <span class="detail-label">Last Login:</span>
                            <span class="detail-value">${formatTimeAgo(user.lastLogin)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Login Count:</span>
                            <span class="detail-value">${user.loginCount}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Member Since:</span>
                            <span class="detail-value">${formatDate(user.createdAt)}</span>
                        </div>
                    </div>
                    <div class="user-actions">
                        <button class="action-btn view" onclick="viewUser('${user.id}')" title="View User">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="editUser('${user.id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteUser('${user.id}')" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            grid.innerHTML = usersHTML;
        }

        function renderUsersTable() {
            const tableBody = document.getElementById('usersTableBody');
            const startIndex = (userListData.currentPage - 1) * userListData.itemsPerPage;
            const endIndex = startIndex + userListData.itemsPerPage;
            const usersToShow = userListData.filteredUsers.slice(startIndex, endIndex);

            if (usersToShow.length === 0) {
                tableBody.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No users found matching the current filters.
                    </div>
                `;
                return;
            }

            const usersHTML = usersToShow.map(user => `
                <div class="table-row">
                    <div class="user-avatar">${user.avatar}</div>
                    <div>
                        <div class="user-name">${user.firstName} ${user.lastName}</div>
                        <div class="user-email">${user.email}</div>
                    </div>
                    <div class="user-role ${user.role}">${user.role.toUpperCase()}</div>
                    <div class="user-status ${user.status}">${user.status.toUpperCase()}</div>
                    <div>${formatTimeAgo(user.lastLogin)}</div>
                    <div class="user-actions">
                        <button class="action-btn view" onclick="viewUser('${user.id}')" title="View User">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="editUser('${user.id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteUser('${user.id}')" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            tableBody.innerHTML = usersHTML;
        }

        // View Functions
        function switchView(view) {
            userListData.currentView = view;

            // Update toggle buttons
            document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
            document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');

            // Update view containers
            document.getElementById('usersGrid').classList.toggle('active', view === 'grid');
            document.getElementById('usersTable').classList.toggle('active', view === 'table');

            renderUsers();
        }

        // Pagination Functions
        function previousPage() {
            if (userListData.currentPage > 1) {
                userListData.currentPage--;
                updatePagination();
                renderUsers();
            }
        }

        function nextPage() {
            if (userListData.currentPage < userListData.totalPages) {
                userListData.currentPage++;
                updatePagination();
                renderUsers();
            }
        }

        // User Actions
        function viewUser(userId) {
            const user = userListData.users.find(u => u.id === userId);
            if (user) {
                window.notificationManager.info(`Viewing user: ${user.firstName} ${user.lastName}`);
            }
        }

        function editUser(userId) {
            const user = userListData.users.find(u => u.id === userId);
            if (user) {
                window.notificationManager.info(`Editing user: ${user.firstName} ${user.lastName}`);
            }
        }

        function deleteUser(userId) {
            const user = userListData.users.find(u => u.id === userId);
            if (user && confirm(`Are you sure you want to delete user: ${user.firstName} ${user.lastName}?`)) {
                userListData.users = userListData.users.filter(u => u.id !== userId);
                updateUserDisplay();
                window.notificationManager.success('User deleted successfully');
            }
        }

        function refreshUsers() {
            loadUsers();
        }

        function addUser() {
            window.notificationManager.info('Add user functionality would open a modal or redirect to user creation page');
        }

        function exportUsers() {
            window.notificationManager.info('Exporting users data...');
            const exportData = {
                timestamp: new Date().toISOString(),
                totalUsers: userListData.users.length,
                users: userListData.users
            };
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `users-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);
            window.notificationManager.success('Users data exported successfully');
        }

        // Utility Functions
        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        function formatDate(timestamp) {
            return timestamp.toLocaleDateString();
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else throw new Error('Server error');
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');
            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');
            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
