<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Administration Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 2rem auto;
        }

        .admin-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .stat-card.total::before {
            background: var(--primary-color);
        }

        .stat-card.active::before {
            background: var(--success-color);
        }

        .stat-card.inactive::before {
            background: var(--warning-color);
        }

        .stat-card.blocked::before {
            background: var(--error-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .stat-change.positive {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .stat-change.negative {
            background: rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
        }

        .admin-controls {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .controls-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            min-width: 150px;
        }

        .users-table {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .table-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 40px 1fr 150px 120px 150px 120px 150px;
            gap: 1rem;
            align-items: center;
        }

        .user-row {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 40px 1fr 150px 120px 150px 120px 150px;
            gap: 1rem;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .user-row:hover {
            background: var(--hover-background);
        }

        .user-row:last-child {
            border-bottom: none;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .user-role {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            text-align: center;
        }

        .user-role.admin {
            background: var(--error-color);
            color: white;
        }

        .user-role.moderator {
            background: var(--warning-color);
            color: white;
        }

        .user-role.user {
            background: var(--info-color);
            color: white;
        }

        .user-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            text-align: center;
        }

        .user-status.active {
            background: var(--success-color);
            color: white;
        }

        .user-status.inactive {
            background: var(--warning-color);
            color: white;
        }

        .user-status.blocked {
            background: var(--error-color);
            color: white;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: var(--info-color);
            color: white;
        }

        .action-btn.edit {
            background: var(--warning-color);
            color: white;
        }

        .action-btn.block {
            background: var(--error-color);
            color: white;
        }

        .action-btn.activate {
            background: var(--success-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .bulk-actions {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }

        .bulk-actions.show {
            display: block;
        }

        .bulk-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .user-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: var(--hover-background);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 1024px) {
            .table-header,
            .user-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .user-info {
                order: 1;
            }

            .user-actions {
                order: 2;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-users-cog"></i>
                    <h1>User Administration Testing</h1>
                    <span class="subtitle">User Administration and Management Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to User Management
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="admin-container">
                <!-- Admin Overview -->
                <div class="admin-overview">
                    <h2>User Administration Overview</h2>
                    <div class="overview-stats">
                        <div class="stat-card total">
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">Total Users</div>
                            <div class="stat-change positive" id="totalChange">+5.2%</div>
                        </div>
                        <div class="stat-card active">
                            <div class="stat-number" id="activeUsers">0</div>
                            <div class="stat-label">Active Users</div>
                            <div class="stat-change positive" id="activeChange">+3.1%</div>
                        </div>
                        <div class="stat-card inactive">
                            <div class="stat-number" id="inactiveUsers">0</div>
                            <div class="stat-label">Inactive Users</div>
                            <div class="stat-change negative" id="inactiveChange">-1.8%</div>
                        </div>
                        <div class="stat-card blocked">
                            <div class="stat-number" id="blockedUsers">0</div>
                            <div class="stat-label">Blocked Users</div>
                            <div class="stat-change positive" id="blockedChange">+0.5%</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="refreshUsers()">
                        <i class="fas fa-sync"></i>
                        Refresh Users
                    </button>
                    <button class="btn btn-success" onclick="createUser()">
                        <i class="fas fa-user-plus"></i>
                        Create User
                    </button>
                    <button class="btn btn-info" onclick="exportUsers()">
                        <i class="fas fa-download"></i>
                        Export Users
                    </button>
                    <button class="btn btn-warning" onclick="bulkImport()">
                        <i class="fas fa-upload"></i>
                        Bulk Import
                    </button>
                </div>

                <!-- User Controls -->
                <div class="admin-controls">
                    <div class="controls-row">
                        <input type="text" class="search-box" id="userSearch" placeholder="Search users by name, email, or role..." onkeyup="filterUsers()">
                        <select class="filter-select" id="statusFilter" onchange="filterUsers()">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="blocked">Blocked</option>
                        </select>
                        <select class="filter-select" id="roleFilter" onchange="filterUsers()">
                            <option value="all">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="moderator">Moderator</option>
                            <option value="user">User</option>
                        </select>
                        <button class="btn btn-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="bulk-controls">
                        <span id="selectedCount">0 users selected</span>
                        <button class="btn btn-sm btn-success" onclick="bulkActivate()">
                            <i class="fas fa-check"></i>
                            Activate Selected
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="bulkDeactivate()">
                            <i class="fas fa-pause"></i>
                            Deactivate Selected
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="bulkBlock()">
                            <i class="fas fa-ban"></i>
                            Block Selected
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                            <i class="fas fa-times"></i>
                            Clear Selection
                        </button>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="users-table">
                    <div class="table-header">
                        <div>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </div>
                        <div>User</div>
                        <div>Role</div>
                        <div>Status</div>
                        <div>Last Login</div>
                        <div>Created</div>
                        <div>Actions</div>
                    </div>
                    <div id="usersTableBody">
                        <!-- User rows will be populated here -->
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button class="pagination-btn" id="prevBtn" onclick="previousPage()" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <div class="pagination-info" id="paginationInfo">
                        Page 1 of 1 (0 users)
                    </div>
                    <button class="pagination-btn" id="nextBtn" onclick="nextPage()" disabled>
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- User Modal -->
                <div class="user-modal" id="userModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title" id="modalTitle">User Details</div>
                            <button class="close-btn" onclick="closeUserModal()">&times;</button>
                        </div>
                        <form id="userForm">
                            <div class="form-group">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-input" id="modalFirstName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-input" id="modalLastName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-input" id="modalEmail" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Role</label>
                                <select class="form-select" id="modalRole" required>
                                    <option value="user">User</option>
                                    <option value="moderator">Moderator</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="modalStatus" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="blocked">Blocked</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save User
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeUserModal()">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="adminResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 User Administration Testing Interface</p>
                    <p>User administration and management testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let adminData = {
            users: [],
            filteredUsers: [],
            selectedUsers: new Set(),
            currentPage: 1,
            itemsPerPage: 10,
            totalPages: 1,
            currentUser: null,
            editMode: false
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load users data
            await loadUsers();

            // Initialize form handler
            initializeFormHandler();
        }

        // User Management Functions
        async function loadUsers() {
            window.notificationManager.info('Loading users...');

            try {
                const response = await window.apiClient.request('GET', '/admin/users');

                if (response.success) {
                    adminData.users = response.data.users || [];
                    updateUsersDisplay();
                    window.notificationManager.success('Users loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load users');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock users data
                loadMockUsers();
                window.notificationManager.success('Users loaded successfully (simulated)');

                showResponse('adminResponse', {
                    success: true,
                    data: { users: adminData.users },
                    message: 'Mock users data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockUsers() {
            const mockUsers = [
                {
                    id: 'user_001',
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
                    loginCount: 127,
                    sessionCount: 3
                },
                {
                    id: 'user_002',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
                    loginCount: 89,
                    sessionCount: 1
                },
                {
                    id: 'user_003',
                    firstName: 'Bob',
                    lastName: 'Wilson',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
                    loginCount: 45,
                    sessionCount: 0
                },
                {
                    id: 'user_004',
                    firstName: 'Alice',
                    lastName: 'Brown',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'inactive',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60), // 60 days ago
                    loginCount: 23,
                    sessionCount: 0
                },
                {
                    id: 'user_005',
                    firstName: 'Charlie',
                    lastName: 'Davis',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'blocked',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14), // 14 days ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45), // 45 days ago
                    loginCount: 12,
                    sessionCount: 0
                },
                {
                    id: 'user_006',
                    firstName: 'Diana',
                    lastName: 'Miller',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20), // 20 days ago
                    loginCount: 67,
                    sessionCount: 2
                }
            ];

            adminData.users = mockUsers;
            updateUsersDisplay();
        }

        function updateUsersDisplay() {
            // Apply filters
            filterUsers();

            // Update statistics
            updateUserStats();

            // Update pagination
            updatePagination();

            // Render users table
            renderUsersTable();
        }

        function updateUserStats() {
            const stats = {
                total: adminData.users.length,
                active: adminData.users.filter(u => u.status === 'active').length,
                inactive: adminData.users.filter(u => u.status === 'inactive').length,
                blocked: adminData.users.filter(u => u.status === 'blocked').length
            };

            document.getElementById('totalUsers').textContent = stats.total;
            document.getElementById('activeUsers').textContent = stats.active;
            document.getElementById('inactiveUsers').textContent = stats.inactive;
            document.getElementById('blockedUsers').textContent = stats.blocked;
        }

        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const roleFilter = document.getElementById('roleFilter').value;

            adminData.filteredUsers = adminData.users.filter(user => {
                const matchesSearch = !searchTerm ||
                    user.firstName.toLowerCase().includes(searchTerm) ||
                    user.lastName.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm) ||
                    user.role.toLowerCase().includes(searchTerm);

                const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
                const matchesRole = roleFilter === 'all' || user.role === roleFilter;

                return matchesSearch && matchesStatus && matchesRole;
            });

            // Reset to first page when filtering
            adminData.currentPage = 1;
            updatePagination();
            renderUsersTable();
        }

        function clearFilters() {
            document.getElementById('userSearch').value = '';
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('roleFilter').value = 'all';
            filterUsers();
        }

        function updatePagination() {
            const totalItems = adminData.filteredUsers.length;
            adminData.totalPages = Math.ceil(totalItems / adminData.itemsPerPage);

            const startItem = (adminData.currentPage - 1) * adminData.itemsPerPage + 1;
            const endItem = Math.min(adminData.currentPage * adminData.itemsPerPage, totalItems);

            document.getElementById('paginationInfo').textContent =
                `Page ${adminData.currentPage} of ${adminData.totalPages} (${totalItems} users)`;

            document.getElementById('prevBtn').disabled = adminData.currentPage <= 1;
            document.getElementById('nextBtn').disabled = adminData.currentPage >= adminData.totalPages;
        }

        function previousPage() {
            if (adminData.currentPage > 1) {
                adminData.currentPage--;
                updatePagination();
                renderUsersTable();
            }
        }

        function nextPage() {
            if (adminData.currentPage < adminData.totalPages) {
                adminData.currentPage++;
                updatePagination();
                renderUsersTable();
            }
        }

        function renderUsersTable() {
            const tableBody = document.getElementById('usersTableBody');
            const startIndex = (adminData.currentPage - 1) * adminData.itemsPerPage;
            const endIndex = startIndex + adminData.itemsPerPage;
            const usersToShow = adminData.filteredUsers.slice(startIndex, endIndex);

            if (usersToShow.length === 0) {
                tableBody.innerHTML = `
                    <div style="padding: 2rem; text-align: center; color: var(--text-secondary); grid-column: 1 / -1;">
                        No users found matching the current filters.
                    </div>
                `;
                return;
            }

            const usersHTML = usersToShow.map(user => `
                <div class="user-row">
                    <div>
                        <input type="checkbox" class="user-checkbox" value="${user.id}" onchange="toggleUserSelection('${user.id}')">
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">${getInitials(user.firstName, user.lastName)}</div>
                        <div>
                            <div class="user-name">${user.firstName} ${user.lastName}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                    </div>
                    <div class="user-role ${user.role}">${user.role.toUpperCase()}</div>
                    <div class="user-status ${user.status}">${user.status.toUpperCase()}</div>
                    <div>${formatTimeAgo(user.lastLogin)}</div>
                    <div>${formatDate(user.createdAt)}</div>
                    <div class="user-actions">
                        <button class="action-btn view" onclick="viewUser('${user.id}')" title="View User">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="editUser('${user.id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.status === 'blocked' ? `
                            <button class="action-btn activate" onclick="activateUser('${user.id}')" title="Activate User">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : `
                            <button class="action-btn block" onclick="blockUser('${user.id}')" title="Block User">
                                <i class="fas fa-ban"></i>
                            </button>
                        `}
                    </div>
                </div>
            `).join('');

            tableBody.innerHTML = usersHTML;
        }

        // User Actions
        async function refreshUsers() {
            await loadUsers();
        }

        function createUser() {
            adminData.currentUser = null;
            adminData.editMode = false;

            document.getElementById('modalTitle').textContent = 'Create New User';
            document.getElementById('userForm').reset();
            document.getElementById('userModal').style.display = 'flex';
        }

        function viewUser(userId) {
            const user = adminData.users.find(u => u.id === userId);
            if (!user) return;

            adminData.currentUser = user;
            adminData.editMode = false;

            document.getElementById('modalTitle').textContent = 'View User Details';
            populateUserForm(user);
            disableFormInputs(true);
            document.getElementById('userModal').style.display = 'flex';
        }

        function editUser(userId) {
            const user = adminData.users.find(u => u.id === userId);
            if (!user) return;

            adminData.currentUser = user;
            adminData.editMode = true;

            document.getElementById('modalTitle').textContent = 'Edit User';
            populateUserForm(user);
            disableFormInputs(false);
            document.getElementById('userModal').style.display = 'flex';
        }

        function populateUserForm(user) {
            document.getElementById('modalFirstName').value = user.firstName;
            document.getElementById('modalLastName').value = user.lastName;
            document.getElementById('modalEmail').value = user.email;
            document.getElementById('modalRole').value = user.role;
            document.getElementById('modalStatus').value = user.status;
        }

        function disableFormInputs(disabled) {
            const inputs = document.querySelectorAll('#userForm input, #userForm select');
            inputs.forEach(input => {
                input.disabled = disabled;
            });

            const submitBtn = document.querySelector('#userForm button[type="submit"]');
            submitBtn.style.display = disabled ? 'none' : 'inline-block';
        }

        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            adminData.currentUser = null;
            adminData.editMode = false;
        }

        function initializeFormHandler() {
            document.getElementById('userForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    firstName: document.getElementById('modalFirstName').value,
                    lastName: document.getElementById('modalLastName').value,
                    email: document.getElementById('modalEmail').value,
                    role: document.getElementById('modalRole').value,
                    status: document.getElementById('modalStatus').value
                };

                if (adminData.editMode && adminData.currentUser) {
                    await updateUser(adminData.currentUser.id, formData);
                } else {
                    await createNewUser(formData);
                }
            });
        }

        async function createNewUser(userData) {
            window.notificationManager.info('Creating new user...');

            try {
                const response = await window.apiClient.request('POST', '/admin/users', userData);

                if (response.success) {
                    const newUser = {
                        id: 'user_' + Date.now(),
                        ...userData,
                        lastLogin: null,
                        createdAt: new Date(),
                        loginCount: 0,
                        sessionCount: 0
                    };

                    adminData.users.unshift(newUser);
                    updateUsersDisplay();
                    closeUserModal();
                    window.notificationManager.success('User created successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to create user');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user creation
                const newUser = {
                    id: 'user_' + Date.now(),
                    ...userData,
                    lastLogin: null,
                    createdAt: new Date(),
                    loginCount: 0,
                    sessionCount: 0
                };

                adminData.users.unshift(newUser);
                updateUsersDisplay();
                closeUserModal();
                window.notificationManager.success('User created successfully (simulated)');

                showResponse('adminResponse', {
                    success: true,
                    data: newUser,
                    message: 'Mock user creation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function updateUser(userId, userData) {
            window.notificationManager.info('Updating user...');

            try {
                const response = await window.apiClient.request('PUT', `/admin/users/${userId}`, userData);

                if (response.success) {
                    const userIndex = adminData.users.findIndex(u => u.id === userId);
                    if (userIndex !== -1) {
                        adminData.users[userIndex] = { ...adminData.users[userIndex], ...userData };
                        updateUsersDisplay();
                        closeUserModal();
                        window.notificationManager.success('User updated successfully');
                    }
                } else {
                    window.notificationManager.error(response.error || 'Failed to update user');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user update
                const userIndex = adminData.users.findIndex(u => u.id === userId);
                if (userIndex !== -1) {
                    adminData.users[userIndex] = { ...adminData.users[userIndex], ...userData };
                    updateUsersDisplay();
                    closeUserModal();
                    window.notificationManager.success('User updated successfully (simulated)');

                    showResponse('adminResponse', {
                        success: true,
                        data: userData,
                        message: 'Mock user update successful (endpoint may not be available)'
                    }, 'warning');
                }
            }
        }

        async function blockUser(userId) {
            const user = adminData.users.find(u => u.id === userId);
            if (!user) return;

            if (!confirm(`Are you sure you want to block user: ${user.firstName} ${user.lastName}?`)) {
                return;
            }

            window.notificationManager.warning(`Blocking user: ${user.firstName} ${user.lastName}`);

            try {
                const response = await window.apiClient.request('PUT', `/admin/users/${userId}/block`);

                if (response.success) {
                    user.status = 'blocked';
                    updateUsersDisplay();
                    window.notificationManager.success('User blocked successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to block user');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user block
                user.status = 'blocked';
                updateUsersDisplay();
                window.notificationManager.success('User blocked successfully (simulated)');

                showResponse('adminResponse', {
                    success: true,
                    data: { userId: userId, status: 'blocked' },
                    message: 'Mock user block successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function activateUser(userId) {
            const user = adminData.users.find(u => u.id === userId);
            if (!user) return;

            window.notificationManager.info(`Activating user: ${user.firstName} ${user.lastName}`);

            try {
                const response = await window.apiClient.request('PUT', `/admin/users/${userId}/activate`);

                if (response.success) {
                    user.status = 'active';
                    updateUsersDisplay();
                    window.notificationManager.success('User activated successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to activate user');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate user activation
                user.status = 'active';
                updateUsersDisplay();
                window.notificationManager.success('User activated successfully (simulated)');

                showResponse('adminResponse', {
                    success: true,
                    data: { userId: userId, status: 'active' },
                    message: 'Mock user activation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Selection Management
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');

            userCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    adminData.selectedUsers.add(checkbox.value);
                } else {
                    adminData.selectedUsers.delete(checkbox.value);
                }
            });

            updateBulkActions();
        }

        function toggleUserSelection(userId) {
            if (adminData.selectedUsers.has(userId)) {
                adminData.selectedUsers.delete(userId);
            } else {
                adminData.selectedUsers.add(userId);
            }

            updateBulkActions();
        }

        function updateBulkActions() {
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            const count = adminData.selectedUsers.size;

            if (count > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = `${count} user${count === 1 ? '' : 's'} selected`;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        function clearSelection() {
            adminData.selectedUsers.clear();
            document.getElementById('selectAll').checked = false;
            document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
            updateBulkActions();
        }

        // Bulk Operations
        async function bulkActivate() {
            const selectedIds = Array.from(adminData.selectedUsers);
            if (selectedIds.length === 0) return;

            if (!confirm(`Are you sure you want to activate ${selectedIds.length} selected user${selectedIds.length === 1 ? '' : 's'}?`)) {
                return;
            }

            window.notificationManager.info(`Activating ${selectedIds.length} selected users...`);

            try {
                const response = await window.apiClient.request('PUT', '/admin/users/bulk-activate', {
                    userIds: selectedIds
                });

                if (response.success) {
                    selectedIds.forEach(userId => {
                        const user = adminData.users.find(u => u.id === userId);
                        if (user) user.status = 'active';
                    });
                    clearSelection();
                    updateUsersDisplay();
                    window.notificationManager.success(`${selectedIds.length} users activated successfully`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to activate selected users');
                }

                showResponse('adminResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate bulk activation
                selectedIds.forEach(userId => {
                    const user = adminData.users.find(u => u.id === userId);
                    if (user) user.status = 'active';
                });
                clearSelection();
                updateUsersDisplay();
                window.notificationManager.success(`${selectedIds.length} users activated successfully (simulated)`);

                showResponse('adminResponse', {
                    success: true,
                    data: { activatedCount: selectedIds.length },
                    message: 'Mock bulk activation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function bulkDeactivate() {
            const selectedIds = Array.from(adminData.selectedUsers);
            if (selectedIds.length === 0) return;

            if (!confirm(`Are you sure you want to deactivate ${selectedIds.length} selected user${selectedIds.length === 1 ? '' : 's'}?`)) {
                return;
            }

            window.notificationManager.warning(`Deactivating ${selectedIds.length} selected users...`);

            // Simulate bulk deactivation
            selectedIds.forEach(userId => {
                const user = adminData.users.find(u => u.id === userId);
                if (user) user.status = 'inactive';
            });
            clearSelection();
            updateUsersDisplay();
            window.notificationManager.success(`${selectedIds.length} users deactivated successfully (simulated)`);
        }

        async function bulkBlock() {
            const selectedIds = Array.from(adminData.selectedUsers);
            if (selectedIds.length === 0) return;

            if (!confirm(`Are you sure you want to BLOCK ${selectedIds.length} selected user${selectedIds.length === 1 ? '' : 's'}? This will prevent them from accessing the system.`)) {
                return;
            }

            window.notificationManager.error(`Blocking ${selectedIds.length} selected users...`);

            // Simulate bulk block
            selectedIds.forEach(userId => {
                const user = adminData.users.find(u => u.id === userId);
                if (user) user.status = 'blocked';
            });
            clearSelection();
            updateUsersDisplay();
            window.notificationManager.success(`${selectedIds.length} users blocked successfully (simulated)`);
        }

        // Additional Actions
        function exportUsers() {
            window.notificationManager.info('Exporting users data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                totalUsers: adminData.users.length,
                users: adminData.users.map(user => ({
                    ...user,
                    lastLogin: user.lastLogin ? user.lastLogin.toISOString() : null,
                    createdAt: user.createdAt.toISOString()
                }))
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `users-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Users data exported successfully');
        }

        function bulkImport() {
            window.notificationManager.info('Bulk import functionality would be implemented here');

            // Simulate bulk import
            setTimeout(() => {
                window.notificationManager.success('Bulk import interface would allow CSV/JSON file upload');
            }, 1000);
        }

        // Utility Functions
        function getInitials(firstName, lastName) {
            return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
        }

        function formatTimeAgo(timestamp) {
            if (!timestamp) return 'Never';

            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        function formatDate(timestamp) {
            return timestamp.toLocaleDateString();
        }

        // Close modal when clicking outside
        document.getElementById('userModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUserModal();
            }
        });

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
