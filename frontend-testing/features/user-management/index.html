<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .user-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .user-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .user-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .user-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-card .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .user-table th,
        .user-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .user-table th {
            background: var(--card-background);
            font-weight: bold;
            color: var(--text-primary);
        }

        .user-table tbody tr:hover {
            background: var(--hover-background);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .user-email {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .role-badge.admin {
            background: var(--error-color);
            color: white;
        }

        .role-badge.user {
            background: var(--success-color);
            color: white;
        }

        .role-badge.moderator {
            background: var(--warning-color);
            color: white;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-badge.active {
            background: var(--success-color);
            color: white;
        }

        .status-badge.inactive {
            background: var(--text-secondary);
            color: white;
        }

        .status-badge.suspended {
            background: var(--error-color);
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: var(--hover-background);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .user-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .form-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .form-section h4 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal {
            background: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .table-container {
            overflow-x: auto;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-users-cog"></i>
                    <h1>User Management Testing</h1>
                    <span class="subtitle">Admin User Management Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Hub
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Navigation Tabs -->
            <div class="user-tabs">
                <button class="user-tab active" onclick="switchTab('overview')">
                    <i class="fas fa-chart-pie"></i>
                    Overview
                </button>
                <button class="user-tab" onclick="switchTab('user-list')">
                    <i class="fas fa-users"></i>
                    User List
                </button>
                <button class="user-tab" onclick="switchTab('role-management')">
                    <i class="fas fa-user-shield"></i>
                    Role Management
                </button>
                <button class="user-tab" onclick="switchTab('user-search')">
                    <i class="fas fa-search"></i>
                    User Search
                </button>
                <button class="user-tab" onclick="switchTab('bulk-actions')">
                    <i class="fas fa-tasks"></i>
                    Bulk Actions
                </button>
            </div>

            <!-- Overview Tab -->
            <div class="tab-content active" id="overview-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>User Statistics Overview</h3>
                        <p>Comprehensive user management statistics and metrics</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadUserStats()">
                                <i class="fas fa-sync"></i>
                                Refresh Stats
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateUserReport()">
                                <i class="fas fa-file-alt"></i>
                                Generate Report
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearOverviewDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear Display
                            </button>
                        </div>

                        <div class="user-stats-grid" id="userStatsGrid">
                            <!-- User statistics will be populated here -->
                        </div>

                        <div id="overviewResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- User List Tab -->
            <div class="tab-content" id="user-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>User List Management</h3>
                        <p>View and manage all users in the system</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadAllUsers()">
                                <i class="fas fa-sync"></i>
                                Load Users
                            </button>
                            <button class="btn btn-sm btn-success" onclick="openCreateUserModal()">
                                <i class="fas fa-plus"></i>
                                Create User
                            </button>
                            <button class="btn btn-sm btn-info" onclick="exportUserList()">
                                <i class="fas fa-download"></i>
                                Export List
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearUserList()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="search-filters">
                            <div class="form-group">
                                <label for="userRoleFilter">Filter by Role:</label>
                                <select id="userRoleFilter" class="form-control" onchange="filterUsers()">
                                    <option value="">All Roles</option>
                                    <option value="ADMIN">Admin</option>
                                    <option value="USER">User</option>
                                    <option value="MODERATOR">Moderator</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="userStatusFilter">Filter by Status:</label>
                                <select id="userStatusFilter" class="form-control" onchange="filterUsers()">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="suspended">Suspended</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="userSearchInput">Search Users:</label>
                                <input type="text" id="userSearchInput" class="form-control" placeholder="Search by name or email" oninput="searchUsers()">
                            </div>
                            <div class="form-group">
                                <label for="usersPerPage">Users per page:</label>
                                <select id="usersPerPage" class="form-control" onchange="changePageSize()">
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="user-table" id="userTable">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()">
                                        </th>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Last Login</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="userTableBody">
                                    <!-- User rows will be populated here -->
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination" id="userPagination">
                            <!-- Pagination will be populated here -->
                        </div>

                        <div id="userListResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Role Management Tab -->
            <div class="tab-content" id="role-management-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Role Management</h3>
                        <p>Manage user roles and permissions</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadRoleStats()">
                                <i class="fas fa-sync"></i>
                                Load Role Stats
                            </button>
                            <button class="btn btn-sm btn-info" onclick="testRolePermissions()">
                                <i class="fas fa-shield-check"></i>
                                Test Permissions
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearRoleDisplay()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="form-section">
                            <h4>Change User Role</h4>
                            <div class="form-group">
                                <label for="roleChangeUserId">User ID or Email:</label>
                                <input type="text" id="roleChangeUserId" class="form-control" placeholder="Enter user ID or email">
                            </div>
                            <div class="form-group">
                                <label for="newUserRole">New Role:</label>
                                <select id="newUserRole" class="form-control">
                                    <option value="USER">User</option>
                                    <option value="MODERATOR">Moderator</option>
                                    <option value="ADMIN">Admin</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" onclick="changeUserRole()">
                                <i class="fas fa-user-edit"></i>
                                Change Role
                            </button>
                        </div>

                        <div class="user-stats-grid" id="roleStatsGrid">
                            <!-- Role statistics will be populated here -->
                        </div>

                        <div id="roleManagementResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- User Search Tab -->
            <div class="tab-content" id="user-search-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Advanced User Search</h3>
                        <p>Search users with advanced filters and criteria</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="performAdvancedSearch()">
                                <i class="fas fa-search"></i>
                                Search Users
                            </button>
                            <button class="btn btn-sm btn-info" onclick="saveSearchQuery()">
                                <i class="fas fa-save"></i>
                                Save Query
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearSearchResults()">
                                <i class="fas fa-eraser"></i>
                                Clear Results
                            </button>
                        </div>

                        <div class="search-filters">
                            <div class="form-group">
                                <label for="searchEmail">Email:</label>
                                <input type="email" id="searchEmail" class="form-control" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="searchName">Name:</label>
                                <input type="text" id="searchName" class="form-control" placeholder="John Doe">
                            </div>
                            <div class="form-group">
                                <label for="searchRole">Role:</label>
                                <select id="searchRole" class="form-control">
                                    <option value="">Any Role</option>
                                    <option value="ADMIN">Admin</option>
                                    <option value="USER">User</option>
                                    <option value="MODERATOR">Moderator</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="searchDateFrom">Created From:</label>
                                <input type="date" id="searchDateFrom" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="searchDateTo">Created To:</label>
                                <input type="date" id="searchDateTo" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="searchMfaEnabled">MFA Status:</label>
                                <select id="searchMfaEnabled" class="form-control">
                                    <option value="">Any</option>
                                    <option value="true">Enabled</option>
                                    <option value="false">Disabled</option>
                                </select>
                            </div>
                        </div>

                        <div id="searchResults">
                            <!-- Search results will be displayed here -->
                        </div>

                        <div id="userSearchResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Bulk Actions Tab -->
            <div class="tab-content" id="bulk-actions-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Bulk User Actions</h3>
                        <p>Perform actions on multiple users simultaneously</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadSelectedUsers()">
                                <i class="fas fa-sync"></i>
                                Load Selected
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="performBulkAction()">
                                <i class="fas fa-play"></i>
                                Execute Action
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearBulkActions()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="form-section">
                            <h4>Bulk Action Configuration</h4>
                            <div class="form-group">
                                <label for="bulkAction">Action to Perform:</label>
                                <select id="bulkAction" class="form-control">
                                    <option value="change-role">Change Role</option>
                                    <option value="suspend">Suspend Users</option>
                                    <option value="activate">Activate Users</option>
                                    <option value="delete">Delete Users</option>
                                    <option value="send-email">Send Email</option>
                                </select>
                            </div>
                            <div class="form-group" id="bulkActionParams">
                                <!-- Dynamic parameters based on selected action -->
                            </div>
                        </div>

                        <div class="form-section">
                            <h4>Target Users</h4>
                            <div class="form-group">
                                <label for="userSelectionMethod">Selection Method:</label>
                                <select id="userSelectionMethod" class="form-control" onchange="updateUserSelection()">
                                    <option value="manual">Manual Selection</option>
                                    <option value="criteria">By Criteria</option>
                                    <option value="upload">Upload List</option>
                                </select>
                            </div>
                            <div id="userSelectionContainer">
                                <!-- User selection interface will be populated here -->
                            </div>
                        </div>

                        <div id="bulkActionResults">
                            <!-- Bulk action results will be displayed here -->
                        </div>

                        <div id="bulkActionsResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- User Modal -->
    <div class="modal-overlay" id="userModal">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modalTitle">Create User</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm" class="user-form">
                    <div class="form-group">
                        <label for="modalUserEmail">Email:</label>
                        <input type="email" id="modalUserEmail" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="modalUserName">Name:</label>
                        <input type="text" id="modalUserName" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="modalUserRole">Role:</label>
                        <select id="modalUserRole" class="form-control">
                            <option value="USER">User</option>
                            <option value="MODERATOR">Moderator</option>
                            <option value="ADMIN">Admin</option>
                        </select>
                    </div>
                    <div class="form-group" id="passwordGroup">
                        <label for="modalUserPassword">Password:</label>
                        <input type="password" id="modalUserPassword" class="form-control">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeUserModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="modalSubmitBtn">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 User Management Testing Interface</p>
                    <p>Admin user management and administration</p>
                </div>
                <div class="footer-links">
                    <a href="../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentUsers = [];
        let filteredUsers = [];
        let currentPage = 1;
        let usersPerPage = 25;
        let selectedUsers = [];
        let currentEditingUser = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Check admin permissions
            if (window.authManager && window.authManager.isAuthenticated()) {
                await checkAdminPermissions();
                await loadUserStats();
            }
        }

        async function checkAdminPermissions() {
            try {
                const response = await window.apiClient.request('GET', '/auth/can-access-users');
                if (!response.success) {
                    window.notificationManager.error('Admin permissions required for user management');
                    setTimeout(() => {
                        window.location.href = '../../index.html';
                    }, 3000);
                }
            } catch (error) {
                window.notificationManager.error('Unable to verify admin permissions');
            }
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.user-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            // Load data for specific tabs
            if (tabName === 'user-list' && window.authManager && window.authManager.isAuthenticated()) {
                loadAllUsers();
            } else if (tabName === 'role-management' && window.authManager && window.authManager.isAuthenticated()) {
                loadRoleStats();
            }
        }

        // Overview Functions
        async function loadUserStats() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/users/admin/stats');

                if (response.success) {
                    displayUserStats(response.data);
                    showResponse('overviewResponse', response, 'success');
                } else {
                    showResponse('overviewResponse', response, 'error');
                }
            } catch (error) {
                // Simulate user stats if endpoint not available
                const mockStats = {
                    totalUsers: 1247,
                    activeUsers: 1156,
                    inactiveUsers: 91,
                    adminUsers: 5,
                    moderatorUsers: 23,
                    regularUsers: 1219,
                    newUsersToday: 12,
                    newUsersThisWeek: 89,
                    mfaEnabledUsers: 456,
                    suspendedUsers: 3
                };
                displayUserStats(mockStats);
                showResponse('overviewResponse', {
                    success: true,
                    data: mockStats,
                    message: 'Mock data displayed (endpoint may not be available)'
                }, 'warning');
            }
        }

        function displayUserStats(stats) {
            const statsGrid = document.getElementById('userStatsGrid');

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">${stats.totalUsers || 0}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-value">${stats.activeUsers || 0}</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-value">${stats.inactiveUsers || 0}</div>
                    <div class="stat-label">Inactive Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-value">${stats.adminUsers || 0}</div>
                    <div class="stat-label">Admin Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-value">${stats.newUsersToday || 0}</div>
                    <div class="stat-label">New Today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="stat-value">${stats.mfaEnabledUsers || 0}</div>
                    <div class="stat-label">MFA Enabled</div>
                </div>
            `;
        }

        function generateUserReport() {
            window.notificationManager.info('Generating user report...');
            // This would typically generate and download a user report
            setTimeout(() => {
                window.notificationManager.success('User report generated successfully');
            }, 2000);
        }

        function clearOverviewDisplay() {
            document.getElementById('userStatsGrid').innerHTML = '';
            hideResponse('overviewResponse');
        }

        // User List Functions
        async function loadAllUsers() {
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.request('GET', '/users');

                if (response.success) {
                    currentUsers = response.data.users || response.data || [];
                    filteredUsers = [...currentUsers];
                    displayUserTable();
                    showResponse('userListResponse', response, 'success');
                } else {
                    showResponse('userListResponse', response, 'error');
                }
            } catch (error) {
                // Generate mock users if endpoint not available
                currentUsers = generateMockUsers();
                filteredUsers = [...currentUsers];
                displayUserTable();
                showResponse('userListResponse', {
                    success: true,
                    data: { users: currentUsers },
                    message: 'Mock data displayed (endpoint may not be available)'
                }, 'warning');
            }
        }

        function generateMockUsers() {
            const roles = ['USER', 'ADMIN', 'MODERATOR'];
            const statuses = ['active', 'inactive', 'suspended'];
            const users = [];

            for (let i = 1; i <= 50; i++) {
                users.push({
                    id: `user-${i}`,
                    email: `user${i}@example.com`,
                    name: `User ${i}`,
                    role: roles[Math.floor(Math.random() * roles.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
                    lastLogin: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : null,
                    mfaEnabled: Math.random() > 0.6
                });
            }

            return users;
        }

        function displayUserTable() {
            const tableBody = document.getElementById('userTableBody');
            const startIndex = (currentPage - 1) * usersPerPage;
            const endIndex = startIndex + usersPerPage;
            const pageUsers = filteredUsers.slice(startIndex, endIndex);

            const usersHTML = pageUsers.map(user => `
                <tr>
                    <td>
                        <input type="checkbox" class="user-checkbox" value="${user.id}" onchange="updateSelectedUsers()">
                    </td>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">
                                ${user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                            </div>
                            <div class="user-details">
                                <div class="user-name">${user.name || 'No Name'}</div>
                                <div class="user-email">${user.email}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="role-badge ${user.role.toLowerCase()}">${user.role}</span>
                    </td>
                    <td>
                        <span class="status-badge ${user.status}">${user.status}</span>
                    </td>
                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                    <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-primary" onclick="editUser('${user.id}')" title="Edit User">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="changeUserRole('${user.id}')" title="Change Role">
                                <i class="fas fa-user-shield"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')" title="Delete User">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            tableBody.innerHTML = usersHTML;
            updatePagination();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
            const pagination = document.getElementById('userPagination');

            let paginationHTML = '';

            // Previous button
            paginationHTML += `<button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>`;

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `<button onclick="changePage(${i})" ${i === currentPage ? 'class="current-page"' : ''}>${i}</button>`;
            }

            // Next button
            paginationHTML += `<button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>`;

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                displayUserTable();
            }
        }

        function changePageSize() {
            usersPerPage = parseInt(document.getElementById('usersPerPage').value);
            currentPage = 1;
            displayUserTable();
        }

        function filterUsers() {
            const roleFilter = document.getElementById('userRoleFilter').value;
            const statusFilter = document.getElementById('userStatusFilter').value;

            filteredUsers = currentUsers.filter(user => {
                const roleMatch = !roleFilter || user.role === roleFilter;
                const statusMatch = !statusFilter || user.status === statusFilter;
                return roleMatch && statusMatch;
            });

            currentPage = 1;
            displayUserTable();
        }

        function searchUsers() {
            const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();

            if (!searchTerm) {
                filterUsers();
                return;
            }

            filteredUsers = currentUsers.filter(user => {
                const nameMatch = user.name && user.name.toLowerCase().includes(searchTerm);
                const emailMatch = user.email.toLowerCase().includes(searchTerm);
                return nameMatch || emailMatch;
            });

            currentPage = 1;
            displayUserTable();
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAllUsers').checked;
            const checkboxes = document.querySelectorAll('.user-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll;
            });

            updateSelectedUsers();
        }

        function updateSelectedUsers() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            selectedUsers = Array.from(checkboxes).map(cb => cb.value);

            // Update select all checkbox
            const selectAll = document.getElementById('selectAllUsers');
            const allCheckboxes = document.querySelectorAll('.user-checkbox');
            selectAll.checked = selectedUsers.length === allCheckboxes.length;
            selectAll.indeterminate = selectedUsers.length > 0 && selectedUsers.length < allCheckboxes.length;
        }

        // User Modal Functions
        function openCreateUserModal() {
            currentEditingUser = null;
            document.getElementById('modalTitle').textContent = 'Create User';
            document.getElementById('modalSubmitBtn').textContent = 'Create User';
            document.getElementById('userForm').reset();
            document.getElementById('passwordGroup').style.display = 'block';
            document.getElementById('userModal').style.display = 'flex';
        }

        function editUser(userId) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            currentEditingUser = user;
            document.getElementById('modalTitle').textContent = 'Edit User';
            document.getElementById('modalSubmitBtn').textContent = 'Update User';
            document.getElementById('modalUserEmail').value = user.email;
            document.getElementById('modalUserName').value = user.name || '';
            document.getElementById('modalUserRole').value = user.role;
            document.getElementById('passwordGroup').style.display = 'none';
            document.getElementById('userModal').style.display = 'flex';
        }

        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            currentEditingUser = null;
        }

        // Handle form submission
        document.getElementById('userForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                email: document.getElementById('modalUserEmail').value,
                name: document.getElementById('modalUserName').value,
                role: document.getElementById('modalUserRole').value
            };

            if (!currentEditingUser) {
                formData.password = document.getElementById('modalUserPassword').value;
            }

            try {
                let response;
                if (currentEditingUser) {
                    // Update user
                    response = await window.apiClient.request('PUT', `/users/${currentEditingUser.id}`, formData);
                } else {
                    // Create user
                    response = await window.apiClient.request('POST', '/auth/register', formData);
                }

                if (response.success) {
                    window.notificationManager.success(currentEditingUser ? 'User updated successfully' : 'User created successfully');
                    closeUserModal();
                    await loadAllUsers();
                } else {
                    window.notificationManager.error(response.error || 'Operation failed');
                }
            } catch (error) {
                window.notificationManager.error('Operation failed: ' + error.message);
            }
        });

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user?')) return;

            try {
                const response = await window.apiClient.request('DELETE', `/users/${userId}`);

                if (response.success) {
                    window.notificationManager.success('User deleted successfully');
                    await loadAllUsers();
                } else {
                    window.notificationManager.error(response.error || 'Failed to delete user');
                }
            } catch (error) {
                window.notificationManager.error('Failed to delete user: ' + error.message);
            }
        }

        function exportUserList() {
            window.notificationManager.info('Exporting user list...');
            // This would export the user list to CSV
            setTimeout(() => {
                window.notificationManager.success('User list exported successfully');
            }, 2000);
        }

        function clearUserList() {
            document.getElementById('userTableBody').innerHTML = '';
            document.getElementById('userPagination').innerHTML = '';
            hideResponse('userListResponse');
        }

        // Role Management Functions
        async function loadRoleStats() {
            // Simulate role statistics
            const roleStats = {
                adminCount: 5,
                moderatorCount: 23,
                userCount: 1219,
                totalPermissions: 15,
                activeRoles: 3
            };

            displayRoleStats(roleStats);
        }

        function displayRoleStats(stats) {
            const statsGrid = document.getElementById('roleStatsGrid');

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-value">${stats.adminCount}</div>
                    <div class="stat-label">Admin Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="stat-value">${stats.moderatorCount}</div>
                    <div class="stat-label">Moderators</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">${stats.userCount}</div>
                    <div class="stat-label">Regular Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="stat-value">${stats.totalPermissions}</div>
                    <div class="stat-label">Total Permissions</div>
                </div>
            `;
        }

        async function changeUserRole() {
            const userIdentifier = document.getElementById('roleChangeUserId').value.trim();
            const newRole = document.getElementById('newUserRole').value;

            if (!userIdentifier) {
                window.notificationManager.error('Please enter a user ID or email');
                return;
            }

            try {
                // Try to find user by email first, then by ID
                let userId = userIdentifier;
                if (userIdentifier.includes('@')) {
                    const user = currentUsers.find(u => u.email === userIdentifier);
                    if (user) {
                        userId = user.id;
                    }
                }

                const response = await window.apiClient.request('PATCH', `/users/${userId}/role`, { role: newRole });

                if (response.success) {
                    window.notificationManager.success(`User role changed to ${newRole} successfully`);
                    document.getElementById('roleChangeUserId').value = '';
                    await loadAllUsers();
                    await loadRoleStats();
                } else {
                    window.notificationManager.error(response.error || 'Failed to change user role');
                }
            } catch (error) {
                window.notificationManager.error('Failed to change user role: ' + error.message);
            }
        }

        function testRolePermissions() {
            window.notificationManager.info('Testing role permissions...');
            // This would test various role permissions
            setTimeout(() => {
                window.notificationManager.success('Role permissions test completed');
            }, 2000);
        }

        function clearRoleDisplay() {
            document.getElementById('roleStatsGrid').innerHTML = '';
            hideResponse('roleManagementResponse');
        }

        // Advanced Search Functions
        async function performAdvancedSearch() {
            const searchParams = {
                email: document.getElementById('searchEmail').value,
                name: document.getElementById('searchName').value,
                role: document.getElementById('searchRole').value,
                dateFrom: document.getElementById('searchDateFrom').value,
                dateTo: document.getElementById('searchDateTo').value,
                mfaEnabled: document.getElementById('searchMfaEnabled').value
            };

            // Filter out empty parameters
            const filteredParams = Object.fromEntries(
                Object.entries(searchParams).filter(([key, value]) => value !== '')
            );

            try {
                // This would call the search endpoint with parameters
                const response = await window.apiClient.request('GET', '/users/search', filteredParams);

                if (response.success) {
                    displaySearchResults(response.data.users || []);
                    showResponse('userSearchResponse', response, 'success');
                } else {
                    showResponse('userSearchResponse', response, 'error');
                }
            } catch (error) {
                // Simulate search results
                const mockResults = currentUsers.filter(user => {
                    let matches = true;

                    if (filteredParams.email && !user.email.toLowerCase().includes(filteredParams.email.toLowerCase())) {
                        matches = false;
                    }
                    if (filteredParams.name && user.name && !user.name.toLowerCase().includes(filteredParams.name.toLowerCase())) {
                        matches = false;
                    }
                    if (filteredParams.role && user.role !== filteredParams.role) {
                        matches = false;
                    }
                    if (filteredParams.mfaEnabled && user.mfaEnabled.toString() !== filteredParams.mfaEnabled) {
                        matches = false;
                    }

                    return matches;
                });

                displaySearchResults(mockResults);
                showResponse('userSearchResponse', {
                    success: true,
                    data: { users: mockResults },
                    message: 'Mock search results (endpoint may not be available)'
                }, 'warning');
            }
        }

        function displaySearchResults(users) {
            const resultsContainer = document.getElementById('searchResults');

            if (users.length === 0) {
                resultsContainer.innerHTML = '<p>No users found matching the search criteria.</p>';
                return;
            }

            const resultsHTML = `
                <h4>Search Results (${users.length} users found)</h4>
                <div class="table-container">
                    <table class="user-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>MFA</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">
                                                ${user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                                            </div>
                                            <div class="user-details">
                                                <div class="user-name">${user.name || 'No Name'}</div>
                                                <div class="user-email">${user.email}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="role-badge ${user.role.toLowerCase()}">${user.role}</span></td>
                                    <td><span class="status-badge ${user.status}">${user.status}</span></td>
                                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                                    <td>${user.mfaEnabled ? '✅' : '❌'}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editUser('${user.id}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            resultsContainer.innerHTML = resultsHTML;
        }

        function saveSearchQuery() {
            window.notificationManager.info('Saving search query...');
            // This would save the current search parameters
            setTimeout(() => {
                window.notificationManager.success('Search query saved successfully');
            }, 1000);
        }

        function clearSearchResults() {
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('userSearchResponse').style.display = 'none';

            // Clear form fields
            document.getElementById('searchEmail').value = '';
            document.getElementById('searchName').value = '';
            document.getElementById('searchRole').value = '';
            document.getElementById('searchDateFrom').value = '';
            document.getElementById('searchDateTo').value = '';
            document.getElementById('searchMfaEnabled').value = '';
        }

        // Bulk Actions Functions
        function loadSelectedUsers() {
            if (selectedUsers.length === 0) {
                window.notificationManager.warning('No users selected');
                return;
            }

            const selectedUserData = currentUsers.filter(user => selectedUsers.includes(user.id));
            window.notificationManager.info(`Loaded ${selectedUserData.length} selected users`);
        }

        function updateUserSelection() {
            const method = document.getElementById('userSelectionMethod').value;
            const container = document.getElementById('userSelectionContainer');

            switch (method) {
                case 'manual':
                    container.innerHTML = `
                        <div class="form-group">
                            <label>Selected Users: ${selectedUsers.length}</label>
                            <p>Use the User List tab to select users manually.</p>
                        </div>
                    `;
                    break;
                case 'criteria':
                    container.innerHTML = `
                        <div class="form-group">
                            <label for="criteriaRole">Role:</label>
                            <select id="criteriaRole" class="form-control">
                                <option value="">Any Role</option>
                                <option value="USER">User</option>
                                <option value="MODERATOR">Moderator</option>
                                <option value="ADMIN">Admin</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="criteriaStatus">Status:</label>
                            <select id="criteriaStatus" class="form-control">
                                <option value="">Any Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'upload':
                    container.innerHTML = `
                        <div class="form-group">
                            <label for="userListFile">Upload User List (CSV):</label>
                            <input type="file" id="userListFile" class="form-control" accept=".csv">
                        </div>
                    `;
                    break;
            }
        }

        function performBulkAction() {
            const action = document.getElementById('bulkAction').value;
            const method = document.getElementById('userSelectionMethod').value;

            let targetUsers = [];

            if (method === 'manual') {
                targetUsers = selectedUsers;
            } else if (method === 'criteria') {
                const role = document.getElementById('criteriaRole').value;
                const status = document.getElementById('criteriaStatus').value;

                targetUsers = currentUsers.filter(user => {
                    const roleMatch = !role || user.role === role;
                    const statusMatch = !status || user.status === status;
                    return roleMatch && statusMatch;
                }).map(user => user.id);
            }

            if (targetUsers.length === 0) {
                window.notificationManager.warning('No users selected for bulk action');
                return;
            }

            if (!confirm(`Are you sure you want to perform "${action}" on ${targetUsers.length} users?`)) {
                return;
            }

            window.notificationManager.info(`Performing ${action} on ${targetUsers.length} users...`);

            // Simulate bulk action
            setTimeout(() => {
                window.notificationManager.success(`Bulk action "${action}" completed successfully`);
                displayBulkActionResults(action, targetUsers.length);
            }, 2000);
        }

        function displayBulkActionResults(action, userCount) {
            const resultsContainer = document.getElementById('bulkActionResults');

            resultsContainer.innerHTML = `
                <div class="form-section">
                    <h4>Bulk Action Results</h4>
                    <p><strong>Action:</strong> ${action}</p>
                    <p><strong>Users Affected:</strong> ${userCount}</p>
                    <p><strong>Status:</strong> <span style="color: var(--success-color);">Completed Successfully</span></p>
                    <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                </div>
            `;
        }

        function clearBulkActions() {
            document.getElementById('bulkActionResults').innerHTML = '';
            document.getElementById('userSelectionContainer').innerHTML = '';
            hideResponse('bulkActionsResponse');
        }

        // Shared utility functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }

        function hideResponse(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'none';
        }

        // Initialize bulk action parameters when action changes
        document.getElementById('bulkAction').addEventListener('change', function() {
            const action = this.value;
            const paramsContainer = document.getElementById('bulkActionParams');

            switch (action) {
                case 'change-role':
                    paramsContainer.innerHTML = `
                        <label for="bulkNewRole">New Role:</label>
                        <select id="bulkNewRole" class="form-control">
                            <option value="USER">User</option>
                            <option value="MODERATOR">Moderator</option>
                            <option value="ADMIN">Admin</option>
                        </select>
                    `;
                    break;
                case 'send-email':
                    paramsContainer.innerHTML = `
                        <label for="emailSubject">Email Subject:</label>
                        <input type="text" id="emailSubject" class="form-control" placeholder="Enter email subject">
                        <label for="emailMessage">Email Message:</label>
                        <textarea id="emailMessage" class="form-control" rows="4" placeholder="Enter email message"></textarea>
                    `;
                    break;
                default:
                    paramsContainer.innerHTML = '';
            }
        });

        // Initialize user selection method
        updateUserSelection();
    </script>
</body>
</html>
