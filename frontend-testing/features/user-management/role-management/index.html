<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .roles-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .roles-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .stat-card.roles::before {
            background: var(--primary-color);
        }

        .stat-card.permissions::before {
            background: var(--success-color);
        }

        .stat-card.users::before {
            background: var(--warning-color);
        }

        .stat-card.custom::before {
            background: var(--info-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .role-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .role-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .role-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .role-info {
            flex: 1;
        }

        .role-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .role-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .role-badge {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin-left: 1rem;
        }

        .role-badge.system {
            background: var(--info-color);
            color: white;
        }

        .role-badge.custom {
            background: var(--success-color);
            color: white;
        }

        .role-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .role-stat {
            text-align: center;
        }

        .role-stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .role-stat-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .permissions-list {
            margin-bottom: 1rem;
        }

        .permissions-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .permission-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .permission-tag {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-secondary);
        }

        .permission-tag.read {
            border-color: var(--info-color);
            color: var(--info-color);
        }

        .permission-tag.write {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .permission-tag.admin {
            border-color: var(--error-color);
            color: var(--error-color);
        }

        .role-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: var(--info-color);
            color: white;
        }

        .action-btn.edit {
            background: var(--warning-color);
            color: white;
        }

        .action-btn.delete {
            background: var(--error-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .role-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            max-width: 700px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .permission-group {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
        }

        .permission-group-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .permission-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .permission-checkbox input {
            width: 16px;
            height: 16px;
        }

        .permission-checkbox label {
            font-size: 0.9rem;
            color: var(--text-primary);
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .roles-grid {
                grid-template-columns: 1fr;
            }

            .role-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user-shield"></i>
                    <h1>Role Management Testing</h1>
                    <span class="subtitle">Role and Permission Management Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to User Management
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="roles-container">
                <!-- Roles Overview -->
                <div class="roles-overview">
                    <h2>Role Management Overview</h2>
                    <div class="overview-stats">
                        <div class="stat-card roles">
                            <div class="stat-number" id="totalRoles">0</div>
                            <div class="stat-label">Total Roles</div>
                        </div>
                        <div class="stat-card permissions">
                            <div class="stat-number" id="totalPermissions">0</div>
                            <div class="stat-label">Total Permissions</div>
                        </div>
                        <div class="stat-card users">
                            <div class="stat-number" id="assignedUsers">0</div>
                            <div class="stat-label">Users with Roles</div>
                        </div>
                        <div class="stat-card custom">
                            <div class="stat-number" id="customRoles">0</div>
                            <div class="stat-label">Custom Roles</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="refreshRoles()">
                        <i class="fas fa-sync"></i>
                        Refresh Roles
                    </button>
                    <button class="btn btn-success" onclick="createRole()">
                        <i class="fas fa-plus"></i>
                        Create Role
                    </button>
                    <button class="btn btn-info" onclick="managePermissions()">
                        <i class="fas fa-key"></i>
                        Manage Permissions
                    </button>
                    <button class="btn btn-warning" onclick="exportRoles()">
                        <i class="fas fa-download"></i>
                        Export Roles
                    </button>
                </div>

                <!-- Roles Grid -->
                <div class="roles-grid" id="rolesGrid">
                    <!-- Role cards will be populated here -->
                </div>

                <!-- Role Modal -->
                <div class="role-modal" id="roleModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title" id="modalTitle">Role Details</div>
                            <button class="close-btn" onclick="closeRoleModal()">&times;</button>
                        </div>
                        <form id="roleForm">
                            <div class="form-group">
                                <label class="form-label">Role Name</label>
                                <input type="text" class="form-input" id="roleName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Description</label>
                                <textarea class="form-input form-textarea" id="roleDescription" required></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Role Type</label>
                                <select class="form-input" id="roleType" required>
                                    <option value="custom">Custom Role</option>
                                    <option value="system">System Role</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Permissions</label>
                                <div class="permissions-grid" id="permissionsGrid">
                                    <!-- Permission groups will be populated here -->
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Role
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeRoleModal()">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="rolesResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Role Management Testing Interface</p>
                    <p>Role and permission management testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let rolesData = {
            roles: [],
            permissions: [],
            currentRole: null,
            editMode: false
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load roles and permissions data
            await loadRoles();
            await loadPermissions();

            // Initialize form handler
            initializeFormHandler();
        }

        // Role Management Functions
        async function loadRoles() {
            window.notificationManager.info('Loading roles...');

            try {
                const response = await window.apiClient.request('GET', '/admin/roles');

                if (response.success) {
                    rolesData.roles = response.data.roles || [];
                    updateRolesDisplay();
                    window.notificationManager.success('Roles loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load roles');
                }

                showResponse('rolesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock roles data
                loadMockRoles();
                window.notificationManager.success('Roles loaded successfully (simulated)');

                showResponse('rolesResponse', {
                    success: true,
                    data: { roles: rolesData.roles },
                    message: 'Mock roles data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockRoles() {
            const mockRoles = [
                {
                    id: 'role_admin',
                    name: 'Administrator',
                    description: 'Full system access with all permissions',
                    type: 'system',
                    userCount: 3,
                    permissions: ['users.read', 'users.write', 'users.delete', 'roles.read', 'roles.write', 'roles.delete', 'system.admin'],
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 90), // 90 days ago
                    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30) // 30 days ago
                },
                {
                    id: 'role_moderator',
                    name: 'Moderator',
                    description: 'User management and content moderation permissions',
                    type: 'system',
                    userCount: 8,
                    permissions: ['users.read', 'users.write', 'content.read', 'content.write', 'content.moderate'],
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 75), // 75 days ago
                    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15) // 15 days ago
                },
                {
                    id: 'role_user',
                    name: 'User',
                    description: 'Basic user permissions for standard functionality',
                    type: 'system',
                    userCount: 156,
                    permissions: ['profile.read', 'profile.write', 'content.read'],
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 90), // 90 days ago
                    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60) // 60 days ago
                },
                {
                    id: 'role_analyst',
                    name: 'Data Analyst',
                    description: 'Read-only access to analytics and reporting data',
                    type: 'custom',
                    userCount: 12,
                    permissions: ['analytics.read', 'reports.read', 'users.read'],
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
                    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) // 7 days ago
                },
                {
                    id: 'role_support',
                    name: 'Support Agent',
                    description: 'Customer support and ticket management permissions',
                    type: 'custom',
                    userCount: 25,
                    permissions: ['tickets.read', 'tickets.write', 'users.read', 'support.access'],
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45), // 45 days ago
                    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3) // 3 days ago
                }
            ];

            rolesData.roles = mockRoles;
            updateRolesDisplay();
        }

        async function loadPermissions() {
            try {
                const response = await window.apiClient.request('GET', '/admin/permissions');

                if (response.success) {
                    rolesData.permissions = response.data.permissions || [];
                } else {
                    loadMockPermissions();
                }
            } catch (error) {
                loadMockPermissions();
            }
        }

        function loadMockPermissions() {
            const mockPermissions = [
                // User Management
                { id: 'users.read', name: 'View Users', category: 'User Management', type: 'read' },
                { id: 'users.write', name: 'Edit Users', category: 'User Management', type: 'write' },
                { id: 'users.delete', name: 'Delete Users', category: 'User Management', type: 'admin' },

                // Role Management
                { id: 'roles.read', name: 'View Roles', category: 'Role Management', type: 'read' },
                { id: 'roles.write', name: 'Edit Roles', category: 'Role Management', type: 'write' },
                { id: 'roles.delete', name: 'Delete Roles', category: 'Role Management', type: 'admin' },

                // Content Management
                { id: 'content.read', name: 'View Content', category: 'Content Management', type: 'read' },
                { id: 'content.write', name: 'Edit Content', category: 'Content Management', type: 'write' },
                { id: 'content.moderate', name: 'Moderate Content', category: 'Content Management', type: 'admin' },

                // Analytics
                { id: 'analytics.read', name: 'View Analytics', category: 'Analytics', type: 'read' },
                { id: 'reports.read', name: 'View Reports', category: 'Analytics', type: 'read' },

                // Support
                { id: 'tickets.read', name: 'View Tickets', category: 'Support', type: 'read' },
                { id: 'tickets.write', name: 'Manage Tickets', category: 'Support', type: 'write' },
                { id: 'support.access', name: 'Support Access', category: 'Support', type: 'read' },

                // Profile
                { id: 'profile.read', name: 'View Profile', category: 'Profile', type: 'read' },
                { id: 'profile.write', name: 'Edit Profile', category: 'Profile', type: 'write' },

                // System
                { id: 'system.admin', name: 'System Administration', category: 'System', type: 'admin' }
            ];

            rolesData.permissions = mockPermissions;
        }

        function updateRolesDisplay() {
            updateRoleStats();
            renderRolesGrid();
        }

        function updateRoleStats() {
            const stats = {
                totalRoles: rolesData.roles.length,
                totalPermissions: rolesData.permissions.length,
                assignedUsers: rolesData.roles.reduce((sum, role) => sum + role.userCount, 0),
                customRoles: rolesData.roles.filter(role => role.type === 'custom').length
            };

            document.getElementById('totalRoles').textContent = stats.totalRoles;
            document.getElementById('totalPermissions').textContent = stats.totalPermissions;
            document.getElementById('assignedUsers').textContent = stats.assignedUsers;
            document.getElementById('customRoles').textContent = stats.customRoles;
        }

        function renderRolesGrid() {
            const grid = document.getElementById('rolesGrid');

            if (rolesData.roles.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No roles found. Create your first role to get started.
                    </div>
                `;
                return;
            }

            const rolesHTML = rolesData.roles.map(role => `
                <div class="role-card">
                    <div class="role-header">
                        <div class="role-info">
                            <div class="role-name">${role.name}</div>
                            <div class="role-description">${role.description}</div>
                        </div>
                        <div class="role-badge ${role.type}">${role.type.toUpperCase()}</div>
                    </div>

                    <div class="role-stats">
                        <div class="role-stat">
                            <div class="role-stat-number">${role.userCount}</div>
                            <div class="role-stat-label">Users</div>
                        </div>
                        <div class="role-stat">
                            <div class="role-stat-number">${role.permissions.length}</div>
                            <div class="role-stat-label">Permissions</div>
                        </div>
                    </div>

                    <div class="permissions-list">
                        <div class="permissions-title">Permissions:</div>
                        <div class="permission-tags">
                            ${role.permissions.slice(0, 4).map(permId => {
                                const perm = rolesData.permissions.find(p => p.id === permId);
                                return `<div class="permission-tag ${perm ? perm.type : 'read'}">${perm ? perm.name : permId}</div>`;
                            }).join('')}
                            ${role.permissions.length > 4 ? `<div class="permission-tag">+${role.permissions.length - 4} more</div>` : ''}
                        </div>
                    </div>

                    <div class="role-actions">
                        <button class="action-btn view" onclick="viewRole('${role.id}')" title="View Role">
                            <i class="fas fa-eye"></i>
                            View
                        </button>
                        <button class="action-btn edit" onclick="editRole('${role.id}')" title="Edit Role">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                        <button class="action-btn delete" onclick="deleteRole('${role.id}')" title="Delete Role" ${role.type === 'system' ? 'disabled' : ''}>
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');

            grid.innerHTML = rolesHTML;
        }

        // Role Actions
        async function refreshRoles() {
            await loadRoles();
        }

        function createRole() {
            rolesData.currentRole = null;
            rolesData.editMode = false;

            document.getElementById('modalTitle').textContent = 'Create New Role';
            document.getElementById('roleForm').reset();
            populatePermissionsGrid();
            document.getElementById('roleModal').style.display = 'flex';
        }

        function viewRole(roleId) {
            const role = rolesData.roles.find(r => r.id === roleId);
            if (!role) return;

            rolesData.currentRole = role;
            rolesData.editMode = false;

            document.getElementById('modalTitle').textContent = 'View Role Details';
            populateRoleForm(role);
            disableFormInputs(true);
            document.getElementById('roleModal').style.display = 'flex';
        }

        function editRole(roleId) {
            const role = rolesData.roles.find(r => r.id === roleId);
            if (!role) return;

            rolesData.currentRole = role;
            rolesData.editMode = true;

            document.getElementById('modalTitle').textContent = 'Edit Role';
            populateRoleForm(role);
            disableFormInputs(false);
            document.getElementById('roleModal').style.display = 'flex';
        }

        function populateRoleForm(role) {
            document.getElementById('roleName').value = role.name;
            document.getElementById('roleDescription').value = role.description;
            document.getElementById('roleType').value = role.type;
            populatePermissionsGrid(role.permissions);
        }

        function populatePermissionsGrid(selectedPermissions = []) {
            const grid = document.getElementById('permissionsGrid');

            // Group permissions by category
            const categories = {};
            rolesData.permissions.forEach(perm => {
                if (!categories[perm.category]) {
                    categories[perm.category] = [];
                }
                categories[perm.category].push(perm);
            });

            const categoriesHTML = Object.keys(categories).map(category => `
                <div class="permission-group">
                    <div class="permission-group-title">${category}</div>
                    ${categories[category].map(perm => `
                        <div class="permission-checkbox">
                            <input type="checkbox" id="perm_${perm.id}" value="${perm.id}"
                                   ${selectedPermissions.includes(perm.id) ? 'checked' : ''}>
                            <label for="perm_${perm.id}">${perm.name}</label>
                        </div>
                    `).join('')}
                </div>
            `).join('');

            grid.innerHTML = categoriesHTML;
        }

        function disableFormInputs(disabled) {
            const inputs = document.querySelectorAll('#roleForm input, #roleForm select, #roleForm textarea');
            inputs.forEach(input => {
                input.disabled = disabled;
            });

            const submitBtn = document.querySelector('#roleForm button[type="submit"]');
            submitBtn.style.display = disabled ? 'none' : 'inline-block';
        }

        function closeRoleModal() {
            document.getElementById('roleModal').style.display = 'none';
            rolesData.currentRole = null;
            rolesData.editMode = false;
        }

        function initializeFormHandler() {
            document.getElementById('roleForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    name: document.getElementById('roleName').value,
                    description: document.getElementById('roleDescription').value,
                    type: document.getElementById('roleType').value,
                    permissions: Array.from(document.querySelectorAll('#permissionsGrid input[type="checkbox"]:checked'))
                        .map(cb => cb.value)
                };

                if (rolesData.editMode && rolesData.currentRole) {
                    await updateRole(rolesData.currentRole.id, formData);
                } else {
                    await createNewRole(formData);
                }
            });
        }

        async function createNewRole(roleData) {
            window.notificationManager.info('Creating new role...');

            try {
                const response = await window.apiClient.request('POST', '/admin/roles', roleData);

                if (response.success) {
                    const newRole = {
                        id: 'role_' + Date.now(),
                        ...roleData,
                        userCount: 0,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };

                    rolesData.roles.push(newRole);
                    updateRolesDisplay();
                    closeRoleModal();
                    window.notificationManager.success('Role created successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to create role');
                }

                showResponse('rolesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate role creation
                const newRole = {
                    id: 'role_' + Date.now(),
                    ...roleData,
                    userCount: 0,
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                rolesData.roles.push(newRole);
                updateRolesDisplay();
                closeRoleModal();
                window.notificationManager.success('Role created successfully (simulated)');

                showResponse('rolesResponse', {
                    success: true,
                    data: newRole,
                    message: 'Mock role creation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function updateRole(roleId, roleData) {
            window.notificationManager.info('Updating role...');

            try {
                const response = await window.apiClient.request('PUT', `/admin/roles/${roleId}`, roleData);

                if (response.success) {
                    const roleIndex = rolesData.roles.findIndex(r => r.id === roleId);
                    if (roleIndex !== -1) {
                        rolesData.roles[roleIndex] = {
                            ...rolesData.roles[roleIndex],
                            ...roleData,
                            updatedAt: new Date()
                        };
                        updateRolesDisplay();
                        closeRoleModal();
                        window.notificationManager.success('Role updated successfully');
                    }
                } else {
                    window.notificationManager.error(response.error || 'Failed to update role');
                }

                showResponse('rolesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate role update
                const roleIndex = rolesData.roles.findIndex(r => r.id === roleId);
                if (roleIndex !== -1) {
                    rolesData.roles[roleIndex] = {
                        ...rolesData.roles[roleIndex],
                        ...roleData,
                        updatedAt: new Date()
                    };
                    updateRolesDisplay();
                    closeRoleModal();
                    window.notificationManager.success('Role updated successfully (simulated)');

                    showResponse('rolesResponse', {
                        success: true,
                        data: roleData,
                        message: 'Mock role update successful (endpoint may not be available)'
                    }, 'warning');
                }
            }
        }

        async function deleteRole(roleId) {
            const role = rolesData.roles.find(r => r.id === roleId);
            if (!role) return;

            if (role.type === 'system') {
                window.notificationManager.error('System roles cannot be deleted');
                return;
            }

            if (!confirm(`Are you sure you want to delete the role "${role.name}"? This action cannot be undone and will affect ${role.userCount} users.`)) {
                return;
            }

            window.notificationManager.warning(`Deleting role: ${role.name}`);

            try {
                const response = await window.apiClient.request('DELETE', `/admin/roles/${roleId}`);

                if (response.success) {
                    rolesData.roles = rolesData.roles.filter(r => r.id !== roleId);
                    updateRolesDisplay();
                    window.notificationManager.success('Role deleted successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to delete role');
                }

                showResponse('rolesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate role deletion
                rolesData.roles = rolesData.roles.filter(r => r.id !== roleId);
                updateRolesDisplay();
                window.notificationManager.success('Role deleted successfully (simulated)');

                showResponse('rolesResponse', {
                    success: true,
                    message: 'Mock role deletion successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Additional Actions
        function managePermissions() {
            window.notificationManager.info('Permission management interface would be implemented here');

            setTimeout(() => {
                window.notificationManager.success('Permission management would allow creating and editing individual permissions');
            }, 1000);
        }

        function exportRoles() {
            window.notificationManager.info('Exporting roles data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                totalRoles: rolesData.roles.length,
                roles: rolesData.roles.map(role => ({
                    ...role,
                    createdAt: role.createdAt.toISOString(),
                    updatedAt: role.updatedAt.toISOString()
                })),
                permissions: rolesData.permissions
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `roles-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Roles data exported successfully');
        }

        // Close modal when clicking outside
        document.getElementById('roleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRoleModal();
            }
        });

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
