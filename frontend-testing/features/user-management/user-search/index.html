<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Search Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .search-header {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .search-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .search-subtitle {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .search-box-container {
            position: relative;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-box {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1.1rem;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.2rem;
        }

        .search-filters {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filters-title {
            font-weight: bold;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .filter-input {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .advanced-search {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }

        .advanced-search.active {
            display: block;
        }

        .advanced-toggle {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: underline;
        }

        .search-results {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .results-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .results-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .results-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .result-item:hover {
            background: var(--hover-background);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .result-info {
            flex: 1;
        }

        .result-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .result-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .result-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .result-role {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 1rem;
        }

        .result-role.admin {
            background: var(--error-color);
            color: white;
        }

        .result-role.moderator {
            background: var(--warning-color);
            color: white;
        }

        .result-role.user {
            background: var(--info-color);
            color: white;
        }

        .result-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: var(--info-color);
            color: white;
        }

        .action-btn.contact {
            background: var(--success-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .search-suggestions {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .suggestions-title {
            font-weight: bold;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-tag {
            padding: 0.5rem 1rem;
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .suggestion-tag:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-results-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .search-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }

            .result-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .result-actions {
                margin-left: 0;
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-search"></i>
                    <h1>User Search Testing</h1>
                    <span class="subtitle">Advanced User Search and Discovery Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to User Management
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="search-container">
                <!-- Search Header -->
                <div class="search-header">
                    <h2 class="search-title">User Search</h2>
                    <p class="search-subtitle">Find users by name, email, role, or other criteria</p>
                    <div class="search-box-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-box" id="mainSearch" placeholder="Search for users..." onkeyup="performSearch()" autocomplete="off">
                    </div>
                </div>

                <!-- Search Statistics -->
                <div class="search-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalSearchable">0</div>
                        <div class="stat-label">Searchable Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="recentSearches">0</div>
                        <div class="stat-label">Recent Searches</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeFilters">0</div>
                        <div class="stat-label">Active Filters</div>
                    </div>
                </div>

                <!-- Search Filters -->
                <div class="search-filters">
                    <div class="filters-title">Search Filters</div>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">Role</label>
                            <select class="filter-input" id="roleFilter" onchange="performSearch()">
                                <option value="">All Roles</option>
                                <option value="admin">Admin</option>
                                <option value="moderator">Moderator</option>
                                <option value="user">User</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Status</label>
                            <select class="filter-input" id="statusFilter" onchange="performSearch()">
                                <option value="">All Status</option>
                                <option value="online">Online</option>
                                <option value="offline">Offline</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Registration Date</label>
                            <select class="filter-input" id="dateFilter" onchange="performSearch()">
                                <option value="">Any Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="year">This Year</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Activity</label>
                            <select class="filter-input" id="activityFilter" onchange="performSearch()">
                                <option value="">Any Activity</option>
                                <option value="active">Recently Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <button class="advanced-toggle" onclick="toggleAdvancedSearch()">
                        <i class="fas fa-cog"></i>
                        Advanced Search Options
                    </button>
                    <div class="advanced-search" id="advancedSearch">
                        <div class="filters-grid">
                            <div class="filter-group">
                                <label class="filter-label">Email Domain</label>
                                <input type="text" class="filter-input" id="domainFilter" placeholder="e.g., example.com" onkeyup="performSearch()">
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">Login Count</label>
                                <select class="filter-input" id="loginCountFilter" onchange="performSearch()">
                                    <option value="">Any Count</option>
                                    <option value="high">High (>100)</option>
                                    <option value="medium">Medium (10-100)</option>
                                    <option value="low">Low (<10)</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">Location</label>
                                <input type="text" class="filter-input" id="locationFilter" placeholder="City, Country" onkeyup="performSearch()">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Suggestions -->
                <div class="search-suggestions">
                    <div class="suggestions-title">Popular Searches</div>
                    <div class="suggestion-tags">
                        <div class="suggestion-tag" onclick="quickSearch('admin')">Admin Users</div>
                        <div class="suggestion-tag" onclick="quickSearch('online')">Online Users</div>
                        <div class="suggestion-tag" onclick="quickSearch('new')">New Users</div>
                        <div class="suggestion-tag" onclick="quickSearch('active')">Active Users</div>
                        <div class="suggestion-tag" onclick="quickSearch('moderator')">Moderators</div>
                        <div class="suggestion-tag" onclick="quickSearch('inactive')">Inactive Users</div>
                    </div>
                </div>

                <!-- Search Results -->
                <div class="search-results">
                    <div class="results-header">
                        <div class="results-title">Search Results</div>
                        <div class="results-count" id="resultsCount">0 users found</div>
                    </div>
                    <div class="results-list" id="searchResults">
                        <div class="no-results">
                            <div class="no-results-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h3>Start searching to find users</h3>
                            <p>Enter a search term or use the filters above to find users</p>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="searchResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 User Search Testing Interface</p>
                    <p>Advanced user search and discovery testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let searchData = {
            users: [],
            searchResults: [],
            recentSearches: [],
            stats: {
                totalSearchable: 0,
                recentSearches: 0,
                activeFilters: 0
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await checkServerHealth();
            updateAuthStatus();
            initializeTheme();
            await loadSearchableUsers();
            loadRecentSearches();
            updateSearchStats();
        }

        // Search Data Functions
        async function loadSearchableUsers() {
            window.notificationManager.info('Loading searchable users...');

            try {
                const response = await window.apiClient.request('GET', '/users/search');

                if (response.success) {
                    searchData.users = response.data.users || [];
                    window.notificationManager.success('Users loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load users');
                }

                showResponse('searchResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                loadMockUsers();
                window.notificationManager.success('Users loaded successfully (simulated)');

                showResponse('searchResponse', {
                    success: true,
                    data: { users: searchData.users },
                    message: 'Mock users data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockUsers() {
            const mockUsers = [
                {
                    id: 'user_001',
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 30),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30),
                    loginCount: 127,
                    location: 'New York, USA',
                    avatar: 'JD'
                },
                {
                    id: 'user_002',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15),
                    loginCount: 89,
                    location: 'London, UK',
                    avatar: 'JS'
                },
                {
                    id: 'user_003',
                    firstName: 'Bob',
                    lastName: 'Wilson',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'offline',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
                    loginCount: 45,
                    location: 'Toronto, Canada',
                    avatar: 'BW'
                },
                {
                    id: 'user_004',
                    firstName: 'Alice',
                    lastName: 'Brown',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'offline',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60),
                    loginCount: 23,
                    location: 'Sydney, Australia',
                    avatar: 'AB'
                },
                {
                    id: 'user_005',
                    firstName: 'Charlie',
                    lastName: 'Davis',
                    email: '<EMAIL>',
                    role: 'moderator',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 6),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
                    loginCount: 67,
                    location: 'Berlin, Germany',
                    avatar: 'CD'
                },
                {
                    id: 'user_006',
                    firstName: 'Diana',
                    lastName: 'Miller',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'online',
                    lastLogin: new Date(Date.now() - 1000 * 60 * 15),
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
                    loginCount: 34,
                    location: 'Tokyo, Japan',
                    avatar: 'DM'
                }
            ];

            searchData.users = mockUsers;
            searchData.stats.totalSearchable = mockUsers.length;
        }

        function loadRecentSearches() {
            const saved = localStorage.getItem('recentSearches');
            if (saved) {
                searchData.recentSearches = JSON.parse(saved);
                searchData.stats.recentSearches = searchData.recentSearches.length;
            }
        }

        function saveRecentSearch(query) {
            if (query.trim()) {
                searchData.recentSearches = searchData.recentSearches.filter(s => s !== query);
                searchData.recentSearches.unshift(query);
                if (searchData.recentSearches.length > 10) {
                    searchData.recentSearches = searchData.recentSearches.slice(0, 10);
                }
                localStorage.setItem('recentSearches', JSON.stringify(searchData.recentSearches));
                searchData.stats.recentSearches = searchData.recentSearches.length;
                updateSearchStats();
            }
        }

        function updateSearchStats() {
            document.getElementById('totalSearchable').textContent = searchData.stats.totalSearchable;
            document.getElementById('recentSearches').textContent = searchData.stats.recentSearches;
            document.getElementById('activeFilters').textContent = searchData.stats.activeFilters;
        }

        // Search Functions
        function performSearch() {
            const query = document.getElementById('mainSearch').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const activityFilter = document.getElementById('activityFilter').value;
            const domainFilter = document.getElementById('domainFilter').value.toLowerCase();
            const loginCountFilter = document.getElementById('loginCountFilter').value;
            const locationFilter = document.getElementById('locationFilter').value.toLowerCase();

            // Count active filters
            let activeFilters = 0;
            if (roleFilter) activeFilters++;
            if (statusFilter) activeFilters++;
            if (dateFilter) activeFilters++;
            if (activityFilter) activeFilters++;
            if (domainFilter) activeFilters++;
            if (loginCountFilter) activeFilters++;
            if (locationFilter) activeFilters++;

            searchData.stats.activeFilters = activeFilters;
            updateSearchStats();

            // Filter users
            searchData.searchResults = searchData.users.filter(user => {
                // Text search
                const matchesQuery = !query ||
                    user.firstName.toLowerCase().includes(query) ||
                    user.lastName.toLowerCase().includes(query) ||
                    user.email.toLowerCase().includes(query) ||
                    user.role.toLowerCase().includes(query) ||
                    user.location.toLowerCase().includes(query);

                // Role filter
                const matchesRole = !roleFilter || user.role === roleFilter;

                // Status filter
                const matchesStatus = !statusFilter || user.status === statusFilter;

                // Date filter
                let matchesDate = true;
                if (dateFilter) {
                    const now = new Date();
                    const userDate = user.createdAt;
                    switch (dateFilter) {
                        case 'today':
                            matchesDate = userDate.toDateString() === now.toDateString();
                            break;
                        case 'week':
                            const weekAgo = new Date(now - 7 * 24 * 60 * 60 * 1000);
                            matchesDate = userDate >= weekAgo;
                            break;
                        case 'month':
                            const monthAgo = new Date(now - 30 * 24 * 60 * 60 * 1000);
                            matchesDate = userDate >= monthAgo;
                            break;
                        case 'year':
                            const yearAgo = new Date(now - 365 * 24 * 60 * 60 * 1000);
                            matchesDate = userDate >= yearAgo;
                            break;
                    }
                }

                // Activity filter
                let matchesActivity = true;
                if (activityFilter) {
                    const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                    if (activityFilter === 'active') {
                        matchesActivity = user.lastLogin >= dayAgo;
                    } else if (activityFilter === 'inactive') {
                        matchesActivity = user.lastLogin < dayAgo;
                    }
                }

                // Domain filter
                const matchesDomain = !domainFilter || user.email.toLowerCase().includes(domainFilter);

                // Login count filter
                let matchesLoginCount = true;
                if (loginCountFilter) {
                    switch (loginCountFilter) {
                        case 'high':
                            matchesLoginCount = user.loginCount > 100;
                            break;
                        case 'medium':
                            matchesLoginCount = user.loginCount >= 10 && user.loginCount <= 100;
                            break;
                        case 'low':
                            matchesLoginCount = user.loginCount < 10;
                            break;
                    }
                }

                // Location filter
                const matchesLocation = !locationFilter || user.location.toLowerCase().includes(locationFilter);

                return matchesQuery && matchesRole && matchesStatus && matchesDate &&
                       matchesActivity && matchesDomain && matchesLoginCount && matchesLocation;
            });

            // Save search query
            if (query) {
                saveRecentSearch(query);
            }

            displaySearchResults();
        }

        function displaySearchResults() {
            const resultsContainer = document.getElementById('searchResults');
            const resultsCount = document.getElementById('resultsCount');

            resultsCount.textContent = `${searchData.searchResults.length} user${searchData.searchResults.length === 1 ? '' : 's'} found`;

            if (searchData.searchResults.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">
                            <i class="fas fa-user-slash"></i>
                        </div>
                        <h3>No users found</h3>
                        <p>Try adjusting your search criteria or filters</p>
                    </div>
                `;
                return;
            }

            const resultsHTML = searchData.searchResults.map(user => `
                <div class="result-item" onclick="selectUser('${user.id}')">
                    <div class="result-avatar">${user.avatar}</div>
                    <div class="result-info">
                        <div class="result-name">${user.firstName} ${user.lastName}</div>
                        <div class="result-email">${user.email}</div>
                        <div class="result-meta">
                            <span><i class="fas fa-map-marker-alt"></i> ${user.location}</span>
                            <span><i class="fas fa-sign-in-alt"></i> ${user.loginCount} logins</span>
                            <span><i class="fas fa-clock"></i> ${formatTimeAgo(user.lastLogin)}</span>
                        </div>
                    </div>
                    <div class="result-role ${user.role}">${user.role.toUpperCase()}</div>
                    <div class="result-actions">
                        <button class="action-btn view" onclick="viewUser('${user.id}'); event.stopPropagation();">
                            <i class="fas fa-eye"></i>
                            View
                        </button>
                        <button class="action-btn contact" onclick="contactUser('${user.id}'); event.stopPropagation();">
                            <i class="fas fa-envelope"></i>
                            Contact
                        </button>
                    </div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;
        }

        // UI Functions
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            advancedSearch.classList.toggle('active');
        }

        function quickSearch(term) {
            document.getElementById('mainSearch').value = term;
            performSearch();
        }

        // User Actions
        function selectUser(userId) {
            const user = searchData.users.find(u => u.id === userId);
            if (user) {
                window.notificationManager.info(`Selected user: ${user.firstName} ${user.lastName}`);
            }
        }

        function viewUser(userId) {
            const user = searchData.users.find(u => u.id === userId);
            if (user) {
                window.notificationManager.info(`Viewing profile for: ${user.firstName} ${user.lastName}`);
            }
        }

        function contactUser(userId) {
            const user = searchData.users.find(u => u.id === userId);
            if (user) {
                window.notificationManager.success(`Opening contact form for: ${user.firstName} ${user.lastName}`);
            }
        }

        // Utility Functions
        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else throw new Error('Server error');
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');
            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');
            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
