<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 2rem auto;
        }

        .profile-header {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            font-weight: bold;
            position: relative;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
        }

        .avatar-upload {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30px;
            height: 30px;
            background: var(--success-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .profile-info {
            flex: 1;
        }

        .profile-name {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .profile-email {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .profile-stats {
            display: flex;
            gap: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .profile-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .section-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .section-actions {
            display: flex;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
        }

        .form-input:disabled {
            background: var(--hover-background);
            color: var(--text-secondary);
            cursor: not-allowed;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .checkbox-input {
            width: 18px;
            height: 18px;
        }

        .checkbox-label {
            font-weight: normal;
            margin-bottom: 0;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .activity-log {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .activity-icon.login {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .activity-icon.update {
            background: rgba(var(--info-color-rgb), 0.2);
            color: var(--info-color);
        }

        .activity-icon.security {
            background: rgba(var(--warning-color-rgb), 0.2);
            color: var(--warning-color);
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .activity-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .security-settings {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .security-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .security-item:last-child {
            border-bottom: none;
        }

        .security-info {
            flex: 1;
        }

        .security-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .security-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .security-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin-right: 1rem;
        }

        .security-status.enabled {
            background: var(--success-color);
            color: white;
        }

        .security-status.disabled {
            background: var(--error-color);
            color: white;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--success-color);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .danger-zone {
            background: rgba(var(--error-color-rgb), 0.1);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .danger-title {
            color: var(--error-color);
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .danger-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-sections {
                grid-template-columns: 1fr;
            }

            .profile-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user"></i>
                    <h1>User Profile Testing</h1>
                    <span class="subtitle">User Profile Management and Settings Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to User Management
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="profile-container">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-avatar" id="profileAvatar" onclick="uploadAvatar()">
                        JD
                        <div class="avatar-upload">
                            <i class="fas fa-camera"></i>
                        </div>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name" id="profileName">John Doe</div>
                        <div class="profile-email" id="profileEmail"><EMAIL></div>
                        <div class="profile-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="loginCount">127</div>
                                <div class="stat-label">Total Logins</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="sessionCount">23</div>
                                <div class="stat-label">Active Sessions</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="deviceCount">5</div>
                                <div class="stat-label">Trusted Devices</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="loadProfile()">
                        <i class="fas fa-sync"></i>
                        Refresh Profile
                    </button>
                    <button class="btn btn-success" onclick="updateProfile()">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                    <button class="btn btn-info" onclick="exportProfile()">
                        <i class="fas fa-download"></i>
                        Export Profile
                    </button>
                    <button class="btn btn-warning" onclick="resetPassword()">
                        <i class="fas fa-key"></i>
                        Reset Password
                    </button>
                </div>

                <!-- Profile Sections -->
                <div class="profile-sections">
                    <!-- Personal Information -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">Personal Information</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-secondary" onclick="toggleEdit('personal')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                        <form id="personalForm">
                            <div class="form-group">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-input" id="firstName" value="John" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-input" id="lastName" value="Doe" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-input" id="email" value="<EMAIL>" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-input" id="phone" value="+****************" disabled>
                            </div>
                        </form>
                    </div>

                    <!-- Account Settings -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">Account Settings</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-secondary" onclick="toggleEdit('account')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                        <form id="accountForm">
                            <div class="form-group">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-input" id="username" value="johndoe" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Time Zone</label>
                                <select class="form-select" id="timezone" disabled>
                                    <option value="America/New_York">Eastern Time (ET)</option>
                                    <option value="America/Chicago">Central Time (CT)</option>
                                    <option value="America/Denver">Mountain Time (MT)</option>
                                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Language</label>
                                <select class="form-select" id="language" disabled>
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                </select>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox-input" id="emailNotifications" checked disabled>
                                <label class="checkbox-label">Email Notifications</label>
                            </div>
                        </form>
                    </div>

                    <!-- Profile Details -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">Profile Details</div>
                            <div class="section-actions">
                                <button class="btn btn-sm btn-secondary" onclick="toggleEdit('profile')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                        <form id="profileForm">
                            <div class="form-group">
                                <label class="form-label">Job Title</label>
                                <input type="text" class="form-input" id="jobTitle" value="Software Engineer" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Company</label>
                                <input type="text" class="form-input" id="company" value="Tech Corp" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Location</label>
                                <input type="text" class="form-input" id="location" value="New York, NY" disabled>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Bio</label>
                                <textarea class="form-input form-textarea" id="bio" disabled>Passionate software engineer with 5+ years of experience in web development and API design.</textarea>
                            </div>
                        </form>
                    </div>

                    <!-- Privacy Settings -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">Privacy Settings</div>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox-input" id="profilePublic" checked>
                            <label class="checkbox-label">Make profile public</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox-input" id="showEmail">
                            <label class="checkbox-label">Show email address</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox-input" id="allowMessages" checked>
                            <label class="checkbox-label">Allow direct messages</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox-input" id="dataCollection">
                            <label class="checkbox-label">Allow data collection for analytics</label>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="security-settings">
                    <h3>Security Settings</h3>

                    <div class="security-item">
                        <div class="security-info">
                            <div class="security-title">Two-Factor Authentication</div>
                            <div class="security-description">Add an extra layer of security to your account</div>
                        </div>
                        <div class="security-status enabled">ENABLED</div>
                        <div class="toggle-switch active" id="twoFactorToggle" onclick="toggleSecurity('twoFactor')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="security-item">
                        <div class="security-info">
                            <div class="security-title">Login Notifications</div>
                            <div class="security-description">Get notified when someone logs into your account</div>
                        </div>
                        <div class="security-status enabled">ENABLED</div>
                        <div class="toggle-switch active" id="loginNotificationsToggle" onclick="toggleSecurity('loginNotifications')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="security-item">
                        <div class="security-info">
                            <div class="security-title">Session Timeout</div>
                            <div class="security-description">Automatically log out after period of inactivity</div>
                        </div>
                        <div class="security-status enabled">ENABLED</div>
                        <div class="toggle-switch active" id="sessionTimeoutToggle" onclick="toggleSecurity('sessionTimeout')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="security-item">
                        <div class="security-info">
                            <div class="security-title">Device Tracking</div>
                            <div class="security-description">Track and manage devices that access your account</div>
                        </div>
                        <div class="security-status enabled">ENABLED</div>
                        <div class="toggle-switch active" id="deviceTrackingToggle" onclick="toggleSecurity('deviceTracking')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="activity-log">
                    <h3>Recent Activity</h3>
                    <div id="activityContainer">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>

                <!-- Danger Zone -->
                <div class="danger-zone">
                    <div class="danger-title">Danger Zone</div>
                    <p>These actions are irreversible. Please be careful.</p>
                    <div class="danger-actions">
                        <button class="btn btn-danger" onclick="deactivateAccount()">
                            <i class="fas fa-user-slash"></i>
                            Deactivate Account
                        </button>
                        <button class="btn btn-danger" onclick="deleteAccount()">
                            <i class="fas fa-trash"></i>
                            Delete Account
                        </button>
                        <button class="btn btn-warning" onclick="clearAllData()">
                            <i class="fas fa-eraser"></i>
                            Clear All Data
                        </button>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="profileResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 User Profile Testing Interface</p>
                    <p>User profile management and settings testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let profileData = {
            personal: {
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                phone: '+****************'
            },
            account: {
                username: 'johndoe',
                timezone: 'America/New_York',
                language: 'en',
                emailNotifications: true
            },
            profile: {
                jobTitle: 'Software Engineer',
                company: 'Tech Corp',
                location: 'New York, NY',
                bio: 'Passionate software engineer with 5+ years of experience in web development and API design.'
            },
            privacy: {
                profilePublic: true,
                showEmail: false,
                allowMessages: true,
                dataCollection: false
            },
            security: {
                twoFactor: true,
                loginNotifications: true,
                sessionTimeout: true,
                deviceTracking: true
            },
            stats: {
                loginCount: 127,
                sessionCount: 23,
                deviceCount: 5
            },
            activity: []
        };

        let editModes = {
            personal: false,
            account: false,
            profile: false
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load profile data
            await loadProfile();

            // Load activity log
            loadActivityLog();
        }

        // Profile Management Functions
        async function loadProfile() {
            window.notificationManager.info('Loading profile data...');

            try {
                const response = await window.apiClient.request('GET', '/user/profile');

                if (response.success) {
                    updateProfileData(response.data);
                    window.notificationManager.success('Profile data loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load profile data');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Use mock profile data
                updateProfileDisplay();
                window.notificationManager.success('Profile data loaded successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    data: profileData,
                    message: 'Mock profile data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function updateProfileData(data) {
            if (data.personal) profileData.personal = { ...profileData.personal, ...data.personal };
            if (data.account) profileData.account = { ...profileData.account, ...data.account };
            if (data.profile) profileData.profile = { ...profileData.profile, ...data.profile };
            if (data.privacy) profileData.privacy = { ...profileData.privacy, ...data.privacy };
            if (data.security) profileData.security = { ...profileData.security, ...data.security };
            if (data.stats) profileData.stats = { ...profileData.stats, ...data.stats };

            updateProfileDisplay();
        }

        function updateProfileDisplay() {
            // Update header
            document.getElementById('profileName').textContent = `${profileData.personal.firstName} ${profileData.personal.lastName}`;
            document.getElementById('profileEmail').textContent = profileData.personal.email;
            document.getElementById('profileAvatar').textContent = getInitials(profileData.personal.firstName, profileData.personal.lastName);

            // Update stats
            document.getElementById('loginCount').textContent = profileData.stats.loginCount;
            document.getElementById('sessionCount').textContent = profileData.stats.sessionCount;
            document.getElementById('deviceCount').textContent = profileData.stats.deviceCount;

            // Update forms
            updateFormFields();
            updateSecurityToggles();
            updatePrivacySettings();
        }

        function updateFormFields() {
            // Personal information
            document.getElementById('firstName').value = profileData.personal.firstName;
            document.getElementById('lastName').value = profileData.personal.lastName;
            document.getElementById('email').value = profileData.personal.email;
            document.getElementById('phone').value = profileData.personal.phone;

            // Account settings
            document.getElementById('username').value = profileData.account.username;
            document.getElementById('timezone').value = profileData.account.timezone;
            document.getElementById('language').value = profileData.account.language;
            document.getElementById('emailNotifications').checked = profileData.account.emailNotifications;

            // Profile details
            document.getElementById('jobTitle').value = profileData.profile.jobTitle;
            document.getElementById('company').value = profileData.profile.company;
            document.getElementById('location').value = profileData.profile.location;
            document.getElementById('bio').value = profileData.profile.bio;
        }

        function updateSecurityToggles() {
            Object.keys(profileData.security).forEach(setting => {
                const toggle = document.getElementById(`${setting}Toggle`);
                if (toggle) {
                    if (profileData.security[setting]) {
                        toggle.classList.add('active');
                    } else {
                        toggle.classList.remove('active');
                    }
                }
            });
        }

        function updatePrivacySettings() {
            Object.keys(profileData.privacy).forEach(setting => {
                const checkbox = document.getElementById(setting);
                if (checkbox) {
                    checkbox.checked = profileData.privacy[setting];
                }
            });
        }

        // Form Management Functions
        function toggleEdit(section) {
            editModes[section] = !editModes[section];
            const form = document.getElementById(`${section}Form`);
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.disabled = !editModes[section];
            });

            const button = event.target.closest('button');
            if (editModes[section]) {
                button.innerHTML = '<i class="fas fa-save"></i> Save';
                button.className = 'btn btn-sm btn-success';
                button.onclick = () => saveSection(section);
            } else {
                button.innerHTML = '<i class="fas fa-edit"></i> Edit';
                button.className = 'btn btn-sm btn-secondary';
                button.onclick = () => toggleEdit(section);
            }
        }

        async function saveSection(section) {
            const formData = getFormData(section);

            window.notificationManager.info(`Saving ${section} information...`);

            try {
                const response = await window.apiClient.request('PUT', `/user/profile/${section}`, formData);

                if (response.success) {
                    profileData[section] = { ...profileData[section], ...formData };
                    updateProfileDisplay();
                    toggleEdit(section); // Exit edit mode
                    window.notificationManager.success(`${section} information saved successfully`);
                } else {
                    window.notificationManager.error(response.error || `Failed to save ${section} information`);
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate save
                profileData[section] = { ...profileData[section], ...formData };
                updateProfileDisplay();
                toggleEdit(section); // Exit edit mode
                window.notificationManager.success(`${section} information saved successfully (simulated)`);

                showResponse('profileResponse', {
                    success: true,
                    data: formData,
                    message: `Mock ${section} save successful (endpoint may not be available)`
                }, 'warning');
            }
        }

        function getFormData(section) {
            const form = document.getElementById(`${section}Form`);
            const formData = {};

            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    formData[input.id] = input.checked;
                } else {
                    formData[input.id] = input.value;
                }
            });

            return formData;
        }

        async function updateProfile() {
            window.notificationManager.info('Updating complete profile...');

            const allData = {
                personal: getFormData('personal'),
                account: getFormData('account'),
                profile: getFormData('profile'),
                privacy: {
                    profilePublic: document.getElementById('profilePublic').checked,
                    showEmail: document.getElementById('showEmail').checked,
                    allowMessages: document.getElementById('allowMessages').checked,
                    dataCollection: document.getElementById('dataCollection').checked
                }
            };

            try {
                const response = await window.apiClient.request('PUT', '/user/profile', allData);

                if (response.success) {
                    Object.keys(allData).forEach(section => {
                        profileData[section] = { ...profileData[section], ...allData[section] };
                    });
                    updateProfileDisplay();
                    window.notificationManager.success('Profile updated successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to update profile');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate update
                Object.keys(allData).forEach(section => {
                    profileData[section] = { ...profileData[section], ...allData[section] };
                });
                updateProfileDisplay();
                window.notificationManager.success('Profile updated successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    data: allData,
                    message: 'Mock profile update successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Security Functions
        async function toggleSecurity(setting) {
            const currentValue = profileData.security[setting];
            const newValue = !currentValue;

            window.notificationManager.info(`${newValue ? 'Enabling' : 'Disabling'} ${setting}...`);

            try {
                const response = await window.apiClient.request('PUT', `/user/security/${setting}`, {
                    enabled: newValue
                });

                if (response.success) {
                    profileData.security[setting] = newValue;
                    updateSecurityToggle(setting, newValue);
                    window.notificationManager.success(`${setting} ${newValue ? 'enabled' : 'disabled'} successfully`);
                } else {
                    window.notificationManager.error(response.error || `Failed to update ${setting}`);
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate toggle
                profileData.security[setting] = newValue;
                updateSecurityToggle(setting, newValue);
                window.notificationManager.success(`${setting} ${newValue ? 'enabled' : 'disabled'} successfully (simulated)`);

                showResponse('profileResponse', {
                    success: true,
                    data: { [setting]: newValue },
                    message: `Mock ${setting} toggle successful (endpoint may not be available)`
                }, 'warning');
            }
        }

        function updateSecurityToggle(setting, value) {
            const toggle = document.getElementById(`${setting}Toggle`);
            const status = toggle.parentElement.querySelector('.security-status');

            if (value) {
                toggle.classList.add('active');
                status.textContent = 'ENABLED';
                status.className = 'security-status enabled';
            } else {
                toggle.classList.remove('active');
                status.textContent = 'DISABLED';
                status.className = 'security-status disabled';
            }
        }

        // Action Functions
        function uploadAvatar() {
            window.notificationManager.info('Avatar upload functionality would be implemented here');

            // Simulate avatar upload
            setTimeout(() => {
                window.notificationManager.success('Avatar uploaded successfully (simulated)');
            }, 1000);
        }

        function exportProfile() {
            window.notificationManager.info('Exporting profile data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                profile: profileData,
                exportType: 'complete_profile'
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `profile-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Profile data exported successfully');
        }

        async function resetPassword() {
            if (!confirm('Are you sure you want to reset your password? You will receive an email with reset instructions.')) {
                return;
            }

            window.notificationManager.info('Sending password reset email...');

            try {
                const response = await window.apiClient.request('POST', '/user/reset-password', {
                    email: profileData.personal.email
                });

                if (response.success) {
                    window.notificationManager.success('Password reset email sent successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to send password reset email');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate password reset
                window.notificationManager.success('Password reset email sent successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    message: 'Mock password reset email sent (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Danger Zone Functions
        async function deactivateAccount() {
            if (!confirm('Are you sure you want to deactivate your account? This action can be reversed within 30 days.')) {
                return;
            }

            const reason = prompt('Please provide a reason for deactivation (optional):');

            window.notificationManager.warning('Deactivating account...');

            try {
                const response = await window.apiClient.request('POST', '/user/deactivate', {
                    reason: reason
                });

                if (response.success) {
                    window.notificationManager.success('Account deactivated successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to deactivate account');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate deactivation
                window.notificationManager.success('Account deactivated successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    data: { reason: reason, deactivatedAt: new Date().toISOString() },
                    message: 'Mock account deactivation successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function deleteAccount() {
            if (!confirm('Are you sure you want to DELETE your account? This action is IRREVERSIBLE and will permanently remove all your data.')) {
                return;
            }

            const confirmation = prompt('Type "DELETE" to confirm account deletion:');
            if (confirmation !== 'DELETE') {
                window.notificationManager.error('Account deletion cancelled - confirmation text did not match');
                return;
            }

            window.notificationManager.error('Deleting account...');

            try {
                const response = await window.apiClient.request('DELETE', '/user/account');

                if (response.success) {
                    window.notificationManager.success('Account deleted successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to delete account');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate deletion
                window.notificationManager.success('Account deleted successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    message: 'Mock account deletion successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function clearAllData() {
            if (!confirm('Are you sure you want to clear all your data? This will remove all personal information, activity logs, and preferences.')) {
                return;
            }

            window.notificationManager.warning('Clearing all user data...');

            try {
                const response = await window.apiClient.request('POST', '/user/clear-data');

                if (response.success) {
                    window.notificationManager.success('All user data cleared successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to clear user data');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate data clearing
                window.notificationManager.success('All user data cleared successfully (simulated)');

                showResponse('profileResponse', {
                    success: true,
                    message: 'Mock data clearing successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Activity Log Functions
        function loadActivityLog() {
            const mockActivity = [
                {
                    type: 'login',
                    title: 'Successful Login',
                    description: 'Logged in from Chrome on Windows',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
                },
                {
                    type: 'update',
                    title: 'Profile Updated',
                    description: 'Updated job title and company information',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
                },
                {
                    type: 'security',
                    title: 'Security Setting Changed',
                    description: 'Enabled two-factor authentication',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day ago
                },
                {
                    type: 'login',
                    title: 'New Device Login',
                    description: 'Logged in from Safari on iPhone',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2) // 2 days ago
                },
                {
                    type: 'update',
                    title: 'Password Changed',
                    description: 'Password was successfully updated',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) // 1 week ago
                }
            ];

            profileData.activity = mockActivity;
            displayActivityLog();
        }

        function displayActivityLog() {
            const container = document.getElementById('activityContainer');

            const activityHTML = profileData.activity.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        <i class="fas ${getActivityIcon(activity.type)}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-description">${activity.description}</div>
                        <div class="activity-time">${formatTimeAgo(activity.timestamp)}</div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = activityHTML;
        }

        function getActivityIcon(type) {
            switch (type) {
                case 'login': return 'fa-sign-in-alt';
                case 'update': return 'fa-edit';
                case 'security': return 'fa-shield-alt';
                default: return 'fa-info';
            }
        }

        // Utility Functions
        function getInitials(firstName, lastName) {
            return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
