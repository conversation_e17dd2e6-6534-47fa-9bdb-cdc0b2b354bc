<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Analytics Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .analytics-container {
            max-width: 1400px;
            margin: 2rem auto;
        }

        .analytics-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .overview-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--primary-color);
        }

        .metric-card.sessions::before {
            background: var(--primary-color);
        }

        .metric-card.users::before {
            background: var(--success-color);
        }

        .metric-card.duration::before {
            background: var(--warning-color);
        }

        .metric-card.devices::before {
            background: var(--info-color);
        }

        .metric-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .metric-change {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .metric-change.positive {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .metric-change.negative {
            background: rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
        }

        .metric-change.neutral {
            background: rgba(var(--text-secondary-rgb), 0.2);
            color: var(--text-secondary);
        }

        .analytics-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .chart-controls {
            display: flex;
            gap: 0.5rem;
        }

        .chart-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .chart-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .chart-btn:hover {
            background: var(--hover-background);
        }

        .chart-placeholder {
            height: 300px;
            background: var(--hover-background);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .analytics-tables {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .table-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .table-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
        }

        .table-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-row:hover {
            background: var(--hover-background);
        }

        .row-info {
            flex: 1;
        }

        .row-primary {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .row-secondary {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .row-metric {
            font-weight: bold;
            color: var(--primary-color);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .time-range-selector {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .time-range-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .time-range-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .time-range-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .time-range-btn:hover {
            background: var(--hover-background);
        }

        .insights-panel {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .insight-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .insight-item:last-child {
            border-bottom: none;
        }

        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .insight-icon.info {
            background: rgba(var(--info-color-rgb), 0.2);
            color: var(--info-color);
        }

        .insight-icon.warning {
            background: rgba(var(--warning-color-rgb), 0.2);
            color: var(--warning-color);
        }

        .insight-icon.success {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .insight-content {
            flex: 1;
        }

        .insight-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .insight-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .export-controls {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .export-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 1024px) {
            .analytics-charts {
                grid-template-columns: 1fr;
            }

            .analytics-tables {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .overview-metrics {
                grid-template-columns: repeat(2, 1fr);
            }

            .time-range-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <h1>Session Analytics Testing</h1>
                    <span class="subtitle">Session Analytics and Insights Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sessions
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="analytics-container">
                <!-- Analytics Overview -->
                <div class="analytics-overview">
                    <h2>Session Analytics Overview</h2>
                    <div class="overview-metrics">
                        <div class="metric-card sessions">
                            <div class="metric-number" id="totalSessions">0</div>
                            <div class="metric-label">Total Sessions</div>
                            <div class="metric-change positive" id="sessionsChange">+12.5%</div>
                        </div>
                        <div class="metric-card users">
                            <div class="metric-number" id="activeUsers">0</div>
                            <div class="metric-label">Active Users</div>
                            <div class="metric-change positive" id="usersChange">+8.3%</div>
                        </div>
                        <div class="metric-card duration">
                            <div class="metric-number" id="avgDuration">0m</div>
                            <div class="metric-label">Avg Session Duration</div>
                            <div class="metric-change negative" id="durationChange">-2.1%</div>
                        </div>
                        <div class="metric-card devices">
                            <div class="metric-number" id="uniqueDevices">0</div>
                            <div class="metric-label">Unique Devices</div>
                            <div class="metric-change positive" id="devicesChange">+15.7%</div>
                        </div>
                    </div>
                </div>

                <!-- Time Range Selector -->
                <div class="time-range-selector">
                    <h3>Time Range</h3>
                    <div class="time-range-buttons">
                        <button class="time-range-btn" onclick="setTimeRange('1h')">Last Hour</button>
                        <button class="time-range-btn active" onclick="setTimeRange('24h')">Last 24 Hours</button>
                        <button class="time-range-btn" onclick="setTimeRange('7d')">Last 7 Days</button>
                        <button class="time-range-btn" onclick="setTimeRange('30d')">Last 30 Days</button>
                        <button class="time-range-btn" onclick="setTimeRange('90d')">Last 90 Days</button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync"></i>
                        Refresh Analytics
                    </button>
                    <button class="btn btn-info" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i>
                        Generate Report
                    </button>
                    <button class="btn btn-success" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        Export Data
                    </button>
                    <button class="btn btn-warning" onclick="scheduleReport()">
                        <i class="fas fa-clock"></i>
                        Schedule Report
                    </button>
                </div>

                <!-- Analytics Charts -->
                <div class="analytics-charts">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">Session Activity Over Time</div>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="setChartType('sessions', 'line')">Line</button>
                                <button class="chart-btn" onclick="setChartType('sessions', 'bar')">Bar</button>
                                <button class="chart-btn" onclick="setChartType('sessions', 'area')">Area</button>
                            </div>
                        </div>
                        <div class="chart-placeholder" id="sessionsChart">
                            <i class="fas fa-chart-line" style="margin-right: 0.5rem;"></i>
                            Session Activity Chart
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">Device Distribution</div>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="setChartType('devices', 'pie')">Pie</button>
                                <button class="chart-btn" onclick="setChartType('devices', 'doughnut')">Doughnut</button>
                            </div>
                        </div>
                        <div class="chart-placeholder" id="devicesChart">
                            <i class="fas fa-chart-pie" style="margin-right: 0.5rem;"></i>
                            Device Distribution Chart
                        </div>
                    </div>
                </div>

                <!-- Analytics Tables -->
                <div class="analytics-tables">
                    <div class="table-card">
                        <div class="table-header">Top Locations</div>
                        <div id="topLocations">
                            <!-- Location data will be populated here -->
                        </div>
                    </div>

                    <div class="table-card">
                        <div class="table-header">Top User Agents</div>
                        <div id="topUserAgents">
                            <!-- User agent data will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h3>Analytics Insights</h3>
                    <div id="insightsContainer">
                        <!-- Insights will be populated here -->
                    </div>
                </div>

                <!-- Export Controls -->
                <div class="export-controls">
                    <h4>Export Options</h4>
                    <div class="export-buttons">
                        <button class="btn btn-sm btn-primary" onclick="exportCSV()">
                            <i class="fas fa-file-csv"></i>
                            Export CSV
                        </button>
                        <button class="btn btn-sm btn-info" onclick="exportJSON()">
                            <i class="fas fa-file-code"></i>
                            Export JSON
                        </button>
                        <button class="btn btn-sm btn-success" onclick="exportPDF()">
                            <i class="fas fa-file-pdf"></i>
                            Export PDF
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="exportExcel()">
                            <i class="fas fa-file-excel"></i>
                            Export Excel
                        </button>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="analyticsResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Session Analytics Testing Interface</p>
                    <p>Session analytics and insights testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let analyticsData = {
            metrics: {
                totalSessions: 0,
                activeUsers: 0,
                avgDuration: 0,
                uniqueDevices: 0
            },
            timeRange: '24h',
            chartTypes: {
                sessions: 'line',
                devices: 'pie'
            },
            locations: [],
            userAgents: [],
            insights: []
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load analytics data
            await loadAnalytics();
        }

        // Analytics Data Functions
        async function loadAnalytics() {
            window.notificationManager.info('Loading analytics data...');

            try {
                const response = await window.apiClient.request('GET', `/analytics/sessions?timeRange=${analyticsData.timeRange}`);

                if (response.success) {
                    updateAnalyticsData(response.data);
                    window.notificationManager.success('Analytics data loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load analytics data');
                }

                showResponse('analyticsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock analytics data
                loadMockAnalytics();
                window.notificationManager.success('Analytics data loaded successfully (simulated)');

                showResponse('analyticsResponse', {
                    success: true,
                    data: analyticsData,
                    message: 'Mock analytics data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockAnalytics() {
            const mockData = {
                metrics: {
                    totalSessions: 1247,
                    activeUsers: 892,
                    avgDuration: 23,
                    uniqueDevices: 456
                },
                locations: [
                    { name: 'New York, US', sessions: 324, percentage: 26.0 },
                    { name: 'Los Angeles, US', sessions: 198, percentage: 15.9 },
                    { name: 'London, UK', sessions: 156, percentage: 12.5 },
                    { name: 'Toronto, CA', sessions: 134, percentage: 10.7 },
                    { name: 'Sydney, AU', sessions: 98, percentage: 7.9 }
                ],
                userAgents: [
                    { name: 'Chrome 108.0', sessions: 456, percentage: 36.6 },
                    { name: 'Safari 16.1', sessions: 298, percentage: 23.9 },
                    { name: 'Firefox 107.0', sessions: 187, percentage: 15.0 },
                    { name: 'Edge 108.0', sessions: 156, percentage: 12.5 },
                    { name: 'Opera 93.0', sessions: 89, percentage: 7.1 }
                ],
                insights: [
                    {
                        type: 'info',
                        title: 'Peak Usage Hours',
                        description: 'Most sessions occur between 2 PM and 4 PM EST, with 23% of daily traffic.'
                    },
                    {
                        type: 'warning',
                        title: 'Session Duration Decline',
                        description: 'Average session duration has decreased by 2.1% compared to last period.'
                    },
                    {
                        type: 'success',
                        title: 'Mobile Growth',
                        description: 'Mobile device usage has increased by 15.7%, indicating strong mobile adoption.'
                    },
                    {
                        type: 'info',
                        title: 'Geographic Distribution',
                        description: 'US accounts for 41.9% of sessions, followed by UK (12.5%) and Canada (10.7%).'
                    }
                ]
            };

            updateAnalyticsData(mockData);
        }

        function updateAnalyticsData(data) {
            analyticsData.metrics = data.metrics;
            analyticsData.locations = data.locations || [];
            analyticsData.userAgents = data.userAgents || [];
            analyticsData.insights = data.insights || [];

            updateMetricsDisplay();
            updateTablesDisplay();
            updateInsightsDisplay();
        }

        function updateMetricsDisplay() {
            document.getElementById('totalSessions').textContent = formatNumber(analyticsData.metrics.totalSessions);
            document.getElementById('activeUsers').textContent = formatNumber(analyticsData.metrics.activeUsers);
            document.getElementById('avgDuration').textContent = analyticsData.metrics.avgDuration + 'm';
            document.getElementById('uniqueDevices').textContent = formatNumber(analyticsData.metrics.uniqueDevices);
        }

        function updateTablesDisplay() {
            // Update top locations
            const locationsContainer = document.getElementById('topLocations');
            const locationsHTML = analyticsData.locations.map(location => `
                <div class="table-row">
                    <div class="row-info">
                        <div class="row-primary">${location.name}</div>
                        <div class="row-secondary">${location.percentage}% of total sessions</div>
                    </div>
                    <div class="row-metric">${formatNumber(location.sessions)}</div>
                </div>
            `).join('');
            locationsContainer.innerHTML = locationsHTML;

            // Update top user agents
            const userAgentsContainer = document.getElementById('topUserAgents');
            const userAgentsHTML = analyticsData.userAgents.map(agent => `
                <div class="table-row">
                    <div class="row-info">
                        <div class="row-primary">${agent.name}</div>
                        <div class="row-secondary">${agent.percentage}% of total sessions</div>
                    </div>
                    <div class="row-metric">${formatNumber(agent.sessions)}</div>
                </div>
            `).join('');
            userAgentsContainer.innerHTML = userAgentsHTML;
        }

        function updateInsightsDisplay() {
            const insightsContainer = document.getElementById('insightsContainer');
            const insightsHTML = analyticsData.insights.map(insight => `
                <div class="insight-item">
                    <div class="insight-icon ${insight.type}">
                        <i class="fas ${getInsightIcon(insight.type)}"></i>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">${insight.title}</div>
                        <div class="insight-description">${insight.description}</div>
                    </div>
                </div>
            `).join('');
            insightsContainer.innerHTML = insightsHTML;
        }

        function getInsightIcon(type) {
            switch (type) {
                case 'info': return 'fa-info';
                case 'warning': return 'fa-exclamation-triangle';
                case 'success': return 'fa-check';
                default: return 'fa-info';
            }
        }

        // Time Range Functions
        function setTimeRange(range) {
            // Update active button
            document.querySelectorAll('.time-range-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            analyticsData.timeRange = range;
            window.notificationManager.info(`Time range set to: ${getTimeRangeLabel(range)}`);

            // Reload analytics data
            loadAnalytics();
        }

        function getTimeRangeLabel(range) {
            const labels = {
                '1h': 'Last Hour',
                '24h': 'Last 24 Hours',
                '7d': 'Last 7 Days',
                '30d': 'Last 30 Days',
                '90d': 'Last 90 Days'
            };
            return labels[range] || range;
        }

        // Chart Functions
        function setChartType(chart, type) {
            // Update active button
            const chartCard = document.getElementById(`${chart}Chart`).closest('.chart-card');
            chartCard.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            analyticsData.chartTypes[chart] = type;
            window.notificationManager.info(`${chart} chart type set to: ${type}`);

            // Update chart display
            updateChartDisplay(chart, type);
        }

        function updateChartDisplay(chart, type) {
            const chartElement = document.getElementById(`${chart}Chart`);
            const icons = {
                line: 'fa-chart-line',
                bar: 'fa-chart-bar',
                area: 'fa-chart-area',
                pie: 'fa-chart-pie',
                doughnut: 'fa-chart-pie'
            };

            chartElement.innerHTML = `
                <i class="fas ${icons[type]}" style="margin-right: 0.5rem;"></i>
                ${chart.charAt(0).toUpperCase() + chart.slice(1)} ${type.charAt(0).toUpperCase() + type.slice(1)} Chart
            `;
        }

        // Action Functions
        async function refreshAnalytics() {
            await loadAnalytics();
        }

        function generateReport() {
            window.notificationManager.info('Generating analytics report...');

            const reportData = {
                timestamp: new Date().toISOString(),
                timeRange: analyticsData.timeRange,
                metrics: analyticsData.metrics,
                topLocations: analyticsData.locations.slice(0, 5),
                topUserAgents: analyticsData.userAgents.slice(0, 5),
                insights: analyticsData.insights,
                summary: {
                    totalSessions: analyticsData.metrics.totalSessions,
                    growthRate: '+12.5%',
                    topLocation: analyticsData.locations[0]?.name || 'N/A',
                    topBrowser: analyticsData.userAgents[0]?.name || 'N/A'
                }
            };

            setTimeout(() => {
                const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `session-analytics-report-${Date.now()}.json`;
                a.click();
                window.URL.revokeObjectURL(url);

                window.notificationManager.success('Analytics report generated successfully');
            }, 1000);
        }

        function exportData() {
            window.notificationManager.info('Exporting analytics data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                timeRange: analyticsData.timeRange,
                data: analyticsData
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `session-analytics-data-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Analytics data exported successfully');
        }

        function scheduleReport() {
            window.notificationManager.info('Scheduling analytics report...');

            setTimeout(() => {
                window.notificationManager.success('Analytics report scheduled for daily delivery at 9:00 AM');

                showResponse('analyticsResponse', {
                    success: true,
                    data: {
                        schedule: 'daily',
                        time: '09:00',
                        recipients: ['<EMAIL>'],
                        format: 'PDF'
                    },
                    message: 'Report scheduling configured'
                }, 'success');
            }, 1000);
        }

        // Export Functions
        function exportCSV() {
            window.notificationManager.info('Exporting data as CSV...');

            const csvData = [
                ['Metric', 'Value'],
                ['Total Sessions', analyticsData.metrics.totalSessions],
                ['Active Users', analyticsData.metrics.activeUsers],
                ['Avg Duration (min)', analyticsData.metrics.avgDuration],
                ['Unique Devices', analyticsData.metrics.uniqueDevices],
                [''],
                ['Top Locations', 'Sessions'],
                ...analyticsData.locations.map(loc => [loc.name, loc.sessions])
            ];

            const csvContent = csvData.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `session-analytics-${Date.now()}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('CSV export completed');
        }

        function exportJSON() {
            exportData();
        }

        function exportPDF() {
            window.notificationManager.info('Generating PDF report...');

            setTimeout(() => {
                window.notificationManager.success('PDF report would be generated (requires PDF library)');
            }, 1000);
        }

        function exportExcel() {
            window.notificationManager.info('Generating Excel report...');

            setTimeout(() => {
                window.notificationManager.success('Excel report would be generated (requires Excel library)');
            }, 1000);
        }

        // Utility Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
