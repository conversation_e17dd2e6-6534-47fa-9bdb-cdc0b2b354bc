<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Device Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .devices-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .devices-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-number.trusted {
            color: var(--success-color);
        }

        .stat-number.blocked {
            color: var(--error-color);
        }

        .stat-number.pending {
            color: var(--warning-color);
        }

        .stat-number.total {
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .device-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .category-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .category-icon {
            font-size: 2rem;
            margin-right: 1rem;
            width: 50px;
            text-align: center;
        }

        .category-icon.desktop {
            color: #007bff;
        }

        .category-icon.mobile {
            color: #28a745;
        }

        .category-icon.tablet {
            color: #ffc107;
        }

        .category-icon.other {
            color: #6c757d;
        }

        .category-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .category-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .device-list {
            margin-top: 1rem;
        }

        .device-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .device-item:last-child {
            border-bottom: none;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .device-details {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .device-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin-right: 0.5rem;
        }

        .device-status.trusted {
            background: var(--success-color);
            color: white;
        }

        .device-status.blocked {
            background: var(--error-color);
            color: white;
        }

        .device-status.pending {
            background: var(--warning-color);
            color: white;
        }

        .device-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.trust {
            background: var(--success-color);
            color: white;
        }

        .action-btn.block {
            background: var(--error-color);
            color: white;
        }

        .action-btn.remove {
            background: var(--warning-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .device-controls {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .controls-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            min-width: 150px;
        }

        .device-details-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .detail-value {
            color: var(--text-secondary);
            font-family: monospace;
            font-size: 0.9rem;
        }

        .security-settings {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .setting-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--success-color);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .bulk-actions {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }

        .bulk-actions.show {
            display: block;
        }

        .bulk-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .device-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .device-actions {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-mobile-alt"></i>
                    <h1>Device Management Testing</h1>
                    <span class="subtitle">Device Management and Security Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sessions
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="devices-container">
                <!-- Devices Overview -->
                <div class="devices-overview">
                    <h2>Device Management Overview</h2>
                    <div class="overview-stats">
                        <div class="stat-card">
                            <div class="stat-number trusted" id="trustedDevices">0</div>
                            <div class="stat-label">Trusted Devices</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number blocked" id="blockedDevices">0</div>
                            <div class="stat-label">Blocked Devices</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number pending" id="pendingDevices">0</div>
                            <div class="stat-label">Pending Approval</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number total" id="totalDevices">0</div>
                            <div class="stat-label">Total Devices</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="refreshDevices()">
                        <i class="fas fa-sync"></i>
                        Refresh Devices
                    </button>
                    <button class="btn btn-success" onclick="trustAllPending()">
                        <i class="fas fa-check"></i>
                        Trust All Pending
                    </button>
                    <button class="btn btn-danger" onclick="blockSuspiciousDevices()">
                        <i class="fas fa-ban"></i>
                        Block Suspicious
                    </button>
                    <button class="btn btn-info" onclick="exportDevices()">
                        <i class="fas fa-download"></i>
                        Export Devices
                    </button>
                </div>

                <!-- Device Controls -->
                <div class="device-controls">
                    <div class="controls-row">
                        <input type="text" class="search-box" id="deviceSearch" placeholder="Search devices by name, user, or IP..." onkeyup="filterDevices()">
                        <select class="filter-select" id="statusFilter" onchange="filterDevices()">
                            <option value="all">All Status</option>
                            <option value="trusted">Trusted</option>
                            <option value="blocked">Blocked</option>
                            <option value="pending">Pending</option>
                        </select>
                        <select class="filter-select" id="typeFilter" onchange="filterDevices()">
                            <option value="all">All Types</option>
                            <option value="desktop">Desktop</option>
                            <option value="mobile">Mobile</option>
                            <option value="tablet">Tablet</option>
                            <option value="other">Other</option>
                        </select>
                        <button class="btn btn-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Device Categories -->
                <div class="device-categories">
                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon desktop">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div>
                                <div class="category-title">Desktop Devices</div>
                                <div class="category-count" id="desktopCount">0 devices</div>
                            </div>
                        </div>
                        <div class="device-list" id="desktopDevices">
                            <!-- Desktop devices will be populated here -->
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon mobile">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div>
                                <div class="category-title">Mobile Devices</div>
                                <div class="category-count" id="mobileCount">0 devices</div>
                            </div>
                        </div>
                        <div class="device-list" id="mobileDevices">
                            <!-- Mobile devices will be populated here -->
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon tablet">
                                <i class="fas fa-tablet-alt"></i>
                            </div>
                            <div>
                                <div class="category-title">Tablet Devices</div>
                                <div class="category-count" id="tabletCount">0 devices</div>
                            </div>
                        </div>
                        <div class="device-list" id="tabletDevices">
                            <!-- Tablet devices will be populated here -->
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-header">
                            <div class="category-icon other">
                                <i class="fas fa-question"></i>
                            </div>
                            <div>
                                <div class="category-title">Other Devices</div>
                                <div class="category-count" id="otherCount">0 devices</div>
                            </div>
                        </div>
                        <div class="device-list" id="otherDevices">
                            <!-- Other devices will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="security-settings">
                    <h3>Device Security Settings</h3>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Auto-Trust Known Devices</div>
                            <div class="setting-description">Automatically trust devices that have been used before</div>
                        </div>
                        <div class="toggle-switch" id="autoTrustToggle" onclick="toggleSetting('autoTrust')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Block Suspicious Devices</div>
                            <div class="setting-description">Automatically block devices with suspicious activity</div>
                        </div>
                        <div class="toggle-switch active" id="blockSuspiciousToggle" onclick="toggleSetting('blockSuspicious')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Require Device Approval</div>
                            <div class="setting-description">Require manual approval for new devices</div>
                        </div>
                        <div class="toggle-switch active" id="requireApprovalToggle" onclick="toggleSetting('requireApproval')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Device Fingerprinting</div>
                            <div class="setting-description">Use advanced device fingerprinting for identification</div>
                        </div>
                        <div class="toggle-switch active" id="fingerprintingToggle" onclick="toggleSetting('fingerprinting')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">Geo-Location Tracking</div>
                            <div class="setting-description">Track device locations for security monitoring</div>
                        </div>
                        <div class="toggle-switch" id="geoTrackingToggle" onclick="toggleSetting('geoTracking')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>

                <!-- Device Details Modal -->
                <div class="device-details-modal" id="deviceModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title">Device Details</div>
                            <button class="close-btn" onclick="closeDeviceModal()">&times;</button>
                        </div>
                        <div id="deviceDetails">
                            <!-- Device details will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="devicesResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Device Management Testing Interface</p>
                    <p>Device management and security testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let devicesData = {
            devices: [],
            filteredDevices: [],
            settings: {
                autoTrust: false,
                blockSuspicious: true,
                requireApproval: true,
                fingerprinting: true,
                geoTracking: false
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load devices data
            await loadDevices();

            // Initialize settings
            initializeSettings();
        }

        // Device Management Functions
        async function loadDevices() {
            window.notificationManager.info('Loading devices...');

            try {
                const response = await window.apiClient.request('GET', '/devices');

                if (response.success) {
                    devicesData.devices = response.data.devices || [];
                    updateDevicesDisplay();
                    window.notificationManager.success('Devices loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load devices');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock devices data
                loadMockDevices();
                window.notificationManager.success('Devices loaded successfully (simulated)');

                showResponse('devicesResponse', {
                    success: true,
                    data: { devices: devicesData.devices },
                    message: 'Mock devices data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockDevices() {
            const mockDevices = [
                {
                    id: 'device_1a2b3c4d',
                    name: 'John\'s MacBook Pro',
                    type: 'desktop',
                    os: 'macOS 13.0',
                    browser: 'Chrome 108.0',
                    userId: 'user_123',
                    userEmail: '<EMAIL>',
                    ipAddress: '*************',
                    location: 'New York, US',
                    status: 'trusted',
                    fingerprint: 'fp_abc123def456',
                    firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
                    lastSeen: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
                    sessionCount: 45,
                    riskScore: 10
                },
                {
                    id: 'device_2b3c4d5e',
                    name: 'Jane\'s iPhone 13',
                    type: 'mobile',
                    os: 'iOS 16.1',
                    browser: 'Safari Mobile',
                    userId: 'user_456',
                    userEmail: '<EMAIL>',
                    ipAddress: '*********',
                    location: 'Los Angeles, US',
                    status: 'trusted',
                    fingerprint: 'fp_def456ghi789',
                    firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
                    lastSeen: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    sessionCount: 28,
                    riskScore: 15
                },
                {
                    id: 'device_3c4d5e6f',
                    name: 'Unknown Windows PC',
                    type: 'desktop',
                    os: 'Windows 11',
                    browser: 'Edge 108.0',
                    userId: 'user_789',
                    userEmail: '<EMAIL>',
                    ipAddress: '************',
                    location: 'London, UK',
                    status: 'pending',
                    fingerprint: 'fp_ghi789jkl012',
                    firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    lastSeen: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    sessionCount: 1,
                    riskScore: 65
                },
                {
                    id: 'device_4d5e6f7g',
                    name: 'Suspicious Android',
                    type: 'mobile',
                    os: 'Android 12',
                    browser: 'Chrome Mobile',
                    userId: 'user_101',
                    userEmail: '<EMAIL>',
                    ipAddress: '*************',
                    location: 'Unknown',
                    status: 'blocked',
                    fingerprint: 'fp_jkl012mno345',
                    firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
                    sessionCount: 3,
                    riskScore: 85
                },
                {
                    id: 'device_5e6f7g8h',
                    name: 'Charlie\'s iPad Pro',
                    type: 'tablet',
                    os: 'iPadOS 16.1',
                    browser: 'Safari Mobile',
                    userId: 'user_202',
                    userEmail: '<EMAIL>',
                    ipAddress: '***********',
                    location: 'Toronto, CA',
                    status: 'trusted',
                    fingerprint: 'fp_mno345pqr678',
                    firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
                    lastSeen: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                    sessionCount: 12,
                    riskScore: 20
                }
            ];

            devicesData.devices = mockDevices;
            updateDevicesDisplay();
        }

        function updateDevicesDisplay() {
            // Apply filters
            filterDevices();

            // Update statistics
            updateDeviceStats();

            // Render device categories
            renderDeviceCategories();
        }

        function updateDeviceStats() {
            const stats = {
                trusted: 0,
                blocked: 0,
                pending: 0,
                total: devicesData.devices.length
            };

            devicesData.devices.forEach(device => {
                if (device.status === 'trusted') stats.trusted++;
                if (device.status === 'blocked') stats.blocked++;
                if (device.status === 'pending') stats.pending++;
            });

            document.getElementById('trustedDevices').textContent = stats.trusted;
            document.getElementById('blockedDevices').textContent = stats.blocked;
            document.getElementById('pendingDevices').textContent = stats.pending;
            document.getElementById('totalDevices').textContent = stats.total;
        }

        function filterDevices() {
            const searchTerm = document.getElementById('deviceSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            devicesData.filteredDevices = devicesData.devices.filter(device => {
                const matchesSearch = !searchTerm ||
                    device.name.toLowerCase().includes(searchTerm) ||
                    device.userEmail.toLowerCase().includes(searchTerm) ||
                    device.ipAddress.includes(searchTerm) ||
                    device.os.toLowerCase().includes(searchTerm);

                const matchesStatus = statusFilter === 'all' || device.status === statusFilter;
                const matchesType = typeFilter === 'all' || device.type === typeFilter;

                return matchesSearch && matchesStatus && matchesType;
            });

            renderDeviceCategories();
        }

        function clearFilters() {
            document.getElementById('deviceSearch').value = '';
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('typeFilter').value = 'all';
            filterDevices();
        }

        function renderDeviceCategories() {
            const categories = {
                desktop: devicesData.filteredDevices.filter(d => d.type === 'desktop'),
                mobile: devicesData.filteredDevices.filter(d => d.type === 'mobile'),
                tablet: devicesData.filteredDevices.filter(d => d.type === 'tablet'),
                other: devicesData.filteredDevices.filter(d => !['desktop', 'mobile', 'tablet'].includes(d.type))
            };

            // Update counts
            document.getElementById('desktopCount').textContent = `${categories.desktop.length} devices`;
            document.getElementById('mobileCount').textContent = `${categories.mobile.length} devices`;
            document.getElementById('tabletCount').textContent = `${categories.tablet.length} devices`;
            document.getElementById('otherCount').textContent = `${categories.other.length} devices`;

            // Render device lists
            Object.keys(categories).forEach(type => {
                renderDeviceList(type, categories[type]);
            });
        }

        function renderDeviceList(type, devices) {
            const container = document.getElementById(`${type}Devices`);

            if (devices.length === 0) {
                container.innerHTML = `
                    <div style="padding: 1rem; text-align: center; color: var(--text-secondary);">
                        No ${type} devices found
                    </div>
                `;
                return;
            }

            const devicesHTML = devices.map(device => `
                <div class="device-item">
                    <div class="device-info">
                        <div class="device-name">${device.name}</div>
                        <div class="device-details">${device.os} • ${device.browser} • ${device.location}</div>
                    </div>
                    <div class="device-status ${device.status}">${device.status.toUpperCase()}</div>
                    <div class="device-actions">
                        <button class="action-btn" onclick="viewDeviceDetails('${device.id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${device.status !== 'trusted' ? `
                            <button class="action-btn trust" onclick="trustDevice('${device.id}')" title="Trust Device">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        ${device.status !== 'blocked' ? `
                            <button class="action-btn block" onclick="blockDevice('${device.id}')" title="Block Device">
                                <i class="fas fa-ban"></i>
                            </button>
                        ` : ''}
                        <button class="action-btn remove" onclick="removeDevice('${device.id}')" title="Remove Device">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = devicesHTML;
        }

        // Device Actions
        async function refreshDevices() {
            await loadDevices();
        }

        async function trustDevice(deviceId) {
            const device = devicesData.devices.find(d => d.id === deviceId);
            if (!device) return;

            window.notificationManager.info(`Trusting device: ${device.name}`);

            try {
                const response = await window.apiClient.request('PUT', `/devices/${deviceId}/trust`);

                if (response.success) {
                    device.status = 'trusted';
                    device.riskScore = Math.max(device.riskScore - 20, 0);
                    updateDevicesDisplay();
                    window.notificationManager.success('Device trusted successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to trust device');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate device trust
                device.status = 'trusted';
                device.riskScore = Math.max(device.riskScore - 20, 0);
                updateDevicesDisplay();
                window.notificationManager.success('Device trusted successfully (simulated)');

                showResponse('devicesResponse', {
                    success: true,
                    data: { deviceId: deviceId, status: 'trusted' },
                    message: 'Mock device trust successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function blockDevice(deviceId) {
            const device = devicesData.devices.find(d => d.id === deviceId);
            if (!device) return;

            if (!confirm(`Are you sure you want to block device: ${device.name}?`)) {
                return;
            }

            window.notificationManager.warning(`Blocking device: ${device.name}`);

            try {
                const response = await window.apiClient.request('PUT', `/devices/${deviceId}/block`);

                if (response.success) {
                    device.status = 'blocked';
                    device.riskScore = Math.min(device.riskScore + 30, 100);
                    updateDevicesDisplay();
                    window.notificationManager.success('Device blocked successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to block device');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate device block
                device.status = 'blocked';
                device.riskScore = Math.min(device.riskScore + 30, 100);
                updateDevicesDisplay();
                window.notificationManager.success('Device blocked successfully (simulated)');

                showResponse('devicesResponse', {
                    success: true,
                    data: { deviceId: deviceId, status: 'blocked' },
                    message: 'Mock device block successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function removeDevice(deviceId) {
            const device = devicesData.devices.find(d => d.id === deviceId);
            if (!device) return;

            if (!confirm(`Are you sure you want to remove device: ${device.name}? This action cannot be undone.`)) {
                return;
            }

            window.notificationManager.info(`Removing device: ${device.name}`);

            try {
                const response = await window.apiClient.request('DELETE', `/devices/${deviceId}`);

                if (response.success) {
                    devicesData.devices = devicesData.devices.filter(d => d.id !== deviceId);
                    updateDevicesDisplay();
                    window.notificationManager.success('Device removed successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to remove device');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate device removal
                devicesData.devices = devicesData.devices.filter(d => d.id !== deviceId);
                updateDevicesDisplay();
                window.notificationManager.success('Device removed successfully (simulated)');

                showResponse('devicesResponse', {
                    success: true,
                    message: 'Mock device removal successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function trustAllPending() {
            const pendingDevices = devicesData.devices.filter(d => d.status === 'pending');
            if (pendingDevices.length === 0) {
                window.notificationManager.info('No pending devices to trust');
                return;
            }

            if (!confirm(`Are you sure you want to trust all ${pendingDevices.length} pending devices?`)) {
                return;
            }

            window.notificationManager.info(`Trusting ${pendingDevices.length} pending devices...`);

            try {
                const response = await window.apiClient.request('PUT', '/devices/trust-pending');

                if (response.success) {
                    pendingDevices.forEach(device => {
                        device.status = 'trusted';
                        device.riskScore = Math.max(device.riskScore - 15, 0);
                    });
                    updateDevicesDisplay();
                    window.notificationManager.success(`${pendingDevices.length} devices trusted successfully`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to trust pending devices');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate trust all pending
                pendingDevices.forEach(device => {
                    device.status = 'trusted';
                    device.riskScore = Math.max(device.riskScore - 15, 0);
                });
                updateDevicesDisplay();
                window.notificationManager.success(`${pendingDevices.length} devices trusted successfully (simulated)`);

                showResponse('devicesResponse', {
                    success: true,
                    data: { trustedCount: pendingDevices.length },
                    message: 'Mock trust all pending successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function blockSuspiciousDevices() {
            const suspiciousDevices = devicesData.devices.filter(d => d.riskScore > 70 && d.status !== 'blocked');
            if (suspiciousDevices.length === 0) {
                window.notificationManager.info('No suspicious devices found');
                return;
            }

            if (!confirm(`Are you sure you want to block ${suspiciousDevices.length} suspicious devices?`)) {
                return;
            }

            window.notificationManager.warning(`Blocking ${suspiciousDevices.length} suspicious devices...`);

            try {
                const response = await window.apiClient.request('PUT', '/devices/block-suspicious');

                if (response.success) {
                    suspiciousDevices.forEach(device => {
                        device.status = 'blocked';
                    });
                    updateDevicesDisplay();
                    window.notificationManager.success(`${suspiciousDevices.length} suspicious devices blocked`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to block suspicious devices');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate block suspicious devices
                suspiciousDevices.forEach(device => {
                    device.status = 'blocked';
                });
                updateDevicesDisplay();
                window.notificationManager.success(`${suspiciousDevices.length} suspicious devices blocked (simulated)`);

                showResponse('devicesResponse', {
                    success: true,
                    data: { blockedCount: suspiciousDevices.length },
                    message: 'Mock block suspicious devices successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function exportDevices() {
            window.notificationManager.info('Exporting devices data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                totalDevices: devicesData.devices.length,
                trustedDevices: devicesData.devices.filter(d => d.status === 'trusted').length,
                blockedDevices: devicesData.devices.filter(d => d.status === 'blocked').length,
                pendingDevices: devicesData.devices.filter(d => d.status === 'pending').length,
                settings: devicesData.settings,
                devices: devicesData.devices
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `devices-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Devices data exported successfully');
        }

        // Device Details Modal
        function viewDeviceDetails(deviceId) {
            const device = devicesData.devices.find(d => d.id === deviceId);
            if (!device) return;

            const modal = document.getElementById('deviceModal');
            const detailsContainer = document.getElementById('deviceDetails');

            const detailsHTML = `
                <div class="detail-item">
                    <div class="detail-label">Device ID:</div>
                    <div class="detail-value">${device.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Device Name:</div>
                    <div class="detail-value">${device.name}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">User:</div>
                    <div class="detail-value">${device.userEmail}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Device Type:</div>
                    <div class="detail-value">${device.type}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Operating System:</div>
                    <div class="detail-value">${device.os}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Browser:</div>
                    <div class="detail-value">${device.browser}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">IP Address:</div>
                    <div class="detail-value">${device.ipAddress}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Location:</div>
                    <div class="detail-value">${device.location}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">${device.status.toUpperCase()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Risk Score:</div>
                    <div class="detail-value">${device.riskScore}/100</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Fingerprint:</div>
                    <div class="detail-value">${device.fingerprint}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">First Seen:</div>
                    <div class="detail-value">${device.firstSeen.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Last Seen:</div>
                    <div class="detail-value">${device.lastSeen.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Session Count:</div>
                    <div class="detail-value">${device.sessionCount}</div>
                </div>
            `;

            detailsContainer.innerHTML = detailsHTML;
            modal.style.display = 'flex';
        }

        function closeDeviceModal() {
            document.getElementById('deviceModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('deviceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeviceModal();
            }
        });

        // Settings Management
        function initializeSettings() {
            Object.keys(devicesData.settings).forEach(setting => {
                const toggle = document.getElementById(`${setting}Toggle`);
                if (toggle) {
                    if (devicesData.settings[setting]) {
                        toggle.classList.add('active');
                    } else {
                        toggle.classList.remove('active');
                    }
                }
            });
        }

        async function toggleSetting(settingName) {
            const currentValue = devicesData.settings[settingName];
            const newValue = !currentValue;

            window.notificationManager.info(`${newValue ? 'Enabling' : 'Disabling'} ${settingName}...`);

            try {
                const response = await window.apiClient.request('PUT', '/devices/settings', {
                    [settingName]: newValue
                });

                if (response.success) {
                    devicesData.settings[settingName] = newValue;
                    updateSettingToggle(settingName, newValue);
                    window.notificationManager.success(`Setting ${newValue ? 'enabled' : 'disabled'} successfully`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to update setting');
                }

                showResponse('devicesResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate setting toggle
                devicesData.settings[settingName] = newValue;
                updateSettingToggle(settingName, newValue);
                window.notificationManager.success(`Setting ${newValue ? 'enabled' : 'disabled'} successfully (simulated)`);

                showResponse('devicesResponse', {
                    success: true,
                    data: { [settingName]: newValue },
                    message: 'Mock setting update successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function updateSettingToggle(settingName, value) {
            const toggle = document.getElementById(`${settingName}Toggle`);
            if (toggle) {
                if (value) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }
            }
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
