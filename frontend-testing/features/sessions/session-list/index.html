<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session List Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sessions-container {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .sessions-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-number.active {
            color: var(--success-color);
        }

        .stat-number.expired {
            color: var(--error-color);
        }

        .stat-number.total {
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .sessions-controls {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .controls-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            min-width: 150px;
        }

        .sessions-table {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .table-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 1fr 150px 120px 150px 120px 100px;
            gap: 1rem;
            align-items: center;
        }

        .session-row {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 1fr 150px 120px 150px 120px 100px;
            gap: 1rem;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .session-row:hover {
            background: var(--hover-background);
        }

        .session-row:last-child {
            border-bottom: none;
        }

        .session-info {
            display: flex;
            flex-direction: column;
        }

        .session-id {
            font-family: monospace;
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .session-user {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .session-ip {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .session-device {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-icon {
            color: var(--text-secondary);
        }

        .device-name {
            font-size: 0.9rem;
        }

        .session-status {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            text-align: center;
        }

        .session-status.active {
            background: var(--success-color);
            color: white;
        }

        .session-status.expired {
            background: var(--error-color);
            color: white;
        }

        .session-status.idle {
            background: var(--warning-color);
            color: white;
        }

        .session-location {
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .session-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .session-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: var(--info-color);
            color: white;
        }

        .action-btn.terminate {
            background: var(--error-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .bulk-actions {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }

        .bulk-actions.show {
            display: block;
        }

        .bulk-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .session-checkbox {
            margin-right: 0.5rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: var(--hover-background);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .session-details-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: bold;
            color: var(--text-primary);
        }

        .detail-value {
            color: var(--text-secondary);
            font-family: monospace;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .table-header,
            .session-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .session-info {
                order: 1;
            }

            .session-actions {
                order: 2;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-list"></i>
                    <h1>Session List Testing</h1>
                    <span class="subtitle">Session Management and Monitoring Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sessions
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="sessions-container">
                <!-- Sessions Overview -->
                <div class="sessions-overview">
                    <h2>Session Overview</h2>
                    <div class="overview-stats">
                        <div class="stat-card">
                            <div class="stat-number active" id="activeSessions">0</div>
                            <div class="stat-label">Active Sessions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number expired" id="expiredSessions">0</div>
                            <div class="stat-label">Expired Sessions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number total" id="totalSessions">0</div>
                            <div class="stat-label">Total Sessions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="uniqueUsers">0</div>
                            <div class="stat-label">Unique Users</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="refreshSessions()">
                        <i class="fas fa-sync"></i>
                        Refresh Sessions
                    </button>
                    <button class="btn btn-warning" onclick="terminateExpiredSessions()">
                        <i class="fas fa-trash"></i>
                        Clear Expired
                    </button>
                    <button class="btn btn-danger" onclick="terminateAllSessions()">
                        <i class="fas fa-ban"></i>
                        Terminate All
                    </button>
                    <button class="btn btn-info" onclick="exportSessions()">
                        <i class="fas fa-download"></i>
                        Export Sessions
                    </button>
                </div>

                <!-- Session Controls -->
                <div class="sessions-controls">
                    <div class="controls-row">
                        <input type="text" class="search-box" id="sessionSearch" placeholder="Search sessions by user, IP, or session ID..." onkeyup="filterSessions()">
                        <select class="filter-select" id="statusFilter" onchange="filterSessions()">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="expired">Expired</option>
                            <option value="idle">Idle</option>
                        </select>
                        <select class="filter-select" id="deviceFilter" onchange="filterSessions()">
                            <option value="all">All Devices</option>
                            <option value="desktop">Desktop</option>
                            <option value="mobile">Mobile</option>
                            <option value="tablet">Tablet</option>
                        </select>
                        <button class="btn btn-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="bulk-controls">
                        <span id="selectedCount">0 sessions selected</span>
                        <button class="btn btn-sm btn-danger" onclick="terminateSelectedSessions()">
                            <i class="fas fa-ban"></i>
                            Terminate Selected
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                            <i class="fas fa-times"></i>
                            Clear Selection
                        </button>
                    </div>
                </div>

                <!-- Sessions Table -->
                <div class="sessions-table">
                    <div class="table-header">
                        <div>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            Session Info
                        </div>
                        <div>Device</div>
                        <div>Status</div>
                        <div>Location</div>
                        <div>Last Activity</div>
                        <div>Actions</div>
                    </div>
                    <div id="sessionsTableBody">
                        <!-- Session rows will be populated here -->
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button class="pagination-btn" id="prevBtn" onclick="previousPage()" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <div class="pagination-info" id="paginationInfo">
                        Page 1 of 1 (0 sessions)
                    </div>
                    <button class="pagination-btn" id="nextBtn" onclick="nextPage()" disabled>
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- Session Details Modal -->
                <div class="session-details-modal" id="sessionModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title">Session Details</div>
                            <button class="close-btn" onclick="closeSessionModal()">&times;</button>
                        </div>
                        <div id="sessionDetails">
                            <!-- Session details will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="sessionsResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Session List Testing Interface</p>
                    <p>Session management and monitoring testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let sessionsData = {
            sessions: [],
            filteredSessions: [],
            selectedSessions: new Set(),
            currentPage: 1,
            itemsPerPage: 10,
            totalPages: 1
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load sessions data
            await loadSessions();
        }

        // Session Management Functions
        async function loadSessions() {
            window.notificationManager.info('Loading sessions...');

            try {
                const response = await window.apiClient.request('GET', '/sessions');

                if (response.success) {
                    sessionsData.sessions = response.data.sessions || [];
                    updateSessionsDisplay();
                    window.notificationManager.success('Sessions loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load sessions');
                }

                showResponse('sessionsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Load mock sessions data
                loadMockSessions();
                window.notificationManager.success('Sessions loaded successfully (simulated)');

                showResponse('sessionsResponse', {
                    success: true,
                    data: { sessions: sessionsData.sessions },
                    message: 'Mock sessions data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockSessions() {
            const mockSessions = [
                {
                    id: 'sess_1a2b3c4d5e6f',
                    userId: 'user_123',
                    userEmail: '<EMAIL>',
                    userName: 'John Doe',
                    ipAddress: '*************',
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    device: 'desktop',
                    deviceName: 'Windows Desktop',
                    location: 'New York, US',
                    status: 'active',
                    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    lastActivity: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
                    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24) // 24 hours from now
                },
                {
                    id: 'sess_2b3c4d5e6f7g',
                    userId: 'user_456',
                    userEmail: '<EMAIL>',
                    userName: 'Jane Smith',
                    ipAddress: '*********',
                    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
                    device: 'mobile',
                    deviceName: 'iPhone 13',
                    location: 'Los Angeles, US',
                    status: 'active',
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    lastActivity: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 22) // 22 hours from now
                },
                {
                    id: 'sess_3c4d5e6f7g8h',
                    userId: 'user_789',
                    userEmail: '<EMAIL>',
                    userName: 'Bob Wilson',
                    ipAddress: '***********',
                    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
                    device: 'desktop',
                    deviceName: 'MacBook Pro',
                    location: 'London, UK',
                    status: 'idle',
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
                    lastActivity: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
                    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 20) // 20 hours from now
                },
                {
                    id: 'sess_4d5e6f7g8h9i',
                    userId: 'user_101',
                    userEmail: '<EMAIL>',
                    userName: 'Alice Brown',
                    ipAddress: '************',
                    userAgent: 'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X)',
                    device: 'tablet',
                    deviceName: 'iPad Pro',
                    location: 'Sydney, AU',
                    status: 'expired',
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 25), // 25 hours ago
                    lastActivity: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    expiresAt: new Date(Date.now() - 1000 * 60 * 60) // 1 hour ago (expired)
                },
                {
                    id: 'sess_5e6f7g8h9i0j',
                    userId: 'user_202',
                    userEmail: '<EMAIL>',
                    userName: 'Charlie Davis',
                    ipAddress: '*************',
                    userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
                    device: 'desktop',
                    deviceName: 'Ubuntu Desktop',
                    location: 'Toronto, CA',
                    status: 'active',
                    createdAt: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                    lastActivity: new Date(Date.now() - 1000 * 60 * 2), // 2 minutes ago
                    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 23) // 23 hours from now
                }
            ];

            sessionsData.sessions = mockSessions;
            updateSessionsDisplay();
        }

        function updateSessionsDisplay() {
            // Apply filters
            filterSessions();

            // Update statistics
            updateSessionStats();

            // Update pagination
            updatePagination();

            // Render sessions table
            renderSessionsTable();
        }

        function updateSessionStats() {
            const stats = {
                active: 0,
                expired: 0,
                total: sessionsData.sessions.length,
                uniqueUsers: new Set()
            };

            sessionsData.sessions.forEach(session => {
                if (session.status === 'active') stats.active++;
                if (session.status === 'expired') stats.expired++;
                stats.uniqueUsers.add(session.userId);
            });

            document.getElementById('activeSessions').textContent = stats.active;
            document.getElementById('expiredSessions').textContent = stats.expired;
            document.getElementById('totalSessions').textContent = stats.total;
            document.getElementById('uniqueUsers').textContent = stats.uniqueUsers.size;
        }

        function filterSessions() {
            const searchTerm = document.getElementById('sessionSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const deviceFilter = document.getElementById('deviceFilter').value;

            sessionsData.filteredSessions = sessionsData.sessions.filter(session => {
                const matchesSearch = !searchTerm ||
                    session.userEmail.toLowerCase().includes(searchTerm) ||
                    session.userName.toLowerCase().includes(searchTerm) ||
                    session.ipAddress.includes(searchTerm) ||
                    session.id.toLowerCase().includes(searchTerm);

                const matchesStatus = statusFilter === 'all' || session.status === statusFilter;
                const matchesDevice = deviceFilter === 'all' || session.device === deviceFilter;

                return matchesSearch && matchesStatus && matchesDevice;
            });

            // Reset to first page when filtering
            sessionsData.currentPage = 1;
            updatePagination();
            renderSessionsTable();
        }

        function clearFilters() {
            document.getElementById('sessionSearch').value = '';
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('deviceFilter').value = 'all';
            filterSessions();
        }

        function updatePagination() {
            const totalItems = sessionsData.filteredSessions.length;
            sessionsData.totalPages = Math.ceil(totalItems / sessionsData.itemsPerPage);

            const startItem = (sessionsData.currentPage - 1) * sessionsData.itemsPerPage + 1;
            const endItem = Math.min(sessionsData.currentPage * sessionsData.itemsPerPage, totalItems);

            document.getElementById('paginationInfo').textContent =
                `Page ${sessionsData.currentPage} of ${sessionsData.totalPages} (${totalItems} sessions)`;

            document.getElementById('prevBtn').disabled = sessionsData.currentPage <= 1;
            document.getElementById('nextBtn').disabled = sessionsData.currentPage >= sessionsData.totalPages;
        }

        function previousPage() {
            if (sessionsData.currentPage > 1) {
                sessionsData.currentPage--;
                updatePagination();
                renderSessionsTable();
            }
        }

        function nextPage() {
            if (sessionsData.currentPage < sessionsData.totalPages) {
                sessionsData.currentPage++;
                updatePagination();
                renderSessionsTable();
            }
        }

        function renderSessionsTable() {
            const tableBody = document.getElementById('sessionsTableBody');
            const startIndex = (sessionsData.currentPage - 1) * sessionsData.itemsPerPage;
            const endIndex = startIndex + sessionsData.itemsPerPage;
            const sessionsToShow = sessionsData.filteredSessions.slice(startIndex, endIndex);

            if (sessionsToShow.length === 0) {
                tableBody.innerHTML = `
                    <div style="padding: 2rem; text-align: center; color: var(--text-secondary);">
                        No sessions found matching the current filters.
                    </div>
                `;
                return;
            }

            const sessionsHTML = sessionsToShow.map(session => `
                <div class="session-row">
                    <div class="session-info">
                        <input type="checkbox" class="session-checkbox" value="${session.id}" onchange="toggleSessionSelection('${session.id}')">
                        <div class="session-id">${session.id}</div>
                        <div class="session-user">${session.userName}</div>
                        <div class="session-ip">${session.ipAddress}</div>
                    </div>
                    <div class="session-device">
                        <i class="fas ${getDeviceIcon(session.device)} device-icon"></i>
                        <div class="device-name">${session.deviceName}</div>
                    </div>
                    <div class="session-status ${session.status}">${session.status.toUpperCase()}</div>
                    <div class="session-location">${session.location}</div>
                    <div class="session-time">${formatTimeAgo(session.lastActivity)}</div>
                    <div class="session-actions">
                        <button class="action-btn view" onclick="viewSessionDetails('${session.id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn terminate" onclick="terminateSession('${session.id}')" title="Terminate Session">
                            <i class="fas fa-ban"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            tableBody.innerHTML = sessionsHTML;
        }

        function getDeviceIcon(device) {
            switch (device) {
                case 'desktop': return 'fa-desktop';
                case 'mobile': return 'fa-mobile-alt';
                case 'tablet': return 'fa-tablet-alt';
                default: return 'fa-question';
            }
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        // Session Actions
        async function refreshSessions() {
            await loadSessions();
        }

        async function terminateSession(sessionId) {
            if (!confirm('Are you sure you want to terminate this session?')) {
                return;
            }

            window.notificationManager.info('Terminating session...');

            try {
                const response = await window.apiClient.request('DELETE', `/sessions/${sessionId}`);

                if (response.success) {
                    // Remove session from local data
                    sessionsData.sessions = sessionsData.sessions.filter(s => s.id !== sessionId);
                    updateSessionsDisplay();
                    window.notificationManager.success('Session terminated successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to terminate session');
                }

                showResponse('sessionsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate session termination
                sessionsData.sessions = sessionsData.sessions.filter(s => s.id !== sessionId);
                updateSessionsDisplay();
                window.notificationManager.success('Session terminated successfully (simulated)');

                showResponse('sessionsResponse', {
                    success: true,
                    message: 'Mock session termination successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function terminateExpiredSessions() {
            if (!confirm('Are you sure you want to terminate all expired sessions?')) {
                return;
            }

            window.notificationManager.info('Terminating expired sessions...');

            try {
                const response = await window.apiClient.request('DELETE', '/sessions/expired');

                if (response.success) {
                    // Remove expired sessions from local data
                    const expiredCount = sessionsData.sessions.filter(s => s.status === 'expired').length;
                    sessionsData.sessions = sessionsData.sessions.filter(s => s.status !== 'expired');
                    updateSessionsDisplay();
                    window.notificationManager.success(`${expiredCount} expired sessions terminated`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to terminate expired sessions');
                }

                showResponse('sessionsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate expired session cleanup
                const expiredCount = sessionsData.sessions.filter(s => s.status === 'expired').length;
                sessionsData.sessions = sessionsData.sessions.filter(s => s.status !== 'expired');
                updateSessionsDisplay();
                window.notificationManager.success(`${expiredCount} expired sessions terminated (simulated)`);

                showResponse('sessionsResponse', {
                    success: true,
                    data: { terminatedCount: expiredCount },
                    message: 'Mock expired session cleanup successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function terminateAllSessions() {
            if (!confirm('Are you sure you want to terminate ALL sessions? This will log out all users!')) {
                return;
            }

            window.notificationManager.warning('Terminating all sessions...');

            try {
                const response = await window.apiClient.request('DELETE', '/sessions/all');

                if (response.success) {
                    const totalCount = sessionsData.sessions.length;
                    sessionsData.sessions = [];
                    updateSessionsDisplay();
                    window.notificationManager.success(`All ${totalCount} sessions terminated`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to terminate all sessions');
                }

                showResponse('sessionsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate all session termination
                const totalCount = sessionsData.sessions.length;
                sessionsData.sessions = [];
                updateSessionsDisplay();
                window.notificationManager.success(`All ${totalCount} sessions terminated (simulated)`);

                showResponse('sessionsResponse', {
                    success: true,
                    data: { terminatedCount: totalCount },
                    message: 'Mock all session termination successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        function exportSessions() {
            window.notificationManager.info('Exporting sessions data...');

            const exportData = {
                timestamp: new Date().toISOString(),
                totalSessions: sessionsData.sessions.length,
                activeSessions: sessionsData.sessions.filter(s => s.status === 'active').length,
                expiredSessions: sessionsData.sessions.filter(s => s.status === 'expired').length,
                sessions: sessionsData.sessions
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sessions-export-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            window.notificationManager.success('Sessions data exported successfully');
        }

        // Selection Management
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const sessionCheckboxes = document.querySelectorAll('.session-checkbox');

            sessionCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    sessionsData.selectedSessions.add(checkbox.value);
                } else {
                    sessionsData.selectedSessions.delete(checkbox.value);
                }
            });

            updateBulkActions();
        }

        function toggleSessionSelection(sessionId) {
            if (sessionsData.selectedSessions.has(sessionId)) {
                sessionsData.selectedSessions.delete(sessionId);
            } else {
                sessionsData.selectedSessions.add(sessionId);
            }

            updateBulkActions();
        }

        function updateBulkActions() {
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            const count = sessionsData.selectedSessions.size;

            if (count > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = `${count} session${count === 1 ? '' : 's'} selected`;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        function clearSelection() {
            sessionsData.selectedSessions.clear();
            document.getElementById('selectAll').checked = false;
            document.querySelectorAll('.session-checkbox').forEach(cb => cb.checked = false);
            updateBulkActions();
        }

        async function terminateSelectedSessions() {
            const selectedIds = Array.from(sessionsData.selectedSessions);
            if (selectedIds.length === 0) return;

            if (!confirm(`Are you sure you want to terminate ${selectedIds.length} selected session${selectedIds.length === 1 ? '' : 's'}?`)) {
                return;
            }

            window.notificationManager.info(`Terminating ${selectedIds.length} selected sessions...`);

            try {
                const response = await window.apiClient.request('DELETE', '/sessions/bulk', {
                    sessionIds: selectedIds
                });

                if (response.success) {
                    // Remove selected sessions from local data
                    sessionsData.sessions = sessionsData.sessions.filter(s => !selectedIds.includes(s.id));
                    clearSelection();
                    updateSessionsDisplay();
                    window.notificationManager.success(`${selectedIds.length} sessions terminated successfully`);
                } else {
                    window.notificationManager.error(response.error || 'Failed to terminate selected sessions');
                }

                showResponse('sessionsResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate bulk session termination
                sessionsData.sessions = sessionsData.sessions.filter(s => !selectedIds.includes(s.id));
                clearSelection();
                updateSessionsDisplay();
                window.notificationManager.success(`${selectedIds.length} sessions terminated successfully (simulated)`);

                showResponse('sessionsResponse', {
                    success: true,
                    data: { terminatedCount: selectedIds.length },
                    message: 'Mock bulk session termination successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Session Details Modal
        function viewSessionDetails(sessionId) {
            const session = sessionsData.sessions.find(s => s.id === sessionId);
            if (!session) return;

            const modal = document.getElementById('sessionModal');
            const detailsContainer = document.getElementById('sessionDetails');

            const detailsHTML = `
                <div class="detail-item">
                    <div class="detail-label">Session ID:</div>
                    <div class="detail-value">${session.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">User:</div>
                    <div class="detail-value">${session.userName} (${session.userEmail})</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">IP Address:</div>
                    <div class="detail-value">${session.ipAddress}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">User Agent:</div>
                    <div class="detail-value">${session.userAgent}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Device:</div>
                    <div class="detail-value">${session.deviceName} (${session.device})</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Location:</div>
                    <div class="detail-value">${session.location}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">${session.status.toUpperCase()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Created:</div>
                    <div class="detail-value">${session.createdAt.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Last Activity:</div>
                    <div class="detail-value">${session.lastActivity.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Expires:</div>
                    <div class="detail-value">${session.expiresAt.toLocaleString()}</div>
                </div>
            `;

            detailsContainer.innerHTML = detailsHTML;
            modal.style.display = 'flex';
        }

        function closeSessionModal() {
            document.getElementById('sessionModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('sessionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSessionModal();
            }
        });

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
