<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Tracking Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .activity-container {
            max-width: 1400px;
            margin: 2rem auto;
        }

        .activity-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .activity-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .activity-stat {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .activity-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .activity-stat.users::before {
            background: var(--primary-color);
        }

        .activity-stat.sessions::before {
            background: var(--success-color);
        }

        .activity-stat.actions::before {
            background: var(--info-color);
        }

        .activity-stat.locations::before {
            background: var(--warning-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .activity-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .activity-section {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .activity-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text-primary);
            min-width: 120px;
        }

        .activity-timeline {
            max-height: 600px;
            overflow-y: auto;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 25px;
            top: 50px;
            width: 2px;
            height: calc(100% - 50px);
            background: var(--border-color);
        }

        .timeline-item:last-child::before {
            display: none;
        }

        .timeline-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
            font-size: 1.2rem;
            position: relative;
            z-index: 1;
        }

        .timeline-icon.login {
            background: rgba(var(--success-color-rgb), 0.2);
            color: var(--success-color);
        }

        .timeline-icon.logout {
            background: rgba(var(--warning-color-rgb), 0.2);
            color: var(--warning-color);
        }

        .timeline-icon.action {
            background: rgba(var(--info-color-rgb), 0.2);
            color: var(--info-color);
        }

        .timeline-icon.security {
            background: rgba(var(--error-color-rgb), 0.2);
            color: var(--error-color);
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .timeline-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .timeline-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .timeline-time {
            color: var(--primary-color);
            font-weight: bold;
            margin-left: 1rem;
            flex-shrink: 0;
        }

        .user-activity-panel {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .user-search {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .user-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .user-item:hover {
            background: var(--hover-background);
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .user-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .user-status.online {
            background: var(--success-color);
            color: white;
        }

        .user-status.offline {
            background: var(--text-secondary);
            color: white;
        }

        .activity-chart {
            background: var(--hover-background);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .chart-placeholder {
            height: 200px;
            background: var(--card-background);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .location-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .location-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .location-item:last-child {
            border-bottom: none;
        }

        .location-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .location-flag {
            width: 20px;
            height: 15px;
            background: var(--primary-color);
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.7rem;
        }

        .location-name {
            font-weight: bold;
        }

        .location-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 1024px) {
            .activity-sections {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .activity-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .activity-filters {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <h1>Activity Tracking Testing</h1>
                    <span class="subtitle">User Activity and Session Tracking Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sessions
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="activity-container">
                <!-- Activity Overview -->
                <div class="activity-overview">
                    <h2>Activity Tracking Overview</h2>
                    <div class="activity-stats">
                        <div class="activity-stat users">
                            <div class="stat-number" id="activeUsers">0</div>
                            <div class="stat-label">Active Users</div>
                        </div>
                        <div class="activity-stat sessions">
                            <div class="stat-number" id="activeSessions">0</div>
                            <div class="stat-label">Active Sessions</div>
                        </div>
                        <div class="activity-stat actions">
                            <div class="stat-number" id="totalActions">0</div>
                            <div class="stat-label">Actions Today</div>
                        </div>
                        <div class="activity-stat locations">
                            <div class="stat-number" id="uniqueLocations">0</div>
                            <div class="stat-label">Unique Locations</div>
                        </div>
                    </div>
                </div>

                <!-- Activity Sections -->
                <div class="activity-sections">
                    <!-- Activity Timeline -->
                    <div class="activity-section">
                        <div class="section-header">
                            <div class="section-title">Activity Timeline</div>
                            <button class="btn btn-sm btn-primary" onclick="refreshActivity()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="activity-filters">
                            <select class="filter-select" id="userFilter" onchange="filterActivity()">
                                <option value="all">All Users</option>
                                <option value="<EMAIL>">John Doe</option>
                                <option value="<EMAIL>">Jane Smith</option>
                                <option value="<EMAIL>">Bob Wilson</option>
                            </select>
                            <select class="filter-select" id="actionFilter" onchange="filterActivity()">
                                <option value="all">All Actions</option>
                                <option value="login">Login</option>
                                <option value="logout">Logout</option>
                                <option value="action">User Action</option>
                                <option value="security">Security Event</option>
                            </select>
                            <select class="filter-select" id="timeFilter" onchange="filterActivity()">
                                <option value="1h">Last Hour</option>
                                <option value="24h">Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                            </select>
                        </div>
                        <div class="activity-timeline" id="activityTimeline">
                            <!-- Timeline items will be populated here -->
                        </div>
                    </div>

                    <!-- User Activity Panel -->
                    <div class="activity-section">
                        <div class="section-header">
                            <div class="section-title">User Activity</div>
                        </div>

                        <!-- User Search -->
                        <div class="user-activity-panel">
                            <input type="text" class="user-search" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                            <div class="user-list" id="userList">
                                <!-- Users will be populated here -->
                            </div>
                        </div>

                        <!-- Activity Chart -->
                        <div class="activity-chart">
                            <h4>Activity Trends</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-area" style="margin-right: 0.5rem;"></i>
                                Activity Chart (24 Hours)
                            </div>
                        </div>

                        <!-- Top Locations -->
                        <div>
                            <h4>Top Locations</h4>
                            <div class="location-list" id="locationList">
                                <!-- Locations will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="activity-section">
                    <div class="section-header">
                        <div class="section-title">Quick Actions</div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <button class="btn btn-primary" onclick="exportActivityReport()">
                            <i class="fas fa-download"></i>
                            Export Activity Report
                        </button>
                        <button class="btn btn-info" onclick="viewDetailedAnalytics()">
                            <i class="fas fa-chart-bar"></i>
                            Detailed Analytics
                        </button>
                        <button class="btn btn-warning" onclick="flagSuspiciousActivity()">
                            <i class="fas fa-flag"></i>
                            Flag Suspicious Activity
                        </button>
                        <button class="btn btn-secondary" onclick="clearActivityLogs()">
                            <i class="fas fa-trash"></i>
                            Clear Old Logs
                        </button>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="activityResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Activity Tracking Testing Interface</p>
                    <p>User activity and session tracking testing</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let activityData = {
            activities: [],
            filteredActivities: [],
            users: [],
            locations: [],
            stats: {
                activeUsers: 0,
                activeSessions: 0,
                totalActions: 0,
                uniqueLocations: 0
            },
            filters: {
                user: 'all',
                action: 'all',
                time: '24h'
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await checkServerHealth();
            updateAuthStatus();
            initializeTheme();
            await loadActivityData();
            startRealTimeUpdates();
        }

        // Activity Data Functions
        async function loadActivityData() {
            window.notificationManager.info('Loading activity data...');

            try {
                const response = await window.apiClient.request('GET', '/sessions/activity');

                if (response.success) {
                    updateActivityData(response.data);
                    window.notificationManager.success('Activity data loaded successfully');
                } else {
                    window.notificationManager.error(response.error || 'Failed to load activity data');
                }

                showResponse('activityResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                loadMockActivityData();
                window.notificationManager.success('Activity data loaded successfully (simulated)');

                showResponse('activityResponse', {
                    success: true,
                    data: activityData,
                    message: 'Mock activity data loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function loadMockActivityData() {
            const mockActivities = [
                {
                    id: 'activity_001',
                    type: 'login',
                    title: 'User Login',
                    description: 'John Doe logged in from Chrome on Windows',
                    user: '<EMAIL>',
                    userAvatar: 'JD',
                    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
                    location: 'New York, USA',
                    ip: '*************',
                    device: 'Chrome on Windows'
                },
                {
                    id: 'activity_002',
                    type: 'action',
                    title: 'Profile Updated',
                    description: 'Jane Smith updated her profile information',
                    user: '<EMAIL>',
                    userAvatar: 'JS',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    location: 'London, UK',
                    ip: '************',
                    device: 'Safari on macOS'
                },
                {
                    id: 'activity_003',
                    type: 'security',
                    title: 'Failed Login Attempt',
                    description: 'Multiple failed login attempts detected',
                    user: '<EMAIL>',
                    userAvatar: 'BW',
                    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
                    location: 'Tokyo, Japan',
                    ip: '*************',
                    device: 'Firefox on Linux'
                },
                {
                    id: 'activity_004',
                    type: 'logout',
                    title: 'User Logout',
                    description: 'Alice Brown logged out',
                    user: '<EMAIL>',
                    userAvatar: 'AB',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                    location: 'Sydney, Australia',
                    ip: '***********',
                    device: 'Edge on Windows'
                },
                {
                    id: 'activity_005',
                    type: 'action',
                    title: 'Data Export',
                    description: 'Charlie Davis exported user data',
                    user: '<EMAIL>',
                    userAvatar: 'CD',
                    timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
                    location: 'Toronto, Canada',
                    ip: '*********',
                    device: 'Chrome on macOS'
                }
            ];

            const mockUsers = [
                { email: '<EMAIL>', name: 'John Doe', avatar: 'JD', status: 'online', lastActivity: new Date() },
                { email: '<EMAIL>', name: 'Jane Smith', avatar: 'JS', status: 'online', lastActivity: new Date(Date.now() - 1000 * 60 * 30) },
                { email: '<EMAIL>', name: 'Bob Wilson', avatar: 'BW', status: 'offline', lastActivity: new Date(Date.now() - 1000 * 60 * 45) },
                { email: '<EMAIL>', name: 'Alice Brown', avatar: 'AB', status: 'offline', lastActivity: new Date(Date.now() - 1000 * 60 * 60) },
                { email: '<EMAIL>', name: 'Charlie Davis', avatar: 'CD', status: 'online', lastActivity: new Date(Date.now() - 1000 * 60 * 90) }
            ];

            const mockLocations = [
                { name: 'New York, USA', flag: 'US', count: 45 },
                { name: 'London, UK', flag: 'GB', count: 32 },
                { name: 'Tokyo, Japan', flag: 'JP', count: 28 },
                { name: 'Sydney, Australia', flag: 'AU', count: 18 },
                { name: 'Toronto, Canada', flag: 'CA', count: 15 }
            ];

            activityData.activities = mockActivities;
            activityData.users = mockUsers;
            activityData.locations = mockLocations;
            activityData.stats = {
                activeUsers: mockUsers.filter(u => u.status === 'online').length,
                activeSessions: 23,
                totalActions: 156,
                uniqueLocations: mockLocations.length
            };

            updateActivityDisplay();
        }

        function updateActivityData(data) {
            if (data.activities) activityData.activities = data.activities;
            if (data.users) activityData.users = data.users;
            if (data.locations) activityData.locations = data.locations;
            if (data.stats) activityData.stats = { ...activityData.stats, ...data.stats };
            updateActivityDisplay();
        }

        function updateActivityDisplay() {
            updateActivityStats();
            filterActivity();
            updateUserList();
            updateLocationList();
        }

        function updateActivityStats() {
            document.getElementById('activeUsers').textContent = activityData.stats.activeUsers;
            document.getElementById('activeSessions').textContent = activityData.stats.activeSessions;
            document.getElementById('totalActions').textContent = activityData.stats.totalActions;
            document.getElementById('uniqueLocations').textContent = activityData.stats.uniqueLocations;
        }

        function filterActivity() {
            const userFilter = document.getElementById('userFilter').value;
            const actionFilter = document.getElementById('actionFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;

            activityData.filters = { user: userFilter, action: actionFilter, time: timeFilter };

            let filtered = activityData.activities;

            // Filter by user
            if (userFilter !== 'all') {
                filtered = filtered.filter(activity => activity.user === userFilter);
            }

            // Filter by action type
            if (actionFilter !== 'all') {
                filtered = filtered.filter(activity => activity.type === actionFilter);
            }

            // Filter by time
            const now = new Date();
            const timeFilters = {
                '1h': 1000 * 60 * 60,
                '24h': 1000 * 60 * 60 * 24,
                '7d': 1000 * 60 * 60 * 24 * 7,
                '30d': 1000 * 60 * 60 * 24 * 30
            };

            if (timeFilters[timeFilter]) {
                const cutoff = now - timeFilters[timeFilter];
                filtered = filtered.filter(activity => activity.timestamp >= cutoff);
            }

            activityData.filteredActivities = filtered;
            updateActivityTimeline();
        }

        function updateActivityTimeline() {
            const timeline = document.getElementById('activityTimeline');

            if (activityData.filteredActivities.length === 0) {
                timeline.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        No activities found for the selected filters.
                    </div>
                `;
                return;
            }

            const activitiesHTML = activityData.filteredActivities
                .sort((a, b) => b.timestamp - a.timestamp)
                .map(activity => `
                    <div class="timeline-item">
                        <div class="timeline-icon ${activity.type}">
                            <i class="fas ${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">${activity.title}</div>
                            <div class="timeline-description">${activity.description}</div>
                            <div class="timeline-meta">
                                <span><i class="fas fa-user"></i> ${activity.user}</span>
                                <span><i class="fas fa-map-marker-alt"></i> ${activity.location}</span>
                                <span><i class="fas fa-desktop"></i> ${activity.device}</span>
                            </div>
                        </div>
                        <div class="timeline-time">${formatTimeAgo(activity.timestamp)}</div>
                    </div>
                `).join('');

            timeline.innerHTML = activitiesHTML;
        }

        function updateUserList() {
            const userList = document.getElementById('userList');

            const usersHTML = activityData.users.map(user => `
                <div class="user-item" onclick="selectUser('${user.email}')">
                    <div class="user-avatar">${user.avatar}</div>
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="user-email">${user.email}</div>
                    </div>
                    <div class="user-status ${user.status}">${user.status.toUpperCase()}</div>
                </div>
            `).join('');

            userList.innerHTML = usersHTML;
        }

        function updateLocationList() {
            const locationList = document.getElementById('locationList');

            const locationsHTML = activityData.locations.map(location => `
                <div class="location-item">
                    <div class="location-info">
                        <div class="location-flag">${location.flag}</div>
                        <div class="location-name">${location.name}</div>
                    </div>
                    <div class="location-count">${location.count} users</div>
                </div>
            `).join('');

            locationList.innerHTML = locationsHTML;
        }

        // User Interaction Functions
        function selectUser(email) {
            document.getElementById('userFilter').value = email;
            filterActivity();
            window.notificationManager.info(`Filtered activities for ${email}`);
        }

        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const filteredUsers = activityData.users.filter(user =>
                user.name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm)
            );

            const userList = document.getElementById('userList');
            const usersHTML = filteredUsers.map(user => `
                <div class="user-item" onclick="selectUser('${user.email}')">
                    <div class="user-avatar">${user.avatar}</div>
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="user-email">${user.email}</div>
                    </div>
                    <div class="user-status ${user.status}">${user.status.toUpperCase()}</div>
                </div>
            `).join('');

            userList.innerHTML = usersHTML;
        }

        // Quick Actions
        function exportActivityReport() {
            window.notificationManager.info('Generating activity report...');
            const reportData = {
                timestamp: new Date().toISOString(),
                filters: activityData.filters,
                activities: activityData.filteredActivities,
                stats: activityData.stats
            };
            const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activity-report-${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);
            window.notificationManager.success('Activity report exported successfully');
        }

        function viewDetailedAnalytics() {
            window.notificationManager.info('Opening detailed analytics dashboard...');
            setTimeout(() => {
                window.notificationManager.success('Detailed analytics would open in a new window');
            }, 1000);
        }

        function flagSuspiciousActivity() {
            window.notificationManager.warning('Flagging suspicious activities for review...');
            setTimeout(() => {
                window.notificationManager.success('3 suspicious activities flagged for manual review');
            }, 1500);
        }

        function clearActivityLogs() {
            if (confirm('Are you sure you want to clear old activity logs? This action cannot be undone.')) {
                window.notificationManager.info('Clearing activity logs older than 30 days...');
                setTimeout(() => {
                    window.notificationManager.success('Old activity logs cleared successfully');
                }, 2000);
            }
        }

        function refreshActivity() {
            loadActivityData();
        }

        // Utility Functions
        function getActivityIcon(type) {
            switch (type) {
                case 'login': return 'fa-sign-in-alt';
                case 'logout': return 'fa-sign-out-alt';
                case 'action': return 'fa-cog';
                case 'security': return 'fa-shield-alt';
                default: return 'fa-info';
            }
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        function startRealTimeUpdates() {
            setInterval(() => {
                if (Math.random() < 0.2) {
                    // Simulate new activity
                    const activityTypes = ['login', 'logout', 'action', 'security'];
                    const users = activityData.users;
                    const randomUser = users[Math.floor(Math.random() * users.length)];
                    const randomType = activityTypes[Math.floor(Math.random() * activityTypes.length)];

                    const newActivity = {
                        id: 'activity_' + Date.now(),
                        type: randomType,
                        title: `${randomType.charAt(0).toUpperCase() + randomType.slice(1)} Event`,
                        description: `${randomUser.name} performed a ${randomType} action`,
                        user: randomUser.email,
                        userAvatar: randomUser.avatar,
                        timestamp: new Date(),
                        location: 'New York, USA',
                        ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
                        device: 'Chrome on Windows'
                    };

                    activityData.activities.unshift(newActivity);
                    if (activityData.activities.length > 50) {
                        activityData.activities = activityData.activities.slice(0, 50);
                    }

                    activityData.stats.totalActions++;
                    updateActivityDisplay();
                }
            }, 15000); // Every 15 seconds
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else throw new Error('Server error');
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');
                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');
            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');
            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
