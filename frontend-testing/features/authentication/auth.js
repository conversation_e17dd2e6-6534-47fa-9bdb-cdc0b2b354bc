/* global document, window, FormData, Utils, setTimeout, fetch, localStorage */

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupTabs();
    setupForms();
    setupPasswordStrength();
});

async function initializeApp() {
    // Check server health
    await checkServerHealth();

    // Check authentication status
    updateAuthStatus();

    // Initialize theme
    initializeTheme();

    // Load profile if authenticated
    if (window.authManager.isAuthenticated()) {
        loadProfile();
    }
}

function setupTabs() {
    const tabs = document.querySelectorAll('.auth-tab');
    const contents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('active'));
            contents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab
            tab.classList.add('active');

            // Show corresponding content
            const tabId = tab.dataset.tab + '-tab';
            const content = document.getElementById(tabId);
            if (content) {
                content.classList.add('active');
            }
        });
    });
}

function setupForms() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    loginForm.addEventListener('submit', handleLogin);

    // Register form
    const registerForm = document.getElementById('registerForm');
    registerForm.addEventListener('submit', handleRegister);

    // Profile form
    const profileForm = document.getElementById('profileForm');
    profileForm.addEventListener('submit', handleProfileUpdate);

    // Password form
    const passwordForm = document.getElementById('passwordForm');
    passwordForm.addEventListener('submit', handlePasswordChange);

    // Forgot password form
    const forgotForm = document.getElementById('forgotForm');
    forgotForm.addEventListener('submit', handleForgotPassword);

    // Reset password form
    const resetForm = document.getElementById('resetForm');
    resetForm.addEventListener('submit', handleResetPassword);

    // Resend verification form
    const resendForm = document.getElementById('resendVerificationForm');
    resendForm.addEventListener('submit', handleResendVerification);

    // Verify token form
    const verifyForm = document.getElementById('verifyTokenForm');
    verifyForm.addEventListener('submit', handleVerifyEmail);

    // Permission form
    const permissionForm = document.getElementById('permissionForm');
    permissionForm.addEventListener('submit', handleCheckPermission);
}

function setupPasswordStrength() {
    const passwordInputs = ['registerPassword', 'newPassword'];

    passwordInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        const strengthId = inputId === 'registerPassword' ? 'passwordStrength' : 'newPasswordStrength';
        const strengthElement = document.getElementById(strengthId);

        if (input && strengthElement) {
            input.addEventListener('input', () => {
                const strength = Utils.getPasswordStrength(input.value);
                updatePasswordStrength(strengthElement, strength);
            });
        }
    });
}

function updatePasswordStrength(element, strength) {
    const fill = element.querySelector('.strength-fill');
    const text = element.querySelector('.strength-text');

    if (strength.score === 0) {
        element.style.display = 'none';
        return;
    }

    element.style.display = 'block';
    fill.style.width = strength.percentage + '%';
    text.textContent = strength.level;

    // Color based on strength
    const colors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];
    fill.style.backgroundColor = colors[strength.score - 1] || colors[0];
}

// Form handlers
async function handleLogin(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('loginResponse');

    try {
        const response = await window.authManager.login({
            email: data.email,
            password: data.password
        });

        if (response.requiresMFA) {
            showResponse('loginResponse', {
                success: true,
                message: 'MFA required. Please complete MFA verification.',
                data: { mfaToken: response.mfaToken }
            }, 'success');

            // Redirect to MFA page
            setTimeout(() => {
                window.location.href = '../mfa/index.html';
            }, 2000);
        } else {
            showResponse('loginResponse', {
                success: true,
                message: 'Login successful!',
                data: response
            }, 'success');

            updateAuthStatus();
            loadProfile();
        }
    } catch (error) {
        showResponse('loginResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Validate passwords match
    if (data.password !== data.confirmPassword) {
        window.notificationManager.error('Passwords do not match');
        return;
    }

    showLoading(form);
    clearResponse('registerResponse');

    try {
        const response = await window.authManager.register({
            email: data.email,
            username: data.username,
            firstName: data.firstName,
            lastName: data.lastName,
            password: data.password
        });

        showResponse('registerResponse', response, 'success');
        form.reset();
    } catch (error) {
        showResponse('registerResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleProfileUpdate(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('profileResponse');

    try {
        const response = await window.authManager.updateProfile({
            firstName: data.firstName,
            lastName: data.lastName,
            bio: data.bio
        });

        showResponse('profileResponse', response, 'success');
        updateAuthStatus();
    } catch (error) {
        showResponse('profileResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handlePasswordChange(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Validate passwords match
    if (data.newPassword !== data.confirmNewPassword) {
        window.notificationManager.error('New passwords do not match');
        return;
    }

    showLoading(form);
    clearResponse('passwordResponse');

    try {
        const response = await window.authManager.changePassword(
            data.currentPassword,
            data.newPassword
        );

        showResponse('passwordResponse', response, 'success');
        form.reset();
    } catch (error) {
        showResponse('passwordResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

// Utility functions
async function loadProfile() {
    if (!window.authManager.isAuthenticated()) {
        window.notificationManager.warning('Please log in first');
        return;
    }

    try {
        const response = await window.apiClient.auth.getProfile();
        if (response.success) {
            const user = response.data.user;
            document.getElementById('profileEmail').value = user.email || '';
            document.getElementById('profileUsername').value = user.username || '';
            document.getElementById('profileFirstName').value = user.firstName || '';
            document.getElementById('profileLastName').value = user.lastName || '';
            document.getElementById('profileBio').value = user.bio || '';

            showResponse('profileResponse', response, 'success');
        }
    } catch (error) {
        showResponse('profileResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    }
}

async function loadPermissions() {
    if (!window.authManager.isAuthenticated()) {
        window.notificationManager.warning('Please log in first');
        return;
    }

    try {
        const permissions = await window.authManager.getPermissions();
        showResponse('permissionsResponse', {
            success: true,
            message: 'User permissions loaded',
            data: { permissions }
        }, 'success');
    } catch (error) {
        showResponse('permissionsResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    }
}

// Quick test functions
function fillTestUser() {
    document.getElementById('loginEmail').value = '<EMAIL>';
    document.getElementById('loginPassword').value = 'TestPassword123!';
}

function fillAdminUser() {
    document.getElementById('loginEmail').value = '<EMAIL>';
    document.getElementById('loginPassword').value = 'AdminPassword123!';
}

function fillTestRegistration() {
    const timestamp = Date.now();
    document.getElementById('registerEmail').value = `test${timestamp}@example.com`;
    document.getElementById('registerUsername').value = `testuser${timestamp}`;
    document.getElementById('registerFirstName').value = 'Test';
    document.getElementById('registerLastName').value = 'User';
    document.getElementById('registerPassword').value = 'TestPassword123!';
    document.getElementById('registerConfirmPassword').value = 'TestPassword123!';
    document.getElementById('agreeTerms').checked = true;
}

// Clear form functions
function clearLoginForm() {
    document.getElementById('loginForm').reset();
    clearResponse('loginResponse');
}

function clearRegisterForm() {
    document.getElementById('registerForm').reset();
    clearResponse('registerResponse');
}

function clearProfileForm() {
    document.getElementById('profileForm').reset();
    clearResponse('profileResponse');
}

// UI utility functions
function showLoading(form) {
    const button = form.querySelector('button[type="submit"]');
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    }
}

function hideLoading(form) {
    const button = form.querySelector('button[type="submit"]');
    if (button) {
        button.disabled = false;
        // Restore original button text based on form
        const formId = form.id;
        const buttonTexts = {
            'loginForm': '<i class="fas fa-sign-in-alt"></i> Login',
            'registerForm': '<i class="fas fa-user-plus"></i> Register',
            'profileForm': '<i class="fas fa-save"></i> Update Profile',
            'passwordForm': '<i class="fas fa-key"></i> Change Password',
            'forgotForm': '<i class="fas fa-paper-plane"></i> Send Reset Link',
            'resetForm': '<i class="fas fa-check"></i> Reset Password',
            'resendVerificationForm': '<i class="fas fa-paper-plane"></i> Resend Verification',
            'verifyTokenForm': '<i class="fas fa-check-circle"></i> Verify Email',
            'permissionForm': '<i class="fas fa-check"></i> Check Permission'
        };
        button.innerHTML = buttonTexts[formId] || 'Submit';
    }
}

function showResponse(elementId, response, type = 'info') {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'block';
        element.innerHTML = Utils.highlightJSON(response);
        element.className = `response-viewer ${type}`;
    }
}

function clearResponse(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
        element.innerHTML = '';
    }
}

// Shared utility functions from main page
async function checkServerHealth() {
    try {
        const response = await fetch('http://localhost:3000/health');
        const data = await response.json();

        const statusElement = document.getElementById('serverStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        if (response.ok) {
            indicator.className = 'fas fa-circle status-indicator status-online';
            text.textContent = 'Server Online';
        } else {
            throw new Error('Server responded with error');
        }
    } catch (error) {
        const statusElement = document.getElementById('serverStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        indicator.className = 'fas fa-circle status-indicator status-offline';
        text.textContent = 'Server Offline';
    }
}

function updateAuthStatus() {
    const token = localStorage.getItem('auth_token');
    const user = JSON.parse(localStorage.getItem('user') || 'null');

    const authElement = document.getElementById('authStatus');
    const text = authElement.querySelector('.auth-text');

    if (token && user) {
        text.textContent = `Logged in as ${user.email}`;
        authElement.classList.add('authenticated');
    } else {
        text.textContent = 'Not Authenticated';
        authElement.classList.remove('authenticated');
    }
}

function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.body.setAttribute('data-theme', savedTheme);

    const themeToggle = document.getElementById('themeToggle');
    const icon = themeToggle.querySelector('i');

    if (savedTheme === 'dark') {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }

    themeToggle.addEventListener('click', toggleTheme);
}

function toggleTheme() {
    const currentTheme = document.body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    const icon = document.getElementById('themeToggle').querySelector('i');
    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

async function handleForgotPassword(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('forgotResponse');

    try {
        const response = await window.authManager.forgotPassword(data.email);
        showResponse('forgotResponse', response, 'success');
    } catch (error) {
        showResponse('forgotResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleResetPassword(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('forgotResponse');

    try {
        const response = await window.authManager.resetPassword(
            data.token,
            data.newPassword
        );

        showResponse('forgotResponse', response, 'success');
        form.reset();
    } catch (error) {
        showResponse('forgotResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleResendVerification(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('verifyResponse');

    try {
        const response = await window.authManager.resendEmailVerification(data.email);
        showResponse('verifyResponse', response, 'success');
    } catch (error) {
        showResponse('verifyResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleVerifyEmail(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('verifyResponse');

    try {
        const response = await window.authManager.verifyEmail(data.token);
        showResponse('verifyResponse', response, 'success');
        updateAuthStatus();
    } catch (error) {
        showResponse('verifyResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}

async function handleCheckPermission(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    showLoading(form);
    clearResponse('permissionsResponse');

    try {
        const hasPermission = await window.authManager.checkPermission(
            data.resource,
            data.action
        );

        showResponse('permissionsResponse', {
            success: true,
            message: `Permission check: ${data.resource}:${data.action}`,
            data: { hasPermission }
        }, hasPermission ? 'success' : 'error');
    } catch (error) {
        showResponse('permissionsResponse', {
            success: false,
            error: error.message,
            details: error.details
        }, 'error');
    } finally {
        hideLoading(form);
    }
}
