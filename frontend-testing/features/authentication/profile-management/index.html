<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 2rem auto;
        }

        .profile-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .profile-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .profile-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .profile-header {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--primary-color);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            position: relative;
            cursor: pointer;
        }

        .avatar-upload {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .profile-info {
            margin-bottom: 1rem;
        }

        .profile-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .profile-email {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .profile-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .profile-status.verified {
            background: var(--success-color);
            color: white;
        }

        .profile-status.unverified {
            background: var(--warning-color);
            color: white;
        }

        .profile-form {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .form-section h3 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .security-settings {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .security-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .security-item:last-child {
            border-bottom: none;
        }

        .security-info {
            flex: 1;
        }

        .security-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .security-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .security-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
        }

        .security-status.enabled {
            background: var(--success-color);
            color: white;
        }

        .security-status.disabled {
            background: var(--text-secondary);
            color: white;
        }

        .activity-log {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 2rem;
        }

        .activity-header {
            background: var(--hover-background);
            padding: 1rem;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-action {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .activity-details {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .danger-zone {
            background: rgba(var(--error-color-rgb), 0.1);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .danger-zone h3 {
            color: var(--error-color);
            margin-bottom: 1rem;
        }

        .danger-zone .btn-danger {
            background: var(--error-color);
            border-color: var(--error-color);
        }

        .input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
        }

        .password-toggle:hover {
            color: var(--text-primary);
        }

        .file-upload {
            display: none;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.2s ease;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user-cog"></i>
                    <h1>Profile Management Testing</h1>
                    <span class="subtitle">User Profile Management Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Auth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="profile-container">
                <!-- Profile Header -->
                <div class="profile-header" id="profileHeader">
                    <div class="profile-avatar" onclick="triggerAvatarUpload()">
                        <span id="avatarInitials">U</span>
                        <div class="avatar-upload">
                            <i class="fas fa-camera"></i>
                        </div>
                    </div>
                    <input type="file" id="avatarUpload" class="file-upload" accept="image/*" onchange="handleAvatarUpload(event)">
                    
                    <div class="profile-info">
                        <div class="profile-name" id="profileName">Test User</div>
                        <div class="profile-email" id="profileEmail"><EMAIL></div>
                        <span class="profile-status verified" id="profileStatus">Email Verified</span>
                    </div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-primary" onclick="loadUserProfile()">
                        <i class="fas fa-sync"></i>
                        Load Profile
                    </button>
                    <button class="btn btn-success" onclick="testProfileUpdate()">
                        <i class="fas fa-save"></i>
                        Test Update
                    </button>
                    <button class="btn btn-warning" onclick="testPasswordChange()">
                        <i class="fas fa-key"></i>
                        Test Password Change
                    </button>
                    <button class="btn btn-info" onclick="testEmailChange()">
                        <i class="fas fa-envelope"></i>
                        Test Email Change
                    </button>
                </div>

                <!-- Navigation Tabs -->
                <div class="profile-tabs">
                    <button class="profile-tab active" onclick="switchTab('personal')">
                        <i class="fas fa-user"></i>
                        Personal Info
                    </button>
                    <button class="profile-tab" onclick="switchTab('security')">
                        <i class="fas fa-shield-alt"></i>
                        Security
                    </button>
                    <button class="profile-tab" onclick="switchTab('preferences')">
                        <i class="fas fa-cog"></i>
                        Preferences
                    </button>
                    <button class="profile-tab" onclick="switchTab('activity')">
                        <i class="fas fa-history"></i>
                        Activity Log
                    </button>
                </div>

                <!-- Personal Info Tab -->
                <div class="tab-content active" id="personal-tab">
                    <div class="profile-form">
                        <form id="personalInfoForm">
                            <div class="form-section">
                                <h3>Basic Information</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="firstName">First Name:</label>
                                        <input type="text" id="firstName" class="form-control" placeholder="John">
                                    </div>
                                    <div class="form-group">
                                        <label for="lastName">Last Name:</label>
                                        <input type="text" id="lastName" class="form-control" placeholder="Doe">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="displayName">Display Name:</label>
                                    <input type="text" id="displayName" class="form-control" placeholder="John Doe">
                                </div>
                                <div class="form-group">
                                    <label for="bio">Bio:</label>
                                    <textarea id="bio" class="form-control" rows="3" placeholder="Tell us about yourself..."></textarea>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Contact Information</h3>
                                <div class="form-group">
                                    <label for="email">Email Address:</label>
                                    <input type="email" id="email" class="form-control" placeholder="<EMAIL>">
                                    <small class="form-text">Changing your email will require verification</small>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="phone">Phone Number:</label>
                                        <input type="tel" id="phone" class="form-control" placeholder="+****************">
                                    </div>
                                    <div class="form-group">
                                        <label for="timezone">Timezone:</label>
                                        <select id="timezone" class="form-control">
                                            <option value="UTC">UTC</option>
                                            <option value="America/New_York">Eastern Time</option>
                                            <option value="America/Chicago">Central Time</option>
                                            <option value="America/Denver">Mountain Time</option>
                                            <option value="America/Los_Angeles">Pacific Time</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Profile Picture</h3>
                                <div class="upload-area" onclick="triggerAvatarUpload()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                                    <p>Click to upload or drag and drop</p>
                                    <small>PNG, JPG up to 5MB</small>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Save Changes
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Security Tab -->
                <div class="tab-content" id="security-tab">
                    <div class="security-settings">
                        <h3>Security Settings</h3>

                        <div class="security-item">
                            <div class="security-info">
                                <div class="security-title">Two-Factor Authentication</div>
                                <div class="security-description">Add an extra layer of security to your account</div>
                            </div>
                            <div>
                                <span class="security-status disabled" id="mfaStatus">Disabled</span>
                                <button class="btn btn-sm btn-primary" onclick="toggleMFA()">
                                    <i class="fas fa-shield-alt"></i>
                                    Enable
                                </button>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-info">
                                <div class="security-title">Login Notifications</div>
                                <div class="security-description">Get notified when someone logs into your account</div>
                            </div>
                            <div>
                                <span class="security-status enabled">Enabled</span>
                                <button class="btn btn-sm btn-secondary" onclick="toggleLoginNotifications()">
                                    <i class="fas fa-bell-slash"></i>
                                    Disable
                                </button>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-info">
                                <div class="security-title">Session Management</div>
                                <div class="security-description">View and manage your active sessions</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-info" onclick="viewActiveSessions()">
                                    <i class="fas fa-list"></i>
                                    View Sessions
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="profile-form">
                        <form id="passwordChangeForm">
                            <div class="form-section">
                                <h3>Change Password</h3>
                                <div class="form-group">
                                    <label for="currentPassword">Current Password:</label>
                                    <div class="input-group">
                                        <input type="password" id="currentPassword" class="form-control" placeholder="Enter current password">
                                        <button type="button" class="password-toggle" onclick="togglePassword('currentPassword')">
                                            <i class="fas fa-eye" id="currentPasswordToggleIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="newPassword">New Password:</label>
                                    <div class="input-group">
                                        <input type="password" id="newPassword" class="form-control" placeholder="Enter new password">
                                        <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                                            <i class="fas fa-eye" id="newPasswordToggleIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="confirmPassword">Confirm New Password:</label>
                                    <div class="input-group">
                                        <input type="password" id="confirmPassword" class="form-control" placeholder="Confirm new password">
                                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                            <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key"></i>
                                    Change Password
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="danger-zone">
                        <h3>Danger Zone</h3>
                        <p>These actions are irreversible. Please be certain before proceeding.</p>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <button class="btn btn-danger" onclick="deactivateAccount()">
                                <i class="fas fa-user-slash"></i>
                                Deactivate Account
                            </button>
                            <button class="btn btn-danger" onclick="deleteAccount()">
                                <i class="fas fa-trash"></i>
                                Delete Account
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Preferences Tab -->
                <div class="tab-content" id="preferences-tab">
                    <div class="profile-form">
                        <form id="preferencesForm">
                            <div class="form-section">
                                <h3>Notification Preferences</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="emailNotifications" checked>
                                        Email Notifications
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="smsNotifications">
                                        SMS Notifications
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="pushNotifications" checked>
                                        Push Notifications
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="marketingEmails">
                                        Marketing Emails
                                    </label>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Privacy Settings</h3>
                                <div class="form-group">
                                    <label for="profileVisibility">Profile Visibility:</label>
                                    <select id="profileVisibility" class="form-control">
                                        <option value="public">Public</option>
                                        <option value="private">Private</option>
                                        <option value="friends">Friends Only</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="showEmail">
                                        Show email address on profile
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="showPhone">
                                        Show phone number on profile
                                    </label>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Application Settings</h3>
                                <div class="form-group">
                                    <label for="language">Language:</label>
                                    <select id="language" class="form-control">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="theme">Theme:</label>
                                    <select id="theme" class="form-control">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto</option>
                                    </select>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Save Preferences
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Activity Log Tab -->
                <div class="tab-content" id="activity-tab">
                    <div class="activity-log" id="activityLog">
                        <div class="activity-header">Recent Activity</div>
                        <!-- Activity items will be populated here -->
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Profile Update</div>
                        <div class="scenario-description">Test updating user profile information</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="fillValidProfileData()">
                                <i class="fas fa-user-edit"></i>
                                Fill Valid Data
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Email Change</div>
                        <div class="scenario-description">Test changing email address with verification</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-info" onclick="testEmailChangeFlow()">
                                <i class="fas fa-envelope"></i>
                                Test Email Change
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Password Security</div>
                        <div class="scenario-description">Test password change with various scenarios</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testPasswordSecurity()">
                                <i class="fas fa-key"></i>
                                Test Password
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Avatar Upload</div>
                        <div class="scenario-description">Test profile picture upload functionality</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-primary" onclick="testAvatarUpload()">
                                <i class="fas fa-camera"></i>
                                Test Upload
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="profileResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Profile Management Testing Interface</p>
                    <p>User profile management testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentUser = null;
        let activityData = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load user profile
            await loadUserProfile();

            // Load activity log
            loadActivityLog();

            // Set up form handlers
            document.getElementById('personalInfoForm').addEventListener('submit', handlePersonalInfoUpdate);
            document.getElementById('passwordChangeForm').addEventListener('submit', handlePasswordChange);
            document.getElementById('preferencesForm').addEventListener('submit', handlePreferencesUpdate);
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.profile-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            // Load data for specific tabs
            if (tabName === 'activity') {
                loadActivityLog();
            }
        }

        // Profile Loading Functions
        async function loadUserProfile() {
            try {
                const response = await window.apiClient.request('GET', '/auth/profile');

                if (response.success) {
                    currentUser = response.data.user || response.data;
                    displayUserProfile(currentUser);
                    showResponse('profileResponse', response, 'success');
                } else {
                    showResponse('profileResponse', response, 'error');
                }
            } catch (error) {
                // Load mock user data
                currentUser = {
                    id: 'user_123',
                    firstName: 'John',
                    lastName: 'Doe',
                    displayName: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    bio: 'Software developer passionate about creating amazing user experiences.',
                    timezone: 'America/New_York',
                    avatar: null,
                    emailVerified: true,
                    mfaEnabled: false,
                    preferences: {
                        emailNotifications: true,
                        smsNotifications: false,
                        pushNotifications: true,
                        marketingEmails: false,
                        profileVisibility: 'public',
                        showEmail: false,
                        showPhone: false,
                        language: 'en',
                        theme: 'light'
                    }
                };

                displayUserProfile(currentUser);
                showResponse('profileResponse', {
                    success: true,
                    data: { user: currentUser },
                    message: 'Mock user profile loaded (endpoint may not be available)'
                }, 'warning');
            }
        }

        function displayUserProfile(user) {
            // Update profile header
            document.getElementById('profileName').textContent = user.displayName || `${user.firstName} ${user.lastName}`;
            document.getElementById('profileEmail').textContent = user.email;
            document.getElementById('avatarInitials').textContent = (user.firstName?.charAt(0) || 'U') + (user.lastName?.charAt(0) || '');

            const statusElement = document.getElementById('profileStatus');
            if (user.emailVerified) {
                statusElement.textContent = 'Email Verified';
                statusElement.className = 'profile-status verified';
            } else {
                statusElement.textContent = 'Email Unverified';
                statusElement.className = 'profile-status unverified';
            }

            // Update form fields
            document.getElementById('firstName').value = user.firstName || '';
            document.getElementById('lastName').value = user.lastName || '';
            document.getElementById('displayName').value = user.displayName || '';
            document.getElementById('email').value = user.email || '';
            document.getElementById('phone').value = user.phone || '';
            document.getElementById('bio').value = user.bio || '';
            document.getElementById('timezone').value = user.timezone || 'UTC';

            // Update MFA status
            const mfaStatus = document.getElementById('mfaStatus');
            if (user.mfaEnabled) {
                mfaStatus.textContent = 'Enabled';
                mfaStatus.className = 'security-status enabled';
            } else {
                mfaStatus.textContent = 'Disabled';
                mfaStatus.className = 'security-status disabled';
            }

            // Update preferences
            if (user.preferences) {
                document.getElementById('emailNotifications').checked = user.preferences.emailNotifications;
                document.getElementById('smsNotifications').checked = user.preferences.smsNotifications;
                document.getElementById('pushNotifications').checked = user.preferences.pushNotifications;
                document.getElementById('marketingEmails').checked = user.preferences.marketingEmails;
                document.getElementById('profileVisibility').value = user.preferences.profileVisibility;
                document.getElementById('showEmail').checked = user.preferences.showEmail;
                document.getElementById('showPhone').checked = user.preferences.showPhone;
                document.getElementById('language').value = user.preferences.language;
                document.getElementById('theme').value = user.preferences.theme;
            }
        }

        // Form Handlers
        async function handlePersonalInfoUpdate(event) {
            event.preventDefault();

            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                displayName: document.getElementById('displayName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                bio: document.getElementById('bio').value,
                timezone: document.getElementById('timezone').value
            };

            try {
                const response = await window.apiClient.request('PUT', '/auth/profile', formData);

                if (response.success) {
                    window.notificationManager.success('Profile updated successfully');
                    currentUser = { ...currentUser, ...formData };
                    displayUserProfile(currentUser);
                    addActivityItem('Profile Updated', 'Personal information updated');
                } else {
                    window.notificationManager.error(response.error || 'Failed to update profile');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate successful update
                window.notificationManager.success('Profile updated successfully (simulated)');
                currentUser = { ...currentUser, ...formData };
                displayUserProfile(currentUser);
                addActivityItem('Profile Updated', 'Personal information updated');

                showResponse('profileResponse', {
                    success: true,
                    data: { user: currentUser },
                    message: 'Mock profile update successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        async function handlePasswordChange(event) {
            event.preventDefault();

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                window.notificationManager.error('Please fill in all password fields');
                return;
            }

            if (newPassword !== confirmPassword) {
                window.notificationManager.error('New passwords do not match');
                return;
            }

            if (newPassword.length < 8) {
                window.notificationManager.error('New password must be at least 8 characters long');
                return;
            }

            try {
                const response = await window.apiClient.request('PUT', '/auth/change-password', {
                    currentPassword: currentPassword,
                    newPassword: newPassword
                });

                if (response.success) {
                    window.notificationManager.success('Password changed successfully');
                    document.getElementById('passwordChangeForm').reset();
                    addActivityItem('Password Changed', 'Account password updated');
                } else {
                    window.notificationManager.error(response.error || 'Failed to change password');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate password change
                if (currentPassword === 'wrongpassword') {
                    window.notificationManager.error('Current password is incorrect');
                    showResponse('profileResponse', {
                        success: false,
                        error: 'Current password is incorrect'
                    }, 'error');
                } else {
                    window.notificationManager.success('Password changed successfully (simulated)');
                    document.getElementById('passwordChangeForm').reset();
                    addActivityItem('Password Changed', 'Account password updated');

                    showResponse('profileResponse', {
                        success: true,
                        message: 'Mock password change successful (endpoint may not be available)'
                    }, 'warning');
                }
            }
        }

        async function handlePreferencesUpdate(event) {
            event.preventDefault();

            const preferences = {
                emailNotifications: document.getElementById('emailNotifications').checked,
                smsNotifications: document.getElementById('smsNotifications').checked,
                pushNotifications: document.getElementById('pushNotifications').checked,
                marketingEmails: document.getElementById('marketingEmails').checked,
                profileVisibility: document.getElementById('profileVisibility').value,
                showEmail: document.getElementById('showEmail').checked,
                showPhone: document.getElementById('showPhone').checked,
                language: document.getElementById('language').value,
                theme: document.getElementById('theme').value
            };

            try {
                const response = await window.apiClient.request('PUT', '/auth/preferences', preferences);

                if (response.success) {
                    window.notificationManager.success('Preferences updated successfully');
                    currentUser.preferences = preferences;
                    addActivityItem('Preferences Updated', 'Account preferences modified');
                } else {
                    window.notificationManager.error(response.error || 'Failed to update preferences');
                }

                showResponse('profileResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate successful update
                window.notificationManager.success('Preferences updated successfully (simulated)');
                currentUser.preferences = preferences;
                addActivityItem('Preferences Updated', 'Account preferences modified');

                showResponse('profileResponse', {
                    success: true,
                    data: { preferences: preferences },
                    message: 'Mock preferences update successful (endpoint may not be available)'
                }, 'warning');
            }
        }

        // Avatar Upload Functions
        function triggerAvatarUpload() {
            document.getElementById('avatarUpload').click();
        }

        function handleAvatarUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            if (!file.type.startsWith('image/')) {
                window.notificationManager.error('Please select an image file');
                return;
            }

            if (file.size > 5 * 1024 * 1024) { // 5MB
                window.notificationManager.error('File size must be less than 5MB');
                return;
            }

            // Simulate upload
            window.notificationManager.info('Uploading avatar...');

            setTimeout(() => {
                window.notificationManager.success('Avatar uploaded successfully');
                addActivityItem('Avatar Updated', 'Profile picture changed');

                // Update avatar display (in real app, this would be the uploaded image URL)
                const reader = new FileReader();
                reader.onload = function(e) {
                    // In a real app, you'd upload to server and get back URL
                    // For demo, we'll just show the local file
                    console.log('Avatar would be updated with:', e.target.result);
                };
                reader.readAsDataURL(file);
            }, 2000);
        }

        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();

            const uploadArea = event.target.closest('.upload-area');
            uploadArea.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const fakeEvent = { target: { files: files } };
                handleAvatarUpload(fakeEvent);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            event.target.closest('.upload-area').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            event.target.closest('.upload-area').classList.remove('dragover');
        }

        // Activity Log Functions
        function loadActivityLog() {
            // Generate mock activity data
            activityData = [
                {
                    action: 'Login',
                    details: 'Logged in from Chrome on Windows',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    icon: 'fas fa-sign-in-alt'
                },
                {
                    action: 'Profile Updated',
                    details: 'Changed display name and bio',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    icon: 'fas fa-user-edit'
                },
                {
                    action: 'Password Changed',
                    details: 'Account password updated',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
                    icon: 'fas fa-key'
                },
                {
                    action: 'Email Verified',
                    details: 'Email address verified successfully',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
                    icon: 'fas fa-envelope-circle-check'
                },
                {
                    action: 'Account Created',
                    details: 'Account registered and created',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 1 week ago
                    icon: 'fas fa-user-plus'
                }
            ];

            displayActivityLog();
        }

        function displayActivityLog() {
            const activityLog = document.getElementById('activityLog');

            const activityHTML = activityData.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-action">${activity.action}</div>
                        <div class="activity-details">${activity.details}</div>
                    </div>
                    <div class="activity-time">${formatTimeAgo(activity.timestamp)}</div>
                </div>
            `).join('');

            activityLog.innerHTML = `
                <div class="activity-header">Recent Activity</div>
                ${activityHTML}
            `;
        }

        function addActivityItem(action, details) {
            activityData.unshift({
                action: action,
                details: details,
                timestamp: new Date(),
                icon: 'fas fa-edit'
            });

            // Keep only last 10 items
            if (activityData.length > 10) {
                activityData = activityData.slice(0, 10);
            }

            displayActivityLog();
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        // Security Functions
        function toggleMFA() {
            window.notificationManager.info('Redirecting to MFA setup...');
            setTimeout(() => {
                window.location.href = '../../mfa/setup/index.html';
            }, 1000);
        }

        function toggleLoginNotifications() {
            const currentStatus = document.querySelector('#loginNotifications .security-status').textContent;
            const newStatus = currentStatus === 'Enabled' ? 'Disabled' : 'Enabled';

            window.notificationManager.success(`Login notifications ${newStatus.toLowerCase()}`);
            addActivityItem('Security Settings', `Login notifications ${newStatus.toLowerCase()}`);
        }

        function viewActiveSessions() {
            window.notificationManager.info('Redirecting to session management...');
            setTimeout(() => {
                window.location.href = '../../sessions/session-list/index.html';
            }, 1000);
        }

        function deactivateAccount() {
            if (confirm('Are you sure you want to deactivate your account? This action can be reversed.')) {
                window.notificationManager.warning('Account deactivation would be processed here');
                addActivityItem('Account Deactivated', 'Account temporarily deactivated');
            }
        }

        function deleteAccount() {
            if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {
                if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {
                    window.notificationManager.error('Account deletion would be processed here');
                    addActivityItem('Account Deletion Requested', 'Permanent account deletion requested');
                }
            }
        }

        // Quick Test Functions
        function testProfileUpdate() {
            fillValidProfileData();
            window.notificationManager.info('Profile data filled. Click Save Changes to test update.');
        }

        function testPasswordChange() {
            document.getElementById('currentPassword').value = 'currentpass123';
            document.getElementById('newPassword').value = 'NewSecurePass123!';
            document.getElementById('confirmPassword').value = 'NewSecurePass123!';
            window.notificationManager.info('Password fields filled. Click Change Password to test.');
        }

        function testEmailChange() {
            const currentEmail = document.getElementById('email').value;
            document.getElementById('email').value = '<EMAIL>';
            window.notificationManager.info('Email changed. This would require verification in real implementation.');
        }

        function fillValidProfileData() {
            document.getElementById('firstName').value = 'Jane';
            document.getElementById('lastName').value = 'Smith';
            document.getElementById('displayName').value = 'Jane Smith';
            document.getElementById('phone').value = '+****************';
            document.getElementById('bio').value = 'Updated bio with new information about my interests and experience.';
            document.getElementById('timezone').value = 'America/Los_Angeles';
        }

        function testEmailChangeFlow() {
            switchTab('personal');
            const originalEmail = document.getElementById('email').value;
            document.getElementById('email').value = '<EMAIL>';

            window.notificationManager.info('Email change initiated. In real app, this would:');
            setTimeout(() => window.notificationManager.info('1. Send verification to new email'), 1000);
            setTimeout(() => window.notificationManager.info('2. Require confirmation from old email'), 2000);
            setTimeout(() => window.notificationManager.info('3. Update email after both verifications'), 3000);
        }

        function testPasswordSecurity() {
            switchTab('security');

            // Test weak password
            document.getElementById('currentPassword').value = 'oldpass';
            document.getElementById('newPassword').value = '123';
            document.getElementById('confirmPassword').value = '123';

            window.notificationManager.warning('Weak password filled. This should trigger validation errors.');
        }

        function testAvatarUpload() {
            window.notificationManager.info('Avatar upload test:');
            setTimeout(() => window.notificationManager.info('1. Click avatar or upload area'), 1000);
            setTimeout(() => window.notificationManager.info('2. Select image file (PNG/JPG, max 5MB)'), 2000);
            setTimeout(() => window.notificationManager.info('3. File would be uploaded and processed'), 3000);
        }

        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + 'ToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
