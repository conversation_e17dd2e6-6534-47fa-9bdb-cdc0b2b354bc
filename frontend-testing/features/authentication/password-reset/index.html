<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .password-reset-container {
            max-width: 500px;
            margin: 2rem auto;
        }

        .reset-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .reset-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .reset-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .reset-form {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header h2 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .reset-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .status-icon.success {
            color: var(--success-color);
        }

        .status-icon.error {
            color: var(--error-color);
        }

        .status-icon.warning {
            color: var(--warning-color);
        }

        .status-icon.info {
            color: var(--info-color);
        }

        .status-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-message {
            color: var(--text-secondary);
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .token-input {
            font-family: monospace;
            font-size: 1rem;
            letter-spacing: 1px;
        }

        .password-requirements {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin: 0.25rem 0;
        }

        .requirement-icon {
            margin-right: 0.5rem;
            width: 16px;
        }

        .requirement.met {
            color: var(--success-color);
        }

        .requirement.unmet {
            color: var(--error-color);
        }

        .password-strength {
            margin: 0.5rem 0;
        }

        .strength-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease, background-color 0.3s ease;
        }

        .strength-fill.weak {
            background: var(--error-color);
        }

        .strength-fill.medium {
            background: var(--warning-color);
        }

        .strength-fill.strong {
            background: var(--success-color);
        }

        .strength-label {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-weight: bold;
        }

        .input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
        }

        .password-toggle:hover {
            color: var(--text-primary);
        }

        .rate-limit-info {
            background: rgba(var(--warning-color-rgb), 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
        }

        .rate-limit-info .warning-icon {
            color: var(--warning-color);
            margin-right: 0.5rem;
        }

        .reset-steps {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .step:last-child {
            border-bottom: none;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .step-number.active {
            background: var(--primary-color);
            color: white;
        }

        .step-number.completed {
            background: var(--success-color);
            color: white;
        }

        .step-content {
            flex: 1;
            font-size: 0.9rem;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .step-description {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-key"></i>
                    <h1>Password Reset Testing</h1>
                    <span class="subtitle">Password Reset Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Auth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="password-reset-container">
                <!-- Reset Status -->
                <div class="reset-status" id="resetStatus">
                    <div class="status-icon info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="status-title">Ready to Test Password Reset</div>
                    <div class="status-message">Use the tabs below to test different password reset scenarios</div>
                </div>

                <!-- Navigation Tabs -->
                <div class="reset-tabs">
                    <button class="reset-tab active" onclick="switchTab('request')">
                        <i class="fas fa-envelope"></i>
                        Request Reset
                    </button>
                    <button class="reset-tab" onclick="switchTab('verify')">
                        <i class="fas fa-key"></i>
                        Verify Token
                    </button>
                    <button class="reset-tab" onclick="switchTab('new-password')">
                        <i class="fas fa-lock"></i>
                        New Password
                    </button>
                </div>

                <!-- Request Reset Tab -->
                <div class="tab-content active" id="request-tab">
                    <div class="quick-test-buttons">
                        <button class="btn btn-success" onclick="testValidRequest()">
                            <i class="fas fa-check"></i>
                            Test Valid Request
                        </button>
                        <button class="btn btn-danger" onclick="testInvalidEmail()">
                            <i class="fas fa-times"></i>
                            Test Invalid Email
                        </button>
                        <button class="btn btn-warning" onclick="testRateLimit()">
                            <i class="fas fa-tachometer-alt"></i>
                            Test Rate Limit
                        </button>
                    </div>

                    <div class="reset-form">
                        <div class="form-header">
                            <h2>Request Password Reset</h2>
                            <p>Enter your email address to receive a password reset link</p>
                        </div>

                        <form id="requestForm">
                            <div class="form-group">
                                <label for="resetEmail">Email Address:</label>
                                <input type="email" id="resetEmail" class="form-control" placeholder="<EMAIL>" required>
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-paper-plane"></i>
                                Send Reset Link
                            </button>

                            <div style="text-align: center; margin-top: 1rem;">
                                <a href="../login/index.html">Remember your password? Login here</a>
                            </div>
                        </form>

                        <div class="rate-limit-info">
                            <i class="fas fa-exclamation-triangle warning-icon"></i>
                            <strong>Rate Limiting:</strong> Password reset requests are limited to prevent abuse.
                            You can request a new reset link every 5 minutes.
                        </div>
                    </div>
                </div>

                <!-- Verify Token Tab -->
                <div class="tab-content" id="verify-tab">
                    <div class="quick-test-buttons">
                        <button class="btn btn-success" onclick="testValidToken()">
                            <i class="fas fa-check"></i>
                            Test Valid Token
                        </button>
                        <button class="btn btn-danger" onclick="testInvalidToken()">
                            <i class="fas fa-times"></i>
                            Test Invalid Token
                        </button>
                        <button class="btn btn-warning" onclick="testExpiredToken()">
                            <i class="fas fa-clock"></i>
                            Test Expired Token
                        </button>
                    </div>

                    <div class="reset-form">
                        <div class="form-header">
                            <h2>Verify Reset Token</h2>
                            <p>Enter the reset token from your email</p>
                        </div>

                        <form id="verifyForm">
                            <div class="form-group">
                                <label for="resetToken">Reset Token:</label>
                                <input type="text" id="resetToken" class="form-control token-input" placeholder="Enter reset token" required>
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-check"></i>
                                Verify Token
                            </button>
                        </form>
                    </div>
                </div>

                <!-- New Password Tab -->
                <div class="tab-content" id="new-password-tab">
                    <div class="quick-test-buttons">
                        <button class="btn btn-success" onclick="testValidPassword()">
                            <i class="fas fa-check"></i>
                            Test Valid Password
                        </button>
                        <button class="btn btn-warning" onclick="testWeakPassword()">
                            <i class="fas fa-exclamation-triangle"></i>
                            Test Weak Password
                        </button>
                        <button class="btn btn-danger" onclick="testMismatchedPasswords()">
                            <i class="fas fa-times"></i>
                            Test Mismatched
                        </button>
                    </div>

                    <div class="reset-form">
                        <div class="form-header">
                            <h2>Set New Password</h2>
                            <p>Enter your new password</p>
                        </div>

                        <form id="newPasswordForm">
                            <div class="form-group">
                                <label for="newPassword">New Password:</label>
                                <div class="input-group">
                                    <input type="password" id="newPassword" class="form-control" placeholder="Enter new password" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                                        <i class="fas fa-eye" id="newPasswordToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="passwordStrength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="strengthFill" style="width: 0%;"></div>
                                    </div>
                                    <div class="strength-label" id="strengthLabel">Enter password to check strength</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="confirmNewPassword">Confirm New Password:</label>
                                <div class="input-group">
                                    <input type="password" id="confirmNewPassword" class="form-control" placeholder="Confirm new password" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('confirmNewPassword')">
                                        <i class="fas fa-eye" id="confirmNewPasswordToggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="password-requirements">
                                <h4>Password Requirements:</h4>
                                <div class="requirement unmet" id="lengthReq">
                                    <i class="fas fa-times requirement-icon"></i>
                                    At least 8 characters long
                                </div>
                                <div class="requirement unmet" id="uppercaseReq">
                                    <i class="fas fa-times requirement-icon"></i>
                                    Contains uppercase letter
                                </div>
                                <div class="requirement unmet" id="lowercaseReq">
                                    <i class="fas fa-times requirement-icon"></i>
                                    Contains lowercase letter
                                </div>
                                <div class="requirement unmet" id="numberReq">
                                    <i class="fas fa-times requirement-icon"></i>
                                    Contains number
                                </div>
                                <div class="requirement unmet" id="specialReq">
                                    <i class="fas fa-times requirement-icon"></i>
                                    Contains special character
                                </div>
                            </div>

                            <input type="hidden" id="resetTokenHidden" value="">

                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-save"></i>
                                Update Password
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Valid Reset Flow</div>
                        <div class="scenario-description">Test complete password reset flow</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="testCompleteFlow()">
                                <i class="fas fa-play"></i>
                                Test Complete Flow
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Email</div>
                        <div class="scenario-description">Test reset request with non-existent email</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillInvalidEmail()">
                                <i class="fas fa-envelope"></i>
                                Fill Invalid Email
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Expired Token</div>
                        <div class="scenario-description">Test with expired reset token</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="fillExpiredToken()">
                                <i class="fas fa-clock"></i>
                                Fill Expired Token
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Token Reuse</div>
                        <div class="scenario-description">Test using same token multiple times</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="testTokenReuse()">
                                <i class="fas fa-redo"></i>
                                Test Token Reuse
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reset Steps -->
                <div class="reset-steps">
                    <h4>Password Reset Process</h4>
                    <div class="step">
                        <div class="step-number" id="step1">1</div>
                        <div class="step-content">
                            <div class="step-title">Request Reset</div>
                            <div class="step-description">User requests password reset with email</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number" id="step2">2</div>
                        <div class="step-content">
                            <div class="step-title">Send Email</div>
                            <div class="step-description">System sends reset link to email</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number" id="step3">3</div>
                        <div class="step-content">
                            <div class="step-title">Verify Token</div>
                            <div class="step-description">User clicks link and token is verified</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number" id="step4">4</div>
                        <div class="step-content">
                            <div class="step-title">Update Password</div>
                            <div class="step-description">User sets new password</div>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="resetResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Password Reset Testing Interface</p>
                    <p>Password reset testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let currentToken = '';
        let passwordStrength = 0;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Set up form handlers
            document.getElementById('requestForm').addEventListener('submit', handleResetRequest);
            document.getElementById('verifyForm').addEventListener('submit', handleTokenVerification);
            document.getElementById('newPasswordForm').addEventListener('submit', handlePasswordUpdate);
            document.getElementById('newPassword').addEventListener('input', checkPasswordStrength);
            document.getElementById('confirmNewPassword').addEventListener('input', checkPasswordMatch);
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.reset-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        // Reset Request Functions
        async function handleResetRequest(event) {
            event.preventDefault();

            const email = document.getElementById('resetEmail').value;

            if (!email) {
                updateResetStatus('error', 'Validation Error', 'Please enter an email address');
                return;
            }

            updateResetStatus('info', 'Sending Reset Link...', 'Processing password reset request');
            updateProgressStep(1, 'active');

            try {
                const response = await window.apiClient.request('POST', '/auth/forgot-password', {
                    email: email
                });

                if (response.success) {
                    updateProgressStep(1, 'completed');
                    updateProgressStep(2, 'completed');
                    updateResetStatus('success', 'Reset Link Sent',
                        'A password reset link has been sent to your email address.');

                    // Simulate token for testing
                    currentToken = 'reset_token_' + Date.now();
                    document.getElementById('resetTokenHidden').value = currentToken;
                } else {
                    updateResetStatus('error', 'Reset Failed', response.error || 'Failed to send reset link');
                }

                showResponse('resetResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate reset request
                const isValidEmail = email.includes('@') && email.includes('.');

                if (isValidEmail && email !== '<EMAIL>') {
                    updateProgressStep(1, 'completed');
                    updateProgressStep(2, 'completed');
                    updateResetStatus('success', 'Reset Link Sent (Simulated)',
                        'A password reset link has been sent to your email address.');

                    currentToken = 'reset_token_' + Date.now();
                    document.getElementById('resetTokenHidden').value = currentToken;

                    showResponse('resetResponse', {
                        success: true,
                        data: { email: email, token: currentToken },
                        message: 'Mock reset request successful (endpoint may not be available)'
                    }, 'warning');
                } else {
                    updateResetStatus('error', 'Reset Failed', 'Email address not found');
                    showResponse('resetResponse', {
                        success: false,
                        error: 'Email address not found',
                        details: 'The provided email address is not registered'
                    }, 'error');
                }
            }
        }

        // Token Verification Functions
        async function handleTokenVerification(event) {
            event.preventDefault();

            const token = document.getElementById('resetToken').value;

            if (!token) {
                updateResetStatus('error', 'Validation Error', 'Please enter a reset token');
                return;
            }

            updateResetStatus('info', 'Verifying Token...', 'Checking token validity');
            updateProgressStep(3, 'active');

            try {
                const response = await window.apiClient.request('POST', '/auth/verify-reset-token', {
                    token: token
                });

                if (response.success) {
                    updateProgressStep(3, 'completed');
                    updateResetStatus('success', 'Token Verified', 'Token is valid. You can now set a new password.');
                    currentToken = token;
                    document.getElementById('resetTokenHidden').value = token;
                    switchTab('new-password');
                } else {
                    updateResetStatus('error', 'Token Invalid', response.error || 'Invalid or expired token');
                }

                showResponse('resetResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate token verification
                const isValidToken = token.startsWith('reset_token_') && !token.includes('expired') && !token.includes('invalid');

                if (isValidToken) {
                    updateProgressStep(3, 'completed');
                    updateResetStatus('success', 'Token Verified (Simulated)', 'Token is valid. You can now set a new password.');
                    currentToken = token;
                    document.getElementById('resetTokenHidden').value = token;

                    showResponse('resetResponse', {
                        success: true,
                        data: { token: token, valid: true },
                        message: 'Mock token verification successful (endpoint may not be available)'
                    }, 'warning');

                    switchTab('new-password');
                } else {
                    updateResetStatus('error', 'Token Invalid', 'Invalid or expired token');
                    showResponse('resetResponse', {
                        success: false,
                        error: 'Invalid or expired token',
                        details: 'Token verification failed in simulation'
                    }, 'error');
                }
            }
        }

        // Password Update Functions
        async function handlePasswordUpdate(event) {
            event.preventDefault();

            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmNewPassword').value;
            const token = document.getElementById('resetTokenHidden').value;

            if (!newPassword || !confirmPassword) {
                updateResetStatus('error', 'Validation Error', 'Please fill in all password fields');
                return;
            }

            if (newPassword !== confirmPassword) {
                updateResetStatus('error', 'Validation Error', 'Passwords do not match');
                return;
            }

            if (passwordStrength < 3) {
                updateResetStatus('error', 'Validation Error', 'Password does not meet security requirements');
                return;
            }

            if (!token) {
                updateResetStatus('error', 'Validation Error', 'No valid reset token found');
                return;
            }

            updateResetStatus('info', 'Updating Password...', 'Setting new password');
            updateProgressStep(4, 'active');

            try {
                const response = await window.apiClient.request('POST', '/auth/reset-password', {
                    token: token,
                    newPassword: newPassword
                });

                if (response.success) {
                    updateProgressStep(4, 'completed');
                    updateResetStatus('success', 'Password Updated', 'Your password has been successfully updated. You can now login with your new password.');

                    // Clear forms
                    document.getElementById('newPasswordForm').reset();
                    resetPasswordStrength();
                } else {
                    updateResetStatus('error', 'Update Failed', response.error || 'Failed to update password');
                }

                showResponse('resetResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate password update
                updateProgressStep(4, 'completed');
                updateResetStatus('success', 'Password Updated (Simulated)', 'Your password has been successfully updated. You can now login with your new password.');

                showResponse('resetResponse', {
                    success: true,
                    data: { passwordUpdated: true },
                    message: 'Mock password update successful (endpoint may not be available)'
                }, 'warning');

                // Clear forms
                document.getElementById('newPasswordForm').reset();
                resetPasswordStrength();
            }
        }

        // Password Strength Functions
        function checkPasswordStrength() {
            const password = document.getElementById('newPassword').value;
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            // Update requirement indicators
            updateRequirement('lengthReq', requirements.length);
            updateRequirement('uppercaseReq', requirements.uppercase);
            updateRequirement('lowercaseReq', requirements.lowercase);
            updateRequirement('numberReq', requirements.number);
            updateRequirement('specialReq', requirements.special);

            // Calculate strength
            const metRequirements = Object.values(requirements).filter(Boolean).length;
            passwordStrength = metRequirements;

            // Update strength bar
            const strengthFill = document.getElementById('strengthFill');
            const strengthLabel = document.getElementById('strengthLabel');

            let width, className, label;
            if (metRequirements <= 2) {
                width = '25%';
                className = 'weak';
                label = 'Weak';
            } else if (metRequirements <= 3) {
                width = '50%';
                className = 'medium';
                label = 'Medium';
            } else if (metRequirements <= 4) {
                width = '75%';
                className = 'medium';
                label = 'Good';
            } else {
                width = '100%';
                className = 'strong';
                label = 'Strong';
            }

            strengthFill.style.width = width;
            strengthFill.className = `strength-fill ${className}`;
            strengthLabel.textContent = label;
            strengthLabel.className = `strength-label ${className}`;
        }

        function updateRequirement(elementId, met) {
            const element = document.getElementById(elementId);
            const icon = element.querySelector('.requirement-icon');

            if (met) {
                element.className = 'requirement met';
                icon.className = 'fas fa-check requirement-icon';
            } else {
                element.className = 'requirement unmet';
                icon.className = 'fas fa-times requirement-icon';
            }
        }

        function resetPasswordStrength() {
            passwordStrength = 0;
            document.getElementById('strengthFill').style.width = '0%';
            document.getElementById('strengthLabel').textContent = 'Enter password to check strength';

            // Reset all requirements
            ['lengthReq', 'uppercaseReq', 'lowercaseReq', 'numberReq', 'specialReq'].forEach(id => {
                updateRequirement(id, false);
            });
        }

        function checkPasswordMatch() {
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmNewPassword').value;

            if (confirmPassword && password !== confirmPassword) {
                document.getElementById('confirmNewPassword').style.borderColor = 'var(--error-color)';
            } else {
                document.getElementById('confirmNewPassword').style.borderColor = '';
            }
        }

        // Status and Progress Functions
        function updateResetStatus(type, title, message) {
            const statusContainer = document.getElementById('resetStatus');
            const iconClass = type === 'success' ? 'fas fa-check-circle' :
                             type === 'error' ? 'fas fa-times-circle' :
                             type === 'warning' ? 'fas fa-exclamation-triangle' :
                             'fas fa-info-circle';

            statusContainer.innerHTML = `
                <div class="status-icon ${type}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="status-title">${title}</div>
                <div class="status-message">${message}</div>
            `;
        }

        function updateProgressStep(stepNumber, status) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            stepElement.className = `step-number ${status}`;
        }

        // Quick Test Functions
        function testValidRequest() {
            document.getElementById('resetEmail').value = '<EMAIL>';
            window.notificationManager.info('Valid email filled. Click Send Reset Link to test.');
        }

        function testInvalidEmail() {
            document.getElementById('resetEmail').value = '<EMAIL>';
            window.notificationManager.info('Non-existent email filled. This should trigger an error.');
        }

        function testRateLimit() {
            window.notificationManager.info('Testing rate limit...');
            // Simulate multiple rapid requests
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    window.notificationManager.warning(`Rate limit test ${i + 1}/3`);
                    if (i === 2) {
                        updateResetStatus('error', 'Rate Limit Exceeded', 'Too many reset requests. Please wait 5 minutes.');
                    }
                }, i * 500);
            }
        }

        function testValidToken() {
            document.getElementById('resetToken').value = 'reset_token_' + Date.now();
            window.notificationManager.info('Valid token filled. Click Verify Token to test.');
        }

        function testInvalidToken() {
            document.getElementById('resetToken').value = 'invalid_token_123';
            window.notificationManager.info('Invalid token filled. This should trigger an error.');
        }

        function testExpiredToken() {
            document.getElementById('resetToken').value = 'expired_token_123';
            window.notificationManager.info('Expired token filled. This should trigger an error.');
        }

        function testValidPassword() {
            document.getElementById('newPassword').value = 'SecurePass123!';
            document.getElementById('confirmNewPassword').value = 'SecurePass123!';
            checkPasswordStrength();
            checkPasswordMatch();
            window.notificationManager.info('Valid password filled. Check password requirements.');
        }

        function testWeakPassword() {
            document.getElementById('newPassword').value = '123';
            document.getElementById('confirmNewPassword').value = '123';
            checkPasswordStrength();
            checkPasswordMatch();
            window.notificationManager.info('Weak password filled. Check password requirements.');
        }

        function testMismatchedPasswords() {
            document.getElementById('newPassword').value = 'Password123!';
            document.getElementById('confirmNewPassword').value = 'DifferentPass456!';
            checkPasswordStrength();
            checkPasswordMatch();
            window.notificationManager.info('Mismatched passwords filled. This should trigger validation error.');
        }

        function testCompleteFlow() {
            window.notificationManager.info('Testing complete password reset flow...');

            // Step 1: Request reset
            document.getElementById('resetEmail').value = '<EMAIL>';
            setTimeout(() => {
                updateProgressStep(1, 'completed');
                updateProgressStep(2, 'completed');
                updateResetStatus('success', 'Reset Link Sent', 'Email sent successfully');

                // Step 2: Switch to verify tab and fill token
                setTimeout(() => {
                    switchTab('verify');
                    document.getElementById('resetToken').value = 'reset_token_' + Date.now();

                    // Step 3: Verify token
                    setTimeout(() => {
                        updateProgressStep(3, 'completed');
                        updateResetStatus('success', 'Token Verified', 'Token is valid');

                        // Step 4: Switch to new password tab
                        setTimeout(() => {
                            switchTab('new-password');
                            document.getElementById('newPassword').value = 'NewSecurePass123!';
                            document.getElementById('confirmNewPassword').value = 'NewSecurePass123!';
                            checkPasswordStrength();

                            setTimeout(() => {
                                updateProgressStep(4, 'completed');
                                updateResetStatus('success', 'Password Updated', 'Complete flow test successful');
                                window.notificationManager.success('Complete password reset flow test completed!');
                            }, 1000);
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 1000);
        }

        // Form Helper Functions
        function fillInvalidEmail() {
            switchTab('request');
            document.getElementById('resetEmail').value = 'invalid-email-format';
        }

        function fillExpiredToken() {
            switchTab('verify');
            document.getElementById('resetToken').value = 'expired_token_old_timestamp';
        }

        function testTokenReuse() {
            window.notificationManager.info('Testing token reuse...');
            const usedToken = 'used_token_123';
            document.getElementById('resetToken').value = usedToken;

            setTimeout(() => {
                updateResetStatus('error', 'Token Already Used', 'This token has already been used and cannot be reused');
            }, 1000);
        }

        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + 'ToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
