<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-container {
            max-width: 500px;
            margin: 2rem auto;
        }

        .login-form {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header h2 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .login-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .status-icon.success {
            color: var(--success-color);
        }

        .status-icon.error {
            color: var(--error-color);
        }

        .status-icon.warning {
            color: var(--warning-color);
        }

        .status-icon.info {
            color: var(--info-color);
        }

        .status-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-message {
            color: var(--text-secondary);
        }

        .quick-login-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
        }

        .password-toggle:hover {
            color: var(--text-primary);
        }

        .input-group {
            position: relative;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .login-options a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .login-options a:hover {
            text-decoration: underline;
        }

        .rate-limit-info {
            background: rgba(var(--warning-color-rgb), 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
        }

        .rate-limit-info .warning-icon {
            color: var(--warning-color);
            margin-right: 0.5rem;
        }

        .login-attempts {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
        }

        .attempt-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .attempt-item:last-child {
            border-bottom: none;
        }

        .attempt-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .attempt-status.success {
            background: var(--success-color);
            color: white;
        }

        .attempt-status.failed {
            background: var(--error-color);
            color: white;
        }

        .attempt-status.blocked {
            background: var(--warning-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-sign-in-alt"></i>
                    <h1>Login Testing</h1>
                    <span class="subtitle">Authentication Login Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Auth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="login-container">
                <!-- Login Status -->
                <div class="login-status" id="loginStatus">
                    <div class="status-icon info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="status-title">Ready to Test Login</div>
                    <div class="status-message">Use the form below or quick test buttons to test login functionality</div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-login-buttons">
                    <button class="btn btn-success" onclick="testValidLogin()">
                        <i class="fas fa-check"></i>
                        Test Valid Login
                    </button>
                    <button class="btn btn-danger" onclick="testInvalidLogin()">
                        <i class="fas fa-times"></i>
                        Test Invalid Login
                    </button>
                    <button class="btn btn-warning" onclick="testRateLimit()">
                        <i class="fas fa-tachometer-alt"></i>
                        Test Rate Limit
                    </button>
                    <button class="btn btn-info" onclick="testMFALogin()">
                        <i class="fas fa-shield-alt"></i>
                        Test MFA Login
                    </button>
                </div>

                <!-- Login Form -->
                <div class="login-form">
                    <div class="form-header">
                        <h2>Login Test Form</h2>
                        <p>Test various login scenarios and edge cases</p>
                    </div>

                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email">Email Address:</label>
                            <input type="email" id="email" class="form-control" placeholder="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label for="password">Password:</label>
                            <div class="input-group">
                                <input type="password" id="password" class="form-control" placeholder="Enter password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="remember-me">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">Remember me</label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-sign-in-alt"></i>
                            Login
                        </button>

                        <div class="login-options">
                            <a href="../password-reset/index.html">Forgot Password?</a>
                            <a href="../registration/index.html">Create Account</a>
                        </div>
                    </form>

                    <div class="rate-limit-info">
                        <i class="fas fa-exclamation-triangle warning-icon"></i>
                        <strong>Rate Limiting:</strong> Login attempts are limited to prevent brute force attacks. 
                        After 5 failed attempts, the account will be temporarily locked.
                    </div>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Valid Credentials</div>
                        <div class="scenario-description">Test login with correct email and password</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="fillValidCredentials()">
                                <i class="fas fa-user-check"></i>
                                Fill Valid
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Credentials</div>
                        <div class="scenario-description">Test login with incorrect email or password</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillInvalidCredentials()">
                                <i class="fas fa-user-times"></i>
                                Fill Invalid
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Empty Fields</div>
                        <div class="scenario-description">Test form validation with empty fields</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="clearForm()">
                                <i class="fas fa-eraser"></i>
                                Clear Form
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">SQL Injection</div>
                        <div class="scenario-description">Test security against SQL injection attempts</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillSQLInjection()">
                                <i class="fas fa-bug"></i>
                                Test SQL Injection
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Login Attempts History -->
                <div class="login-attempts" id="loginAttempts">
                    <h4>Recent Login Attempts</h4>
                    <div id="attemptsList">
                        <div class="attempt-item">
                            <div>No login attempts yet</div>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="loginResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Login Testing Interface</p>
                    <p>Authentication login testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let loginAttempts = [];
        let rateLimitCount = 0;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Set up form handler
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // Load any existing login attempts
            loadLoginAttempts();
        }

        // Login Functions
        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!email || !password) {
                updateLoginStatus('error', 'Validation Error', 'Please fill in all required fields');
                return;
            }

            updateLoginStatus('info', 'Logging In...', 'Attempting to authenticate user');

            try {
                const response = await window.apiClient.request('POST', '/auth/login', {
                    email: email,
                    password: password,
                    rememberMe: rememberMe
                });

                if (response.success) {
                    // Store authentication data
                    if (response.data.token) {
                        localStorage.setItem('auth_token', response.data.token);
                    }
                    if (response.data.user) {
                        localStorage.setItem('user', JSON.stringify(response.data.user));
                    }

                    updateLoginStatus('success', 'Login Successful', `Welcome back, ${response.data.user?.email || email}!`);
                    addLoginAttempt(email, 'success', 'Login successful');
                    updateAuthStatus();

                    // Check if MFA is required
                    if (response.data.requiresMFA) {
                        updateLoginStatus('warning', 'MFA Required', 'Please complete multi-factor authentication');
                        setTimeout(() => {
                            window.location.href = '../../mfa/verification/index.html';
                        }, 2000);
                    }
                } else {
                    updateLoginStatus('error', 'Login Failed', response.error || 'Invalid credentials');
                    addLoginAttempt(email, 'failed', response.error || 'Invalid credentials');
                    rateLimitCount++;
                }

                showResponse('loginResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate login behavior
                const isValidEmail = email.includes('@') && email.includes('.');
                const isValidPassword = password.length >= 6;
                const isTestCredentials = email === '<EMAIL>' && password === 'password123';

                if (isTestCredentials || (isValidEmail && isValidPassword && !password.includes('wrong'))) {
                    // Simulate successful login
                    const mockUser = {
                        id: 'user_123',
                        email: email,
                        name: 'Test User',
                        role: 'USER'
                    };

                    localStorage.setItem('auth_token', 'mock_token_' + Date.now());
                    localStorage.setItem('user', JSON.stringify(mockUser));

                    updateLoginStatus('success', 'Login Successful (Simulated)', `Welcome back, ${email}!`);
                    addLoginAttempt(email, 'success', 'Login successful (simulated)');
                    updateAuthStatus();

                    showResponse('loginResponse', {
                        success: true,
                        data: { user: mockUser, token: 'mock_token_' + Date.now() },
                        message: 'Mock login successful (endpoint may not be available)'
                    }, 'warning');
                } else {
                    updateLoginStatus('error', 'Login Failed', 'Invalid email or password');
                    addLoginAttempt(email, 'failed', 'Invalid credentials');
                    rateLimitCount++;

                    showResponse('loginResponse', {
                        success: false,
                        error: 'Invalid email or password',
                        details: 'Login failed in simulation'
                    }, 'error');
                }
            }

            // Check rate limiting
            if (rateLimitCount >= 5) {
                updateLoginStatus('error', 'Account Locked', 'Too many failed login attempts. Account temporarily locked.');
                addLoginAttempt(email, 'blocked', 'Account locked due to too many failed attempts');
            }

            loadLoginAttempts();
        }

        function updateLoginStatus(type, title, message) {
            const statusContainer = document.getElementById('loginStatus');
            const iconClass = type === 'success' ? 'fas fa-check-circle' :
                             type === 'error' ? 'fas fa-times-circle' :
                             type === 'warning' ? 'fas fa-exclamation-triangle' :
                             'fas fa-info-circle';

            statusContainer.innerHTML = `
                <div class="status-icon ${type}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="status-title">${title}</div>
                <div class="status-message">${message}</div>
            `;
        }

        function addLoginAttempt(email, status, message) {
            loginAttempts.unshift({
                id: Date.now(),
                email: email,
                status: status,
                message: message,
                timestamp: new Date().toISOString(),
                ip: '127.0.0.1' // Mock IP
            });

            // Keep only last 10 attempts
            if (loginAttempts.length > 10) {
                loginAttempts = loginAttempts.slice(0, 10);
            }
        }

        function loadLoginAttempts() {
            const attemptsList = document.getElementById('attemptsList');

            if (loginAttempts.length === 0) {
                attemptsList.innerHTML = '<div class="attempt-item"><div>No login attempts yet</div></div>';
                return;
            }

            const attemptsHTML = loginAttempts.map(attempt => `
                <div class="attempt-item">
                    <div>
                        <div><strong>${attempt.email}</strong></div>
                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                            ${attempt.message} • ${new Date(attempt.timestamp).toLocaleString()}
                        </div>
                    </div>
                    <span class="attempt-status ${attempt.status}">${attempt.status}</span>
                </div>
            `).join('');

            attemptsList.innerHTML = attemptsHTML;
        }

        // Quick Test Functions
        function testValidLogin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password123';
            window.notificationManager.info('Valid credentials filled. Click Login to test.');
        }

        function testInvalidLogin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'wrongpassword';
            window.notificationManager.info('Invalid credentials filled. Click Login to test.');
        }

        function testRateLimit() {
            window.notificationManager.info('Testing rate limit with multiple failed attempts...');

            // Simulate multiple failed login attempts
            for (let i = 0; i < 6; i++) {
                setTimeout(() => {
                    addLoginAttempt('<EMAIL>', i < 5 ? 'failed' : 'blocked',
                                  i < 5 ? `Failed attempt ${i + 1}` : 'Account locked');
                    if (i === 5) {
                        loadLoginAttempts();
                        updateLoginStatus('error', 'Rate Limit Triggered', 'Account locked after 5 failed attempts');
                    }
                }, i * 200);
            }
        }

        function testMFALogin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password123';
            window.notificationManager.info('MFA user credentials filled. Login will require MFA verification.');
        }

        // Form Helper Functions
        function fillValidCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'securepassword123';
        }

        function fillInvalidCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'wrongpassword';
        }

        function clearForm() {
            document.getElementById('loginForm').reset();
            updateLoginStatus('info', 'Form Cleared', 'Ready for new login test');
        }

        function fillSQLInjection() {
            document.getElementById('email').value = "<EMAIL>'; DROP TABLE users; --";
            document.getElementById('password').value = "' OR '1'='1";
            window.notificationManager.warning('SQL injection test data filled. This should be safely handled by the backend.');
        }

        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
