<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../../assets/css/main.css">
    <link rel="stylesheet" href="../../../assets/css/components.css">
    <link rel="stylesheet" href="../../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .registration-container {
            max-width: 600px;
            margin: 2rem auto;
        }

        .registration-form {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header h2 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .password-requirements {
            background: var(--hover-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin: 0.25rem 0;
        }

        .requirement-icon {
            margin-right: 0.5rem;
            width: 16px;
        }

        .requirement.met {
            color: var(--success-color);
        }

        .requirement.unmet {
            color: var(--error-color);
        }

        .password-strength {
            margin: 0.5rem 0;
        }

        .strength-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease, background-color 0.3s ease;
        }

        .strength-fill.weak {
            background: var(--error-color);
        }

        .strength-fill.medium {
            background: var(--warning-color);
        }

        .strength-fill.strong {
            background: var(--success-color);
        }

        .strength-label {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-weight: bold;
        }

        .input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
        }

        .password-toggle:hover {
            color: var(--text-primary);
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .terms-checkbox input {
            margin-top: 0.25rem;
        }

        .terms-checkbox label {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .terms-checkbox a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .terms-checkbox a:hover {
            text-decoration: underline;
        }

        .registration-status {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .status-icon.success {
            color: var(--success-color);
        }

        .status-icon.error {
            color: var(--error-color);
        }

        .status-icon.warning {
            color: var(--warning-color);
        }

        .status-icon.info {
            color: var(--info-color);
        }

        .status-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .status-message {
            color: var(--text-secondary);
        }

        .quick-test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .scenario-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .scenario-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .scenario-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .scenario-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .email-validation {
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }

        .email-validation.valid {
            color: var(--success-color);
        }

        .email-validation.invalid {
            color: var(--error-color);
        }

        .registration-progress {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
        }

        .progress-step {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .progress-step:last-child {
            border-bottom: none;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .step-number.active {
            background: var(--primary-color);
            color: white;
        }

        .step-number.completed {
            background: var(--success-color);
            color: white;
        }

        .step-content {
            flex: 1;
            font-size: 0.9rem;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .step-description {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user-plus"></i>
                    <h1>Registration Testing</h1>
                    <span class="subtitle">User Registration Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="../index.html" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        Back to Auth
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="registration-container">
                <!-- Registration Status -->
                <div class="registration-status" id="registrationStatus">
                    <div class="status-icon info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="status-title">Ready to Test Registration</div>
                    <div class="status-message">Use the form below or quick test buttons to test registration functionality</div>
                </div>

                <!-- Quick Test Buttons -->
                <div class="quick-test-buttons">
                    <button class="btn btn-success" onclick="testValidRegistration()">
                        <i class="fas fa-check"></i>
                        Test Valid Registration
                    </button>
                    <button class="btn btn-danger" onclick="testInvalidRegistration()">
                        <i class="fas fa-times"></i>
                        Test Invalid Data
                    </button>
                    <button class="btn btn-warning" onclick="testDuplicateEmail()">
                        <i class="fas fa-copy"></i>
                        Test Duplicate Email
                    </button>
                    <button class="btn btn-info" onclick="testPasswordValidation()">
                        <i class="fas fa-shield-alt"></i>
                        Test Password Rules
                    </button>
                </div>

                <!-- Registration Form -->
                <div class="registration-form">
                    <div class="form-header">
                        <h2>Registration Test Form</h2>
                        <p>Test user registration with various scenarios and validation rules</p>
                    </div>

                    <form id="registrationForm">
                        <div class="form-group">
                            <label for="firstName">First Name:</label>
                            <input type="text" id="firstName" class="form-control" placeholder="John" required>
                        </div>

                        <div class="form-group">
                            <label for="lastName">Last Name:</label>
                            <input type="text" id="lastName" class="form-control" placeholder="Doe" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address:</label>
                            <input type="email" id="email" class="form-control" placeholder="<EMAIL>" required>
                            <div id="emailValidation" class="email-validation"></div>
                        </div>

                        <div class="form-group">
                            <label for="password">Password:</label>
                            <div class="input-group">
                                <input type="password" id="password" class="form-control" placeholder="Enter secure password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="passwordStrength">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strengthFill" style="width: 0%;"></div>
                                </div>
                                <div class="strength-label" id="strengthLabel">Enter password to check strength</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password:</label>
                            <div class="input-group">
                                <input type="password" id="confirmPassword" class="form-control" placeholder="Confirm password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <div class="requirement unmet" id="lengthReq">
                                <i class="fas fa-times requirement-icon"></i>
                                At least 8 characters long
                            </div>
                            <div class="requirement unmet" id="uppercaseReq">
                                <i class="fas fa-times requirement-icon"></i>
                                Contains uppercase letter
                            </div>
                            <div class="requirement unmet" id="lowercaseReq">
                                <i class="fas fa-times requirement-icon"></i>
                                Contains lowercase letter
                            </div>
                            <div class="requirement unmet" id="numberReq">
                                <i class="fas fa-times requirement-icon"></i>
                                Contains number
                            </div>
                            <div class="requirement unmet" id="specialReq">
                                <i class="fas fa-times requirement-icon"></i>
                                Contains special character
                            </div>
                        </div>

                        <div class="terms-checkbox">
                            <input type="checkbox" id="agreeTerms" required>
                            <label for="agreeTerms">
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="terms-checkbox">
                            <input type="checkbox" id="subscribeNewsletter">
                            <label for="subscribeNewsletter">
                                Subscribe to our newsletter for updates and news
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>

                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="../login/index.html">Already have an account? Login here</a>
                        </div>
                    </form>
                </div>

                <!-- Test Scenarios -->
                <div class="test-scenarios">
                    <div class="scenario-card">
                        <div class="scenario-title">Valid Registration</div>
                        <div class="scenario-description">Test registration with all valid data</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-success" onclick="fillValidData()">
                                <i class="fas fa-user-check"></i>
                                Fill Valid Data
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Weak Password</div>
                        <div class="scenario-description">Test registration with weak password</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="fillWeakPassword()">
                                <i class="fas fa-key"></i>
                                Fill Weak Password
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Invalid Email</div>
                        <div class="scenario-description">Test registration with invalid email format</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillInvalidEmail()">
                                <i class="fas fa-envelope-open-text"></i>
                                Fill Invalid Email
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">Password Mismatch</div>
                        <div class="scenario-description">Test with mismatched password confirmation</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-warning" onclick="fillMismatchedPasswords()">
                                <i class="fas fa-times"></i>
                                Fill Mismatched
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">SQL Injection</div>
                        <div class="scenario-description">Test security against SQL injection</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillSQLInjection()">
                                <i class="fas fa-bug"></i>
                                Test SQL Injection
                            </button>
                        </div>
                    </div>

                    <div class="scenario-card">
                        <div class="scenario-title">XSS Attack</div>
                        <div class="scenario-description">Test security against XSS attacks</div>
                        <div class="scenario-actions">
                            <button class="btn btn-sm btn-danger" onclick="fillXSSAttack()">
                                <i class="fas fa-code"></i>
                                Test XSS Attack
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Registration Progress -->
                <div class="registration-progress">
                    <h4>Registration Process</h4>
                    <div class="progress-step">
                        <div class="step-number" id="step1">1</div>
                        <div class="step-content">
                            <div class="step-title">Form Validation</div>
                            <div class="step-description">Validate all input fields and requirements</div>
                        </div>
                    </div>
                    <div class="progress-step">
                        <div class="step-number" id="step2">2</div>
                        <div class="step-content">
                            <div class="step-title">Email Verification</div>
                            <div class="step-description">Check email format and availability</div>
                        </div>
                    </div>
                    <div class="progress-step">
                        <div class="step-number" id="step3">3</div>
                        <div class="step-content">
                            <div class="step-title">Account Creation</div>
                            <div class="step-description">Create user account in database</div>
                        </div>
                    </div>
                    <div class="progress-step">
                        <div class="step-number" id="step4">4</div>
                        <div class="step-content">
                            <div class="step-title">Email Confirmation</div>
                            <div class="step-description">Send verification email to user</div>
                        </div>
                    </div>
                </div>

                <!-- Response Viewer -->
                <div id="registrationResponse" class="response-viewer" style="display: none;"></div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Registration Testing Interface</p>
                    <p>User registration testing and validation</p>
                </div>
                <div class="footer-links">
                    <a href="../../../docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="../../../index.html">
                        <i class="fas fa-home"></i>
                        Main Hub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../../../assets/js/utils/api.js"></script>
    <script src="../../../assets/js/utils/auth.js"></script>
    <script src="../../../assets/js/utils/storage.js"></script>
    <script src="../../../assets/js/utils/notifications.js"></script>
    <script src="../../../assets/js/shared/components.js"></script>
    <script src="../../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let passwordStrength = 0;
        let emailValid = false;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Set up form handlers
            document.getElementById('registrationForm').addEventListener('submit', handleRegistration);
            document.getElementById('password').addEventListener('input', checkPasswordStrength);
            document.getElementById('email').addEventListener('input', validateEmail);
            document.getElementById('confirmPassword').addEventListener('input', checkPasswordMatch);
        }

        // Registration Functions
        async function handleRegistration(event) {
            event.preventDefault();

            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value,
                agreeTerms: document.getElementById('agreeTerms').checked,
                subscribeNewsletter: document.getElementById('subscribeNewsletter').checked
            };

            // Validate form
            if (!validateForm(formData)) {
                return;
            }

            updateRegistrationStatus('info', 'Creating Account...', 'Processing registration request');
            updateProgressStep(1, 'active');

            try {
                const response = await window.apiClient.register({
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    password: formData.password,
                    subscribeNewsletter: formData.subscribeNewsletter
                });

                if (response.success) {
                    updateProgressStep(1, 'completed');
                    updateProgressStep(2, 'completed');
                    updateProgressStep(3, 'completed');
                    updateProgressStep(4, 'active');

                    updateRegistrationStatus('success', 'Registration Successful',
                        'Account created successfully! Please check your email for verification.');

                    // Clear form
                    document.getElementById('registrationForm').reset();
                    resetPasswordStrength();
                } else {
                    updateRegistrationStatus('error', 'Registration Failed', response.error || 'Failed to create account');
                }

                showResponse('registrationResponse', response, response.success ? 'success' : 'error');
            } catch (error) {
                // Simulate registration behavior
                updateProgressStep(1, 'completed');
                updateProgressStep(2, 'active');

                const isDuplicateEmail = formData.email === '<EMAIL>';

                if (isDuplicateEmail) {
                    updateRegistrationStatus('error', 'Registration Failed', 'Email address already exists');
                    showResponse('registrationResponse', {
                        success: false,
                        error: 'Email address already exists',
                        details: 'This email is already registered'
                    }, 'error');
                } else {
                    updateProgressStep(2, 'completed');
                    updateProgressStep(3, 'completed');
                    updateProgressStep(4, 'completed');

                    updateRegistrationStatus('success', 'Registration Successful (Simulated)',
                        'Account created successfully! Please check your email for verification.');

                    showResponse('registrationResponse', {
                        success: true,
                        data: {
                            user: {
                                id: 'user_' + Date.now(),
                                email: formData.email,
                                firstName: formData.firstName,
                                lastName: formData.lastName
                            }
                        },
                        message: 'Mock registration successful (endpoint may not be available)'
                    }, 'warning');

                    // Clear form
                    document.getElementById('registrationForm').reset();
                    resetPasswordStrength();
                }
            }
        }

        function validateForm(formData) {
            // Check required fields
            if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
                updateRegistrationStatus('error', 'Validation Error', 'Please fill in all required fields');
                return false;
            }

            // Check email validity
            if (!emailValid) {
                updateRegistrationStatus('error', 'Validation Error', 'Please enter a valid email address');
                return false;
            }

            // Check password strength
            if (passwordStrength < 3) {
                updateRegistrationStatus('error', 'Validation Error', 'Password does not meet security requirements');
                return false;
            }

            // Check password match
            if (formData.password !== formData.confirmPassword) {
                updateRegistrationStatus('error', 'Validation Error', 'Passwords do not match');
                return false;
            }

            // Check terms agreement
            if (!formData.agreeTerms) {
                updateRegistrationStatus('error', 'Validation Error', 'You must agree to the Terms of Service');
                return false;
            }

            return true;
        }

        function updateRegistrationStatus(type, title, message) {
            const statusContainer = document.getElementById('registrationStatus');
            const iconClass = type === 'success' ? 'fas fa-check-circle' :
                             type === 'error' ? 'fas fa-times-circle' :
                             type === 'warning' ? 'fas fa-exclamation-triangle' :
                             'fas fa-info-circle';

            statusContainer.innerHTML = `
                <div class="status-icon ${type}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="status-title">${title}</div>
                <div class="status-message">${message}</div>
            `;
        }

        function updateProgressStep(stepNumber, status) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            stepElement.className = `step-number ${status}`;
        }

        // Password Validation Functions
        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            // Update requirement indicators
            updateRequirement('lengthReq', requirements.length);
            updateRequirement('uppercaseReq', requirements.uppercase);
            updateRequirement('lowercaseReq', requirements.lowercase);
            updateRequirement('numberReq', requirements.number);
            updateRequirement('specialReq', requirements.special);

            // Calculate strength
            const metRequirements = Object.values(requirements).filter(Boolean).length;
            passwordStrength = metRequirements;

            // Update strength bar
            const strengthFill = document.getElementById('strengthFill');
            const strengthLabel = document.getElementById('strengthLabel');

            let width, className, label;
            if (metRequirements <= 2) {
                width = '25%';
                className = 'weak';
                label = 'Weak';
            } else if (metRequirements <= 3) {
                width = '50%';
                className = 'medium';
                label = 'Medium';
            } else if (metRequirements <= 4) {
                width = '75%';
                className = 'medium';
                label = 'Good';
            } else {
                width = '100%';
                className = 'strong';
                label = 'Strong';
            }

            strengthFill.style.width = width;
            strengthFill.className = `strength-fill ${className}`;
            strengthLabel.textContent = label;
            strengthLabel.className = `strength-label ${className}`;
        }

        function updateRequirement(elementId, met) {
            const element = document.getElementById(elementId);
            const icon = element.querySelector('.requirement-icon');

            if (met) {
                element.className = 'requirement met';
                icon.className = 'fas fa-check requirement-icon';
            } else {
                element.className = 'requirement unmet';
                icon.className = 'fas fa-times requirement-icon';
            }
        }

        function resetPasswordStrength() {
            passwordStrength = 0;
            document.getElementById('strengthFill').style.width = '0%';
            document.getElementById('strengthLabel').textContent = 'Enter password to check strength';

            // Reset all requirements
            ['lengthReq', 'uppercaseReq', 'lowercaseReq', 'numberReq', 'specialReq'].forEach(id => {
                updateRequirement(id, false);
            });
        }

        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (confirmPassword && password !== confirmPassword) {
                document.getElementById('confirmPassword').style.borderColor = 'var(--error-color)';
            } else {
                document.getElementById('confirmPassword').style.borderColor = '';
            }
        }

        // Email Validation Functions
        function validateEmail() {
            const email = document.getElementById('email').value;
            const emailValidation = document.getElementById('emailValidation');

            if (!email) {
                emailValidation.textContent = '';
                emailValid = false;
                return;
            }

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            emailValid = emailRegex.test(email);

            if (emailValid) {
                emailValidation.textContent = '✓ Valid email format';
                emailValidation.className = 'email-validation valid';
            } else {
                emailValidation.textContent = '✗ Invalid email format';
                emailValidation.className = 'email-validation invalid';
            }
        }

        // Quick Test Functions
        function testValidRegistration() {
            fillValidData();
            window.notificationManager.info('Valid registration data filled. Click Create Account to test.');
        }

        function testInvalidRegistration() {
            fillInvalidEmail();
            fillWeakPassword();
            window.notificationManager.info('Invalid registration data filled. Click Create Account to test validation.');
        }

        function testDuplicateEmail() {
            document.getElementById('email').value = '<EMAIL>';
            fillValidData();
            document.getElementById('email').value = '<EMAIL>'; // Override email
            window.notificationManager.info('Duplicate email filled. This should trigger an error.');
        }

        function testPasswordValidation() {
            document.getElementById('password').value = '123';
            checkPasswordStrength();
            window.notificationManager.info('Weak password filled. Check password requirements.');
        }

        // Form Helper Functions
        function fillValidData() {
            document.getElementById('firstName').value = 'John';
            document.getElementById('lastName').value = 'Doe';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'SecurePass123!';
            document.getElementById('confirmPassword').value = 'SecurePass123!';
            document.getElementById('agreeTerms').checked = true;

            validateEmail();
            checkPasswordStrength();
            checkPasswordMatch();
        }

        function fillWeakPassword() {
            document.getElementById('password').value = '123';
            document.getElementById('confirmPassword').value = '123';
            checkPasswordStrength();
            checkPasswordMatch();
        }

        function fillInvalidEmail() {
            document.getElementById('email').value = 'invalid-email';
            validateEmail();
        }

        function fillMismatchedPasswords() {
            document.getElementById('password').value = 'Password123!';
            document.getElementById('confirmPassword').value = 'DifferentPass456!';
            checkPasswordStrength();
            checkPasswordMatch();
        }

        function fillSQLInjection() {
            document.getElementById('firstName').value = "'; DROP TABLE users; --";
            document.getElementById('lastName').value = "'; DELETE FROM accounts; --";
            document.getElementById('email').value = "<EMAIL>'; DROP TABLE users; --";
            window.notificationManager.warning('SQL injection test data filled. This should be safely handled.');
        }

        function fillXSSAttack() {
            document.getElementById('firstName').value = '<script>alert("XSS")</script>';
            document.getElementById('lastName').value = '<img src="x" onerror="alert(\'XSS\')">';
            document.getElementById('email').value = '<EMAIL>';
            window.notificationManager.warning('XSS attack test data filled. This should be safely handled.');
        }

        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + 'ToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Utility Functions
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function showResponse(elementId, response, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response-viewer response-${type}`;
            element.innerHTML = `<pre>${JSON.stringify(response, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
