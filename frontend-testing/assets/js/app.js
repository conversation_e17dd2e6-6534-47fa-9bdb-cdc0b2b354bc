document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    attachEventListeners();
});

async function initializeApp() {
    // Check server health
    await checkServerHealth();

    // Check authentication status
    updateAuthStatus();

    // Initialize theme
    initializeTheme();

    // Load recent activity
    loadRecentActivity();
}

function attachEventListeners() {
    // Quick Actions
    document.getElementById('quickLoginBtn')?.addEventListener('click', quickLogin);
    document.getElementById('checkServerHealthBtn')?.addEventListener('click', checkServerHealth);
    document.getElementById('viewApiDocsBtn')?.addEventListener('click', viewApiDocs);

    // Feature Navigation Buttons
    document.querySelectorAll('.navigate-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const feature = event.currentTarget.dataset.feature;
            if (feature) {
                navigateToFeature(feature);
            }
        });
    });

    // Monitoring Dashboard Button
    document.getElementById('navigateToMonitoringBtn')?.addEventListener('click', navigateToMonitoring);

    // Theme Toggle Button (already handled in initializeTheme, but adding here for consistency)
    document.getElementById('themeToggle')?.addEventListener('click', toggleTheme);
}

function navigateToFeature(feature) {
    window.location.href = `features/${feature}/index.html`;
}

function navigateToMonitoring() {
    window.location.href = 'monitoring-dashboard/index.html';
}

function quickLogin() {
    window.location.href = 'features/authentication/login/index.html';
}

function viewApiDocs() {
    window.open('http://localhost:3000/api-docs', '_blank');
}

async function checkServerHealth() {
    try {
        const response = await fetch('http://localhost:3000/health');
        const data = await response.json();

        const statusElement = document.getElementById('serverStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        if (response.ok) {
            indicator.className = 'fas fa-circle status-indicator status-online';
            text.textContent = 'Server Online';
            addActivityLog('Server health check successful', 'success');
        } else {
            throw new Error('Server responded with error');
        }
    } catch (error) {
        const statusElement = document.getElementById('serverStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        indicator.className = 'fas fa-circle status-indicator status-offline';
        text.textContent = 'Server Offline';
        addActivityLog('Server health check failed', 'error');
    }
}

function updateAuthStatus() {
    const token = localStorage.getItem('auth_token');
    const user = JSON.parse(localStorage.getItem('user') || 'null');

    const authElement = document.getElementById('authStatus');
    const text = authElement.querySelector('.auth-text');

    if (token && user) {
        text.textContent = `Logged in as ${user.email}`;
        authElement.classList.add('authenticated');
    } else {
        text.textContent = 'Not Authenticated';
        authElement.classList.remove('authenticated');
    }
}

function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.body.setAttribute('data-theme', savedTheme);

    const themeToggle = document.getElementById('themeToggle');
    const icon = themeToggle.querySelector('i');

    if (savedTheme === 'dark') {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }

    themeToggle.addEventListener('click', toggleTheme);
}

function toggleTheme() {
    const currentTheme = document.body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    const icon = document.getElementById('themeToggle').querySelector('i');
    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

function loadRecentActivity() {
    const savedActivity = JSON.parse(localStorage.getItem('recentActivity') || '[]');
    const activityLog = document.getElementById('activityLog');

    // Clear existing content except welcome message
    const welcomeMessage = activityLog.querySelector('.activity-item');
    activityLog.innerHTML = '';
    activityLog.appendChild(welcomeMessage);

    // Add saved activities
    savedActivity.slice(-5).forEach(activity => {
        addActivityLogElement(activity.message, activity.type, activity.timestamp);
    });
}

function addActivityLog(message, type = 'info') {
    const activity = {
        message,
        type,
        timestamp: new Date().toISOString()
    };

    // Save to localStorage
    const savedActivity = JSON.parse(localStorage.getItem('recentActivity') || '[]');
    savedActivity.push(activity);

    // Keep only last 50 activities
    if (savedActivity.length > 50) {
        savedActivity.shift();
    }

    localStorage.setItem('recentActivity', JSON.stringify(savedActivity));

    // Add to UI
    addActivityLogElement(message, type);
}

function addActivityLogElement(message, type, timestamp) {
    const activityLog = document.getElementById('activityLog');
    const activityItem = document.createElement('div');
    activityItem.className = `activity-item activity-${type}`;

    const icon = type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    const timeText = timestamp ?
        new Date(timestamp).toLocaleTimeString() :
        'Just now';

    activityItem.innerHTML = `
        <i class="fas ${icon}"></i>
        <span class="activity-text">${message}</span>
        <span class="activity-time">${timeText}</span>
    `;

    // Insert at the beginning (after welcome message if it exists)
    const firstChild = activityLog.firstChild;
    if (firstChild && firstChild.textContent.includes('Welcome!')) {
        activityLog.insertBefore(activityItem, firstChild.nextSibling);
    } else {
        activityLog.insertBefore(activityItem, firstChild);
    }
}
